﻿namespace MoonLight.Core.Assets.Data
{
    public class ResourceToken
    {
        #region Basic

        #region Geometry

        public const string CalendarGeometry = nameof(CalendarGeometry);

        public const string DeleteGeometry = nameof(DeleteGeometry);

        public const string DeleteFillCircleGeometry = nameof(DeleteFillCircleGeometry);

        public const string CloseGeometry = nameof(CloseGeometry);

        public const string UpGeometry = nameof(UpGeometry);

        public const string DownGeometry = nameof(DownGeometry);

        public const string ClockGeometry = nameof(ClockGeometry);

        public const string LeftGeometry = nameof(LeftGeometry);

        public const string RightGeometry = nameof(RightGeometry);

        public const string RotateLeftGeometry = nameof(RotateLeftGeometry);

        public const string EnlargeGeometry = nameof(EnlargeGeometry);

        public const string ReduceGeometry = nameof(ReduceGeometry);

        public const string DownloadGeometry = nameof(DownloadGeometry);

        public const string SaveGeometry = nameof(SaveGeometry);

        public const string WindowsGeometry = nameof(WindowsGeometry);

        public const string FullScreenGeometry = nameof(FullScreenGeometry);

        public const string FullScreenReturnGeometry = nameof(FullScreenReturnGeometry);

        public const string SearchGeometry = nameof(SearchGeometry);

        public const string UpDownGeometry = nameof(UpDownGeometry);

        public const string WindowMinGeometry = nameof(WindowMinGeometry);

        public const string CheckedGeometry = nameof(CheckedGeometry);

        public const string EyeOpenGeometry = nameof(EyeOpenGeometry);

        public const string EyeCloseGeometry = nameof(EyeCloseGeometry);

        public const string WindowRestoreGeometry = nameof(WindowRestoreGeometry);

        public const string WindowMaxGeometry = nameof(WindowMaxGeometry);

        public const string AudioGeometry = nameof(AudioGeometry);

        public const string BubbleTailGeometry = nameof(BubbleTailGeometry);

        public const string StarGeometry = nameof(StarGeometry);

        public const string AddGeometry = nameof(AddGeometry);

        public const string SubGeometry = nameof(SubGeometry);

        public const string AllGeometry = nameof(AllGeometry);

        public const string DragGeometry = nameof(DragGeometry);

        public const string DropperGeometry = nameof(DropperGeometry);

        public const string SuccessGeometry = nameof(SuccessGeometry);

        public const string InfoGeometry = nameof(InfoGeometry);

        public const string ErrorGeometry = nameof(ErrorGeometry);

        public const string WarningGeometry = nameof(WarningGeometry);

        public const string AskGeometry = nameof(AskGeometry);

        public const string FatalGeometry = nameof(FatalGeometry);

        #endregion

        #region Brush

        public const string PrimaryBrush = nameof(PrimaryBrush);

        public const string DarkPrimaryBrush = nameof(DarkPrimaryBrush);

        public const string SuccessBrush = nameof(SuccessBrush);

        public const string DarkSuccessBrush = nameof(DarkSuccessBrush);

        public const string InfoBrush = nameof(InfoBrush);

        public const string DarkInfoBrush = nameof(DarkInfoBrush);

        public const string DangerBrush = nameof(DangerBrush);

        public const string DarkDangerBrush = nameof(DarkDangerBrush);

        public const string WarningBrush = nameof(WarningBrush);

        public const string DarkWarningBrush = nameof(DarkWarningBrush);

        public const string AccentBrush = nameof(AccentBrush);

        public const string DarkAccentBrush = nameof(DarkAccentBrush);

        public const string PrimaryTextBrush = nameof(PrimaryTextBrush);

        public const string SecondaryTextBrush = nameof(SecondaryTextBrush);

        public const string ThirdlyTextBrush = nameof(ThirdlyTextBrush);

        public const string ReverseTextBrush = nameof(ReverseTextBrush);

        public const string TextIconBrush = nameof(TextIconBrush);

        public const string BorderBrush = nameof(BorderBrush);

        public const string SecondaryBorderBrush = nameof(SecondaryBorderBrush);

        public const string BackgroundBrush = nameof(BackgroundBrush);

        public const string RegionBrush = nameof(RegionBrush);

        public const string SecondaryRegionBrush = nameof(SecondaryRegionBrush);

        public const string ThirdlyRegionBrush = nameof(ThirdlyRegionBrush);

        public const string TitleBrush = nameof(TitleBrush);

        public const string DefaultBrush = nameof(DefaultBrush);

        public const string DarkDefaultBrush = nameof(DarkDefaultBrush);

        public const string DarkMaskBrush = nameof(DarkMaskBrush);

        public const string DarkOpacityBrush = nameof(DarkOpacityBrush);

        #endregion

        #region Converter

        public const string Boolean2BooleanReConverter = nameof(Boolean2BooleanReConverter);

        public const string Boolean2VisibilityReConverter = nameof(Boolean2VisibilityReConverter);

        public const string BooleanArr2VisibilityConverter = nameof(BooleanArr2VisibilityConverter);

        public const string Long2FileSizeConverter = nameof(Long2FileSizeConverter);

        public const string String2VisibilityConverter = nameof(String2VisibilityConverter);

        public const string String2VisibilityReConverter = nameof(String2VisibilityReConverter);

        public const string Boolean2VisibilityConverter = nameof(Boolean2VisibilityConverter);

        public const string TreeViewItemMarginConverter = nameof(TreeViewItemMarginConverter);

        public const string Color2HexStringConverter = nameof(Color2HexStringConverter);

        public const string Object2BooleanConverter = nameof(Object2BooleanConverter);

        public const string Boolean2StringConverter = nameof(Boolean2StringConverter);

        public const string Int2StringConverter = nameof(Int2StringConverter);

        public const string BorderClipConverter = nameof(BorderClipConverter);

        public const string BorderCircularClipConverter = nameof(BorderCircularClipConverter);

        public const string BorderCircularConverter = nameof(BorderCircularConverter);

        public const string Object2VisibilityConverter = nameof(Object2VisibilityConverter);

        public const string Number2PercentageConverter = nameof(Number2PercentageConverter);

        public const string RectangleCircularConverter = nameof(RectangleCircularConverter);

        public const string ThicknessSplitConverter = nameof(ThicknessSplitConverter);

        public const string CornerRadiusSplitConverter = nameof(CornerRadiusSplitConverter);

        public const string MenuScrollingVisibilityConverter = nameof(MenuScrollingVisibilityConverter);

        public const string Double2GridLengthConverter = nameof(Double2GridLengthConverter);

        public const string DoubleMinConverter = nameof(DoubleMinConverter);

        #endregion

        #region Effect

        public const string EffectShadowColor = nameof(EffectShadowColor);

        public const string EffectShadow1 = nameof(EffectShadow1);

        public const string EffectShadow2 = nameof(EffectShadow2);

        public const string EffectShadow3 = nameof(EffectShadow3);

        public const string EffectShadow4 = nameof(EffectShadow4);

        public const string EffectShadow5 = nameof(EffectShadow5);

        #endregion

        #region Color

        public const string PrimaryColor = nameof(PrimaryColor);

        public const string DarkPrimaryColor = nameof(DarkPrimaryColor);

        public const string DangerColor = nameof(DangerColor);

        public const string DarkDangerColor = nameof(DarkDangerColor);

        public const string WarningColor = nameof(WarningColor);

        public const string DarkWarningColor = nameof(DarkWarningColor);

        public const string InfoColor = nameof(InfoColor);

        public const string DarkInfoColor = nameof(DarkInfoColor);

        public const string SuccessColor = nameof(SuccessColor);

        public const string DarkSuccessColor = nameof(DarkSuccessColor);

        public const string PrimaryTextColor = nameof(PrimaryTextColor);

        public const string SecondaryTextColor = nameof(SecondaryTextColor);

        public const string ThirdlyTextColor = nameof(ThirdlyTextColor);

        public const string ReverseTextColor = nameof(ReverseTextColor);

        public const string TextIconColor = nameof(TextIconColor);

        public const string BorderColor = nameof(BorderColor);

        public const string SecondaryBorderColor = nameof(SecondaryBorderColor);

        public const string BackgroundColor = nameof(BackgroundColor);

        public const string RegionColor = nameof(RegionColor);

        public const string SecondaryRegionColor = nameof(SecondaryRegionColor);

        public const string ThirdlyRegionColor = nameof(ThirdlyRegionColor);

        public const string TitleColor = nameof(TitleColor);

        public const string SecondaryTitleColor = nameof(SecondaryTitleColor);

        public const string DefaultColor = nameof(DefaultColor);

        public const string DarkDefaultColor = nameof(DarkDefaultColor);

        public const string AccentColor = nameof(AccentColor);

        public const string DarkAccentColor = nameof(DarkAccentColor);

        public const string DarkMaskColor = nameof(DarkMaskColor);

        public const string DarkOpacityColor = nameof(DarkOpacityColor);

        #endregion

        #region Behavior

        public const string BehaviorXY200 = nameof(BehaviorXY200);

        public const string BehaviorX200 = nameof(BehaviorX200);

        public const string BehaviorY200 = nameof(BehaviorY200);

        public const string BehaviorXY400 = nameof(BehaviorXY400);

        public const string BehaviorX400 = nameof(BehaviorX400);

        public const string BehaviorY400 = nameof(BehaviorY400);

        #endregion

        #endregion

        #region Internal

        internal const string BlurGradientValue = nameof(BlurGradientValue);

        internal const string ButtonCustom = nameof(ButtonCustom);

        internal const string PaginationButtonStyle = nameof(PaginationButtonStyle);

        internal const string WindowWin10 = nameof(WindowWin10);

        internal const string WindowBlur = nameof(WindowBlur);

        internal const string WindowGlow = nameof(WindowGlow);

        internal const string Window4ScreenshotStyle = nameof(Window4ScreenshotStyle);

        internal const string AddTagButtonStyle = nameof(AddTagButtonStyle);

        internal const string RadioGroupItemDefault = nameof(RadioGroupItemDefault);

        internal const string RadioGroupItemSingle = nameof(RadioGroupItemSingle);

        internal const string RadioGroupItemHorizontalFirst = nameof(RadioGroupItemHorizontalFirst);

        internal const string RadioGroupItemHorizontalLast = nameof(RadioGroupItemHorizontalLast);

        internal const string RadioGroupItemVerticalFirst = nameof(RadioGroupItemVerticalFirst);

        internal const string RadioGroupItemVerticalLast = nameof(RadioGroupItemVerticalLast);

        internal const string ButtonGroupItemDefault = nameof(ButtonGroupItemDefault);

        internal const string ButtonGroupItemSingle = nameof(ButtonGroupItemSingle);

        internal const string ButtonGroupItemHorizontalFirst = nameof(ButtonGroupItemHorizontalFirst);

        internal const string ButtonGroupItemHorizontalLast = nameof(ButtonGroupItemHorizontalLast);

        internal const string ButtonGroupItemVerticalFirst = nameof(ButtonGroupItemVerticalFirst);

        internal const string ButtonGroupItemVerticalLast = nameof(ButtonGroupItemVerticalLast);

        internal const string ToggleButtonGroupItemDefault = nameof(ToggleButtonGroupItemDefault);

        internal const string ToggleButtonGroupItemSingle = nameof(ToggleButtonGroupItemSingle);

        internal const string ToggleButtonGroupItemHorizontalFirst = nameof(ToggleButtonGroupItemHorizontalFirst);

        internal const string ToggleButtonGroupItemHorizontalLast = nameof(ToggleButtonGroupItemHorizontalLast);

        internal const string ToggleButtonGroupItemVerticalFirst = nameof(ToggleButtonGroupItemVerticalFirst);

        internal const string ToggleButtonGroupItemVerticalLast = nameof(ToggleButtonGroupItemVerticalLast);

        internal const string TabItemCapsuleDefault = nameof(TabItemCapsuleDefault);

        internal const string TabItemCapsuleSingle = nameof(TabItemCapsuleSingle);

        internal const string TabItemCapsuleHorizontalFirst = nameof(TabItemCapsuleHorizontalFirst);

        internal const string TabItemCapsuleHorizontalLast = nameof(TabItemCapsuleHorizontalLast);

        internal const string TabItemCapsuleVerticalFirst = nameof(TabItemCapsuleVerticalFirst);

        internal const string TabItemCapsuleVerticalLast = nameof(TabItemCapsuleVerticalLast);

        internal const string ComboBoxItemCapsuleDefault = nameof(ComboBoxItemCapsuleDefault);

        internal const string ComboBoxItemCapsuleSingle = nameof(ComboBoxItemCapsuleSingle);

        internal const string ComboBoxItemCapsuleHorizontalFirst = nameof(ComboBoxItemCapsuleHorizontalFirst);

        internal const string ComboBoxItemCapsuleHorizontalLast = nameof(ComboBoxItemCapsuleHorizontalLast);

        internal const string ComboBoxItemCapsuleVerticalFirst = nameof(ComboBoxItemCapsuleVerticalFirst);

        internal const string ComboBoxItemCapsuleVerticalLast = nameof(ComboBoxItemCapsuleVerticalLast);

        #endregion
    }
}
