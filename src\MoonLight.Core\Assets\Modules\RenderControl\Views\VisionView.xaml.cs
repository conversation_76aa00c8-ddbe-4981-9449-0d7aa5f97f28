﻿using MoonLight.Core.Config;
using MoonLight.Core.Enums;
using MoonLight.Modules.RenderControl.Managers;
using RenderControl.Views;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms.Integration;
using System.Windows.Forms;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Services;
using MahApps.Metro.Controls;
using MoonLight.Core.Assets.Modules.RenderControl.Views;
using System;
using System.Windows.Threading;

namespace MoonLight.Modules.RenderControl.Views
{
    /// <summary>
    /// VisionView.xaml 的交互逻辑
    /// </summary>
    public partial class VisionView : System.Windows.Controls.UserControl, IRenderViewGroupEx
    {
        public VisionView()
        {
            InitializeComponent();
            //for (int i = 1; i <= 9; i++)
            //{
            //    GetImageBox(i);
            //}
            ViewMode =  Solution.Ins.ViewMode;

        }

        #region Prop
        private ViewMode _ViewMode = ViewMode.One;
        public ViewMode ViewMode
        {
            get { return _ViewMode; }
            set
            {
                _ViewMode = value;
                ShowCanvasAll();
            }
        }

        #endregion
        #region Method
        public RenderViewWpf GetImageBox(int key)
        {
            (RenderViewManager.ViewList[key] as RenderViewWpf).View.father = this;
            return RenderViewManager.ViewList[key];
            //if (!RenderViewManager.mViewDic.ContainsKey(key))
            //{
            //    RenderView mWindowH = (RenderView)IoC.Get<IRenderViewManager>().GenRenderView();
            //    mWindowH.BackgroundImageLayout = ImageLayout.Center;
            //    RenderViewManager.mViewDic.Add(key, mWindowH);
            //}
            //return RenderViewManager.mViewDic[key];
        }

        private void ShowCanvasAll()
        {
         
            RowDefinition row1 = new RowDefinition();
            RowDefinition row2 = new RowDefinition();
            RowDefinition row3 = new RowDefinition();
            ColumnDefinition col1 = new ColumnDefinition();
            ColumnDefinition col2 = new ColumnDefinition();
            ColumnDefinition col3 = new ColumnDefinition();
            ColumnDefinition col4 = new ColumnDefinition();
            grid.Children.Clear();
            grid.RowDefinitions.Clear();
            grid.ColumnDefinitions.Clear();

            this.Invoke(() =>
            {

                switch (_ViewMode)
                {
                    case ViewMode.One:
                        grid.Children.Add(GetImageBox(0));

                        break;
                    case ViewMode.Two:
                        grid.ColumnDefinitions.Add(col1);
                        grid.ColumnDefinitions.Add(col2);
                        grid.Children.Add(GetImageBox(0));
                        Grid.SetRow(GetImageBox(0), 0);
                        Grid.SetColumn(GetImageBox(0), 0);


                        grid.Children.Add(GetImageBox(1));
                        Grid.SetRow(GetImageBox(1), 0);
                        Grid.SetColumn(GetImageBox(1), 1);

                        break;
                    case ViewMode.Three:
                        grid.ColumnDefinitions.Add(col1);
                        grid.ColumnDefinitions.Add(col2);
                        grid.RowDefinitions.Add(row1);
                        grid.RowDefinitions.Add(row2);

                        grid.Children.Add(GetImageBox(0));
                        Grid.SetRow(GetImageBox(0), 0);
                        Grid.SetColumn(GetImageBox(0), 0);
                        Grid.SetRowSpan(GetImageBox(0), 2);


                        grid.Children.Add(GetImageBox(1));
                        Grid.SetRow(GetImageBox(1), 0);
                        Grid.SetColumn(GetImageBox(1), 1);


                        grid.Children.Add(GetImageBox(2));
                        Grid.SetRow(GetImageBox(2), 1);
                        Grid.SetColumn(GetImageBox(2), 1);

                        break;
                    //case ViewMode.Four:
                    //    grid.ColumnDefinitions.Add(col1);
                    //    grid.ColumnDefinitions.Add(col2);
                    //    grid.RowDefinitions.Add(row1);
                    //    grid.RowDefinitions.Add(row2);
                    //    windowsFormsHost[0].Margin = new Thickness(0);
                    //    windowsFormsHost[0].Child = GetImageBox(0);
                    //    grid.Children.Add(windowsFormsHost[0]);
                    //    Grid.SetRow(windowsFormsHost[0], 0);
                    //    Grid.SetColumn(windowsFormsHost[0], 0);

                    //    windowsFormsHost[1].Margin = new Thickness(0);
                    //    windowsFormsHost[1].Child = GetImageBox(1);
                    //    grid.Children.Add(windowsFormsHost[1]);
                    //    Grid.SetRow(windowsFormsHost[1], 0);
                    //    Grid.SetColumn(windowsFormsHost[1], 1);

                    //    windowsFormsHost[2].Margin = new Thickness(0);
                    //    windowsFormsHost[2].Child = GetImageBox(2);
                    //    grid.Children.Add(windowsFormsHost[2]);
                    //    Grid.SetRow(windowsFormsHost[2], 1);
                    //    Grid.SetColumn(windowsFormsHost[2], 0);

                    //    windowsFormsHost[3].Margin = new Thickness(0);
                    //    windowsFormsHost[3].Child = GetImageBox(3);
                    //    grid.Children.Add(windowsFormsHost[3]);
                    //    Grid.SetRow(windowsFormsHost[3], 1);
                    //    Grid.SetColumn(windowsFormsHost[3], 1);

                    //    break;
                    //case ViewMode.Five:
                    //    grid.ColumnDefinitions.Add(col1);
                    //    grid.ColumnDefinitions.Add(col2);
                    //    grid.ColumnDefinitions.Add(col3);
                    //    grid.RowDefinitions.Add(row1);
                    //    grid.RowDefinitions.Add(row2);
                    //    windowsFormsHost[0].Margin = new Thickness(0);
                    //    windowsFormsHost[0].Child = GetImageBox(0);
                    //    grid.Children.Add(windowsFormsHost[0]);
                    //    Grid.SetRow(windowsFormsHost[0], 0);
                    //    Grid.SetColumn(windowsFormsHost[0], 0);
                    //    Grid.SetColumnSpan(windowsFormsHost[0], 2);

                    //    windowsFormsHost[1].Margin = new Thickness(0);
                    //    windowsFormsHost[1].Child = GetImageBox(1);
                    //    grid.Children.Add(windowsFormsHost[1]);
                    //    Grid.SetRow(windowsFormsHost[1], 0);
                    //    Grid.SetColumn(windowsFormsHost[1], 2);

                    //    windowsFormsHost[2].Margin = new Thickness(0);
                    //    windowsFormsHost[2].Child = GetImageBox(2);
                    //    grid.Children.Add(windowsFormsHost[2]);
                    //    Grid.SetRow(windowsFormsHost[2], 1);
                    //    Grid.SetColumn(windowsFormsHost[2], 0);

                    //    windowsFormsHost[3].Margin = new Thickness(0);
                    //    windowsFormsHost[3].Child = GetImageBox(3);
                    //    grid.Children.Add(windowsFormsHost[3]);
                    //    Grid.SetRow(windowsFormsHost[3], 1);
                    //    Grid.SetColumn(windowsFormsHost[3], 1);

                    //    windowsFormsHost[4].Margin = new Thickness(0);
                    //    windowsFormsHost[4].Child = GetImageBox(4);
                    //    grid.Children.Add(windowsFormsHost[4]);
                    //    Grid.SetRow(windowsFormsHost[4], 1);
                    //    Grid.SetColumn(windowsFormsHost[4], 2);
                    //    break;
                    //case ViewMode.Six:
                    //    grid.ColumnDefinitions.Add(col1);
                    //    grid.ColumnDefinitions.Add(col2);
                    //    grid.ColumnDefinitions.Add(col3);
                    //    grid.RowDefinitions.Add(row1);
                    //    grid.RowDefinitions.Add(row2);
                    //    windowsFormsHost[0].Margin = new Thickness(0);
                    //    windowsFormsHost[0].Child = GetImageBox(0);
                    //    grid.Children.Add(windowsFormsHost[0]);
                    //    Grid.SetRow(windowsFormsHost[0], 0);
                    //    Grid.SetColumn(windowsFormsHost[0], 0);

                    //    windowsFormsHost[1].Margin = new Thickness(0);
                    //    windowsFormsHost[1].Child = GetImageBox(1);
                    //    grid.Children.Add(windowsFormsHost[1]);
                    //    Grid.SetRow(windowsFormsHost[1], 0);
                    //    Grid.SetColumn(windowsFormsHost[1], 1);

                    //    windowsFormsHost[2].Margin = new Thickness(0);
                    //    windowsFormsHost[2].Child = GetImageBox(2);
                    //    grid.Children.Add(windowsFormsHost[2]);
                    //    Grid.SetRow(windowsFormsHost[2], 0);
                    //    Grid.SetColumn(windowsFormsHost[2], 2);

                    //    windowsFormsHost[3].Margin = new Thickness(0);
                    //    windowsFormsHost[3].Child = GetImageBox(3);
                    //    grid.Children.Add(windowsFormsHost[3]);
                    //    Grid.SetRow(windowsFormsHost[3], 1);
                    //    Grid.SetColumn(windowsFormsHost[3], 0);

                    //    windowsFormsHost[4].Margin = new Thickness(0);
                    //    windowsFormsHost[4].Child = GetImageBox(4);
                    //    grid.Children.Add(windowsFormsHost[4]);
                    //    Grid.SetRow(windowsFormsHost[4], 1);
                    //    Grid.SetColumn(windowsFormsHost[4], 1);

                    //    windowsFormsHost[5].Margin = new Thickness(0);
                    //    windowsFormsHost[5].Child = GetImageBox(5);
                    //    grid.Children.Add(windowsFormsHost[5]);
                    //    Grid.SetRow(windowsFormsHost[5], 1);
                    //    Grid.SetColumn(windowsFormsHost[5], 2);
                    //    break;
                    //case ViewMode.Seven:
                    //    grid.ColumnDefinitions.Add(col1);
                    //    grid.ColumnDefinitions.Add(col2);
                    //    grid.ColumnDefinitions.Add(col3);
                    //    grid.ColumnDefinitions.Add(col4);
                    //    grid.RowDefinitions.Add(row1);
                    //    grid.RowDefinitions.Add(row2);
                    //    windowsFormsHost[0].Margin = new Thickness(0);
                    //    windowsFormsHost[0].Child = GetImageBox(0);
                    //    grid.Children.Add(windowsFormsHost[0]);
                    //    Grid.SetRow(windowsFormsHost[0], 0);
                    //    Grid.SetColumn(windowsFormsHost[0], 0);
                    //    Grid.SetColumnSpan(windowsFormsHost[0], 2);

                    //    windowsFormsHost[1].Margin = new Thickness(0);
                    //    windowsFormsHost[1].Child = GetImageBox(1);
                    //    grid.Children.Add(windowsFormsHost[1]);
                    //    Grid.SetRow(windowsFormsHost[1], 0);
                    //    Grid.SetColumn(windowsFormsHost[1], 2);

                    //    windowsFormsHost[2].Margin = new Thickness(0);
                    //    windowsFormsHost[2].Child = GetImageBox(2);
                    //    grid.Children.Add(windowsFormsHost[2]);
                    //    Grid.SetRow(windowsFormsHost[2], 0);
                    //    Grid.SetColumn(windowsFormsHost[2], 3);

                    //    windowsFormsHost[3].Margin = new Thickness(0);
                    //    windowsFormsHost[3].Child = GetImageBox(3);
                    //    grid.Children.Add(windowsFormsHost[3]);
                    //    Grid.SetRow(windowsFormsHost[3], 1);
                    //    Grid.SetColumn(windowsFormsHost[3], 0);

                    //    windowsFormsHost[4].Margin = new Thickness(0);
                    //    windowsFormsHost[4].Child = GetImageBox(4);
                    //    grid.Children.Add(windowsFormsHost[4]);
                    //    Grid.SetRow(windowsFormsHost[4], 1);
                    //    Grid.SetColumn(windowsFormsHost[4], 1);

                    //    windowsFormsHost[5].Margin = new Thickness(0);
                    //    windowsFormsHost[5].Child = GetImageBox(5);
                    //    grid.Children.Add(windowsFormsHost[5]);
                    //    Grid.SetRow(windowsFormsHost[5], 1);
                    //    Grid.SetColumn(windowsFormsHost[5], 2);

                    //    windowsFormsHost[6].Margin = new Thickness(0);
                    //    windowsFormsHost[6].Child = GetImageBox(6);
                    //    grid.Children.Add(windowsFormsHost[6]);
                    //    Grid.SetRow(windowsFormsHost[6], 1);
                    //    Grid.SetColumn(windowsFormsHost[6], 3);
                    //    break;
                    //case ViewMode.Eight:
                    //    grid.ColumnDefinitions.Add(col1);
                    //    grid.ColumnDefinitions.Add(col2);
                    //    grid.ColumnDefinitions.Add(col3);
                    //    grid.ColumnDefinitions.Add(col4);
                    //    grid.RowDefinitions.Add(row1);
                    //    grid.RowDefinitions.Add(row2);
                    //    windowsFormsHost[0].Margin = new Thickness(0);
                    //    windowsFormsHost[0].Child = GetImageBox(0);
                    //    grid.Children.Add(windowsFormsHost[0]);
                    //    Grid.SetRow(windowsFormsHost[0], 0);
                    //    Grid.SetColumn(windowsFormsHost[0], 0);

                    //    windowsFormsHost[1].Margin = new Thickness(0);
                    //    windowsFormsHost[1].Child = GetImageBox(1);
                    //    grid.Children.Add(windowsFormsHost[1]);
                    //    Grid.SetRow(windowsFormsHost[1], 0);
                    //    Grid.SetColumn(windowsFormsHost[1], 1);

                    //    windowsFormsHost[2].Margin = new Thickness(0);
                    //    windowsFormsHost[2].Child = GetImageBox(2);
                    //    grid.Children.Add(windowsFormsHost[2]);
                    //    Grid.SetRow(windowsFormsHost[2], 0);
                    //    Grid.SetColumn(windowsFormsHost[2], 2);

                    //    windowsFormsHost[3].Margin = new Thickness(0);
                    //    windowsFormsHost[3].Child = GetImageBox(3);
                    //    grid.Children.Add(windowsFormsHost[3]);
                    //    Grid.SetRow(windowsFormsHost[3], 0);
                    //    Grid.SetColumn(windowsFormsHost[3], 3);

                    //    windowsFormsHost[4].Margin = new Thickness(0);
                    //    windowsFormsHost[4].Child = GetImageBox(4);
                    //    grid.Children.Add(windowsFormsHost[4]);
                    //    Grid.SetRow(windowsFormsHost[4], 1);
                    //    Grid.SetColumn(windowsFormsHost[4], 0);

                    //    windowsFormsHost[5].Margin = new Thickness(0);
                    //    windowsFormsHost[5].Child = GetImageBox(5);
                    //    grid.Children.Add(windowsFormsHost[5]);
                    //    Grid.SetRow(windowsFormsHost[5], 1);
                    //    Grid.SetColumn(windowsFormsHost[5], 1);

                    //    windowsFormsHost[6].Margin = new Thickness(0);
                    //    windowsFormsHost[6].Child = GetImageBox(6);
                    //    grid.Children.Add(windowsFormsHost[6]);
                    //    Grid.SetRow(windowsFormsHost[6], 1);
                    //    Grid.SetColumn(windowsFormsHost[6], 2);

                    //    windowsFormsHost[7].Margin = new Thickness(0);
                    //    windowsFormsHost[7].Child = GetImageBox(7);
                    //    grid.Children.Add(windowsFormsHost[7]);
                    //    Grid.SetRow(windowsFormsHost[7], 1);
                    //    Grid.SetColumn(windowsFormsHost[7], 3);
                    //    break;
                    //case ViewMode.Night:
                    //    grid.ColumnDefinitions.Add(col1);
                    //    grid.ColumnDefinitions.Add(col2);
                    //    grid.ColumnDefinitions.Add(col3);
                    //    grid.RowDefinitions.Add(row1);
                    //    grid.RowDefinitions.Add(row2);
                    //    grid.RowDefinitions.Add(row3);
                    //    windowsFormsHost[0].Margin = new Thickness(0);
                    //    windowsFormsHost[0].Child = GetImageBox(0);
                    //    grid.Children.Add(windowsFormsHost[0]);
                    //    Grid.SetRow(windowsFormsHost[0], 0);
                    //    Grid.SetColumn(windowsFormsHost[0], 0);

                    //    windowsFormsHost[1].Margin = new Thickness(0);
                    //    windowsFormsHost[1].Child = GetImageBox(1);
                    //    grid.Children.Add(windowsFormsHost[1]);
                    //    Grid.SetRow(windowsFormsHost[1], 0);
                    //    Grid.SetColumn(windowsFormsHost[1], 1);

                    //    windowsFormsHost[2].Margin = new Thickness(0);
                    //    windowsFormsHost[2].Child = GetImageBox(2);
                    //    grid.Children.Add(windowsFormsHost[2]);
                    //    Grid.SetRow(windowsFormsHost[2], 0);
                    //    Grid.SetColumn(windowsFormsHost[2], 2);

                    //    windowsFormsHost[3].Margin = new Thickness(0);
                    //    windowsFormsHost[3].Child = GetImageBox(3);
                    //    grid.Children.Add(windowsFormsHost[3]);
                    //    Grid.SetRow(windowsFormsHost[3], 1);
                    //    Grid.SetColumn(windowsFormsHost[3], 0);

                    //    windowsFormsHost[4].Margin = new Thickness(0);
                    //    windowsFormsHost[4].Child = GetImageBox(4);
                    //    grid.Children.Add(windowsFormsHost[4]);
                    //    Grid.SetRow(windowsFormsHost[4], 1);
                    //    Grid.SetColumn(windowsFormsHost[4], 1);

                    //    windowsFormsHost[5].Margin = new Thickness(0);
                    //    windowsFormsHost[5].Child = GetImageBox(5);
                    //    grid.Children.Add(windowsFormsHost[5]);
                    //    Grid.SetRow(windowsFormsHost[5], 1);
                    //    Grid.SetColumn(windowsFormsHost[5], 2);

                    //    windowsFormsHost[6].Margin = new Thickness(0);
                    //    windowsFormsHost[6].Child = GetImageBox(6);
                    //    grid.Children.Add(windowsFormsHost[6]);
                    //    Grid.SetRow(windowsFormsHost[6], 2);
                    //    Grid.SetColumn(windowsFormsHost[6], 0);

                    //    windowsFormsHost[7].Margin = new Thickness(0);
                    //    windowsFormsHost[7].Child = GetImageBox(7);
                    //    grid.Children.Add(windowsFormsHost[7]);
                    //    Grid.SetRow(windowsFormsHost[7], 2);
                    //    Grid.SetColumn(windowsFormsHost[7], 1);

                    //    windowsFormsHost[8].Margin = new Thickness(0);
                    //    windowsFormsHost[8].Child = GetImageBox(8);
                    //    grid.Children.Add(windowsFormsHost[8]);
                    //    Grid.SetRow(windowsFormsHost[8], 2);
                    //    Grid.SetColumn(windowsFormsHost[8], 2);

                    //    break;
                    default:
                        break;
                }
            });

        }

        public void SetFullScreenEx(IRenderView view, bool bFullScreen)
        {
            try
            {

                if (bFullScreen)
                {
                    grid.Children.Clear();
                    grid.RowDefinitions.Clear();
                    grid.ColumnDefinitions.Clear();
                    if (view is RenderViewWpf)
                    {
                        grid.Children.Add(view as RenderViewWpf);
                    }
                    else if (view is RenderView)
                    {
                        grid.Children.Add((view as RenderView).Owner as RenderViewWpf);
                    }
                }
                else
                {
                    SetViewModeEx(0);
                }
            }
            catch (Exception ex)
            {

            }
        }

        public int GetIdByRenderViewEx(IRenderView view)
        {
            return 0;
        }

        public void AddView(IRenderView view1)
        {
            return;
        }

        public void ClearViews()
        {
            return;
        }

        public void SetViewModeEx(int mode)
        {
            ShowCanvasAll();
        }
        #endregion
    }
}
