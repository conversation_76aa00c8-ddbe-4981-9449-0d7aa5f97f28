{"RootPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight", "ProjectFileName": "MoonLight.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Platforms\\ActivateExtensions.cs"}, {"SourceFile": "Platforms\\ActivationEventArgs.cs"}, {"SourceFile": "Platforms\\ActivationProcessedEventArgs.cs"}, {"SourceFile": "Platforms\\AsyncEventHandler.cs"}, {"SourceFile": "Platforms\\AsyncEventHandlerExtensions.cs"}, {"SourceFile": "Platforms\\BindableCollection.cs"}, {"SourceFile": "Platforms\\CloseResult.cs"}, {"SourceFile": "Platforms\\Conductor.cs"}, {"SourceFile": "Platforms\\ConductorBase.cs"}, {"SourceFile": "Platforms\\ConductorBaseWithActiveItem.cs"}, {"SourceFile": "Platforms\\ConductorExtensions.cs"}, {"SourceFile": "Platforms\\ConductorWithCollectionAllActive.cs"}, {"SourceFile": "Platforms\\ConductorWithCollectionOneActive.cs"}, {"SourceFile": "Platforms\\ContainerExtensions.cs"}, {"SourceFile": "Platforms\\ContinueResultDecorator.cs"}, {"SourceFile": "Platforms\\Coroutine.cs"}, {"SourceFile": "Platforms\\CoroutineExecutionContext.cs"}, {"SourceFile": "Platforms\\DeactivateExtensions.cs"}, {"SourceFile": "Platforms\\DeactivationEventArgs.cs"}, {"SourceFile": "Platforms\\DebugLog.cs"}, {"SourceFile": "Platforms\\DefaultCloseStrategy.cs"}, {"SourceFile": "Platforms\\DefaultPlatformProvider.cs"}, {"SourceFile": "Platforms\\DelegateResult.cs"}, {"SourceFile": "Platforms\\EnumerableExtensions.cs"}, {"SourceFile": "Platforms\\EventAggregator.cs"}, {"SourceFile": "Platforms\\EventAggregatorExtensions.cs"}, {"SourceFile": "Platforms\\Execute.cs"}, {"SourceFile": "Platforms\\ExpressionExtensions.cs"}, {"SourceFile": "Platforms\\IActivate.cs"}, {"SourceFile": "Platforms\\IChild.cs"}, {"SourceFile": "Platforms\\IClose.cs"}, {"SourceFile": "Platforms\\ICloseResult.cs"}, {"SourceFile": "Platforms\\ICloseStrategy.cs"}, {"SourceFile": "Platforms\\IConductor.cs"}, {"SourceFile": "Platforms\\IDeactivate.cs"}, {"SourceFile": "Platforms\\IEventAggregator.cs"}, {"SourceFile": "Platforms\\IGuardClose.cs"}, {"SourceFile": "Platforms\\IHandle.cs"}, {"SourceFile": "Platforms\\IHaveActiveItem.cs"}, {"SourceFile": "Platforms\\IHaveDisplayName.cs"}, {"SourceFile": "Platforms\\ILog.cs"}, {"SourceFile": "Platforms\\INotifyPropertyChangedEx.cs"}, {"SourceFile": "Platforms\\IObservableCollection.cs"}, {"SourceFile": "Platforms\\IoC.cs"}, {"SourceFile": "Platforms\\IParent.cs"}, {"SourceFile": "Platforms\\IPlatformProvider.cs"}, {"SourceFile": "Platforms\\IResult.cs"}, {"SourceFile": "Platforms\\IScreen.cs"}, {"SourceFile": "Platforms\\IViewAware.cs"}, {"SourceFile": "Platforms\\LogManager.cs"}, {"SourceFile": "Platforms\\OverrideCancelResultDecorator.cs"}, {"SourceFile": "Platforms\\PlatformProvider.cs"}, {"SourceFile": "Platforms\\Action.cs"}, {"SourceFile": "Platforms\\ActionExecutionContext.cs"}, {"SourceFile": "Platforms\\ActionMessage.cs"}, {"SourceFile": "Platforms\\AssemblySource.cs"}, {"SourceFile": "Platforms\\AttachedCollection.cs"}, {"SourceFile": "Platforms\\Bind.cs"}, {"SourceFile": "Platforms\\BindingScope.cs"}, {"SourceFile": "Platforms\\BooleanToVisibilityConverter.cs"}, {"SourceFile": "Platforms\\Bootstrapper.cs"}, {"SourceFile": "Platforms\\ChildResolver.cs"}, {"SourceFile": "Platforms\\ConventionManager.cs"}, {"SourceFile": "Platforms\\DependencyPropertyHelper.cs"}, {"SourceFile": "Platforms\\ElementConvention.cs"}, {"SourceFile": "Platforms\\ExtensionMethods.cs"}, {"SourceFile": "Platforms\\FrameAdapter.cs"}, {"SourceFile": "Platforms\\IHaveParameters.cs"}, {"SourceFile": "Platforms\\INavigationService.cs"}, {"SourceFile": "Platforms\\Message.cs"}, {"SourceFile": "Platforms\\MessageBinder.cs"}, {"SourceFile": "Platforms\\NameTransformer.cs"}, {"SourceFile": "Platforms\\NavigationExtensions.cs"}, {"SourceFile": "Platforms\\NavigationHelper.cs"}, {"SourceFile": "Platforms\\Parameter.cs"}, {"SourceFile": "Platforms\\Parser.cs"}, {"SourceFile": "Platforms\\RegExHelper.cs"}, {"SourceFile": "Platforms\\StringSplitter.cs"}, {"SourceFile": "Platforms\\TypeMappingConfiguration.cs"}, {"SourceFile": "Platforms\\View.cs"}, {"SourceFile": "Platforms\\ViewLocator.cs"}, {"SourceFile": "Platforms\\ViewModelBinder.cs"}, {"SourceFile": "Platforms\\ViewModelLocator.cs"}, {"SourceFile": "Platforms\\WindowManager.cs"}, {"SourceFile": "Platforms\\XamlPlatformProvider.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Platforms\\PropertyChangedBase.cs"}, {"SourceFile": "Platforms\\RescueResultDecorator.cs"}, {"SourceFile": "Platforms\\ResultCompletionEventArgs.cs"}, {"SourceFile": "Platforms\\ResultDecoratorBase.cs"}, {"SourceFile": "Platforms\\ResultExtensions.cs"}, {"SourceFile": "Platforms\\Screen.cs"}, {"SourceFile": "Platforms\\ScreenExtensions.cs"}, {"SourceFile": "Platforms\\SequentialResult.cs"}, {"SourceFile": "Platforms\\SimpleContainer.cs"}, {"SourceFile": "Platforms\\SimpleResult.cs"}, {"SourceFile": "Platforms\\TaskExtensions.cs"}, {"SourceFile": "Platforms\\TaskResult.cs"}, {"SourceFile": "Platforms\\ViewAttachedEventArgs.cs"}, {"SourceFile": "Platforms\\ViewAware.cs"}, {"SourceFile": "Platforms\\WeakValueDictionary.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Resources.zh-Hans.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "UI\\AppBootstrapper.cs"}, {"SourceFile": "UI\\Framework\\AsyncCommand.cs"}, {"SourceFile": "UI\\Framework\\Behaviors\\BindableTreeViewSelectedItemBehavior.cs"}, {"SourceFile": "UI\\Framework\\Behaviors\\KeyboardFocusBehavior.cs"}, {"SourceFile": "UI\\Framework\\Behaviors\\WindowOptionsBehavior.cs"}, {"SourceFile": "UI\\Framework\\Commands\\Command.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandDefinition.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandDefinitionAttribute.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandDefinitionBase.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandHandlerAttribute.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandHandlerWrapper.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandKeyboardShortcut.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandKeyGestureService.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandListDefinition.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandRouter.cs"}, {"SourceFile": "UI\\Framework\\Commands\\CommandService.cs"}, {"SourceFile": "UI\\Framework\\Commands\\ExcludeCommandKeyboardShortcut.cs"}, {"SourceFile": "UI\\Framework\\Commands\\ICommandHandler.cs"}, {"SourceFile": "UI\\Framework\\Commands\\ICommandKeyGestureService.cs"}, {"SourceFile": "UI\\Framework\\Commands\\ICommandRerouter.cs"}, {"SourceFile": "UI\\Framework\\Commands\\ICommandRouter.cs"}, {"SourceFile": "UI\\Framework\\Commands\\ICommandService.cs"}, {"SourceFile": "UI\\Framework\\Commands\\ICommandUiItem.cs"}, {"SourceFile": "UI\\Framework\\Commands\\TargetableCommand.cs"}, {"SourceFile": "UI\\Framework\\Controls\\ClippingHwndHost.cs"}, {"SourceFile": "UI\\Framework\\Controls\\DynamicStyle.cs"}, {"SourceFile": "UI\\Framework\\Controls\\ExpanderEx.cs"}, {"SourceFile": "UI\\Framework\\Controls\\HwndMouse.cs"}, {"SourceFile": "UI\\Framework\\Controls\\HwndMouseEventArgs.cs"}, {"SourceFile": "UI\\Framework\\Controls\\HwndMouseState.cs"}, {"SourceFile": "UI\\Framework\\Controls\\HwndWrapper.cs"}, {"SourceFile": "UI\\Framework\\Controls\\SliderEx.cs"}, {"SourceFile": "UI\\Framework\\Document.cs"}, {"SourceFile": "UI\\Framework\\IDocument.cs"}, {"SourceFile": "UI\\Framework\\ILayoutItem.cs"}, {"SourceFile": "UI\\Framework\\IModule.cs"}, {"SourceFile": "UI\\Framework\\IPersistedDocument.cs"}, {"SourceFile": "UI\\Framework\\ITool.cs"}, {"SourceFile": "UI\\Framework\\IWindow.cs"}, {"SourceFile": "UI\\Framework\\LayoutItemBase.cs"}, {"SourceFile": "UI\\Framework\\Markup\\TranslateExtension.cs"}, {"SourceFile": "UI\\Framework\\Menus\\CommandMenuItemDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\ExcludeMenuDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\ExcludeMenuItemDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\ExcludeMenuItemGroupDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\MenuBarDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\MenuDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\MenuDefinitionBase.cs"}, {"SourceFile": "UI\\Framework\\Menus\\MenuItemDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\MenuItemGroupDefinition.cs"}, {"SourceFile": "UI\\Framework\\Menus\\TextMenuItemDefinition.cs"}, {"SourceFile": "UI\\Framework\\ModuleBase.cs"}, {"SourceFile": "UI\\Framework\\PersistedDocument.cs"}, {"SourceFile": "UI\\Framework\\RelayCommand.cs"}, {"SourceFile": "UI\\Framework\\Results\\IOpenResult.cs"}, {"SourceFile": "UI\\Framework\\Results\\LambdaResult.cs"}, {"SourceFile": "UI\\Framework\\Results\\OpenDocumentResult.cs"}, {"SourceFile": "UI\\Framework\\Results\\OpenResultBase.cs"}, {"SourceFile": "UI\\Framework\\Results\\Show.cs"}, {"SourceFile": "UI\\Framework\\Results\\ShowCommonDialogResult.cs"}, {"SourceFile": "UI\\Framework\\Results\\ShowDialogResult.cs"}, {"SourceFile": "UI\\Framework\\Results\\ShowToolResult.cs"}, {"SourceFile": "UI\\Framework\\Results\\ShowWindowResult.cs"}, {"SourceFile": "UI\\Framework\\Services\\EditorFileType.cs"}, {"SourceFile": "UI\\Framework\\Services\\ExtensionMethods.cs"}, {"SourceFile": "UI\\Framework\\Services\\IEditorProvider.cs"}, {"SourceFile": "UI\\Framework\\Services\\IInputManager.cs"}, {"SourceFile": "UI\\Framework\\Services\\IMainWindow.cs"}, {"SourceFile": "UI\\Framework\\Services\\InputBindingTrigger.cs"}, {"SourceFile": "UI\\Framework\\Services\\InputManager.cs"}, {"SourceFile": "UI\\Framework\\Services\\IResourceManager.cs"}, {"SourceFile": "UI\\Framework\\Services\\IShell.cs"}, {"SourceFile": "UI\\Framework\\Services\\PaneLocation.cs"}, {"SourceFile": "UI\\Framework\\Services\\ResourceManager.cs"}, {"SourceFile": "UI\\Framework\\Services\\ServiceProvider.cs"}, {"SourceFile": "UI\\Framework\\Services\\SettingsPropertyChangedEventManager.cs"}, {"SourceFile": "UI\\Framework\\ShaderEffects\\GrayscaleEffect.cs"}, {"SourceFile": "UI\\Framework\\ShaderEffects\\ShaderEffectBase.cs"}, {"SourceFile": "UI\\Framework\\ShaderEffects\\ShaderEffectUtility.cs"}, {"SourceFile": "UI\\Framework\\Themes\\BlueTheme.cs"}, {"SourceFile": "UI\\Framework\\Themes\\DarkTheme.cs"}, {"SourceFile": "UI\\Framework\\Themes\\ITheme.cs"}, {"SourceFile": "UI\\Framework\\Themes\\IThemeManager.cs"}, {"SourceFile": "UI\\Framework\\Themes\\LightTheme.cs"}, {"SourceFile": "UI\\Framework\\Themes\\ThemeManager.cs"}, {"SourceFile": "UI\\Framework\\Threading\\TaskUtility.cs"}, {"SourceFile": "UI\\Framework\\Tool.cs"}, {"SourceFile": "UI\\Framework\\ToolBars\\CommandToolBarItemDefinition.cs"}, {"SourceFile": "UI\\Framework\\ToolBars\\ExcludeToolBarDefinition.cs"}, {"SourceFile": "UI\\Framework\\ToolBars\\ExcludeToolBarItemDefinition.cs"}, {"SourceFile": "UI\\Framework\\ToolBars\\ExcludeToolBarItemGroupDefinition.cs"}, {"SourceFile": "UI\\Framework\\ToolBars\\ToolBarDefinition.cs"}, {"SourceFile": "UI\\Framework\\ToolBars\\ToolBarItemDefinition.cs"}, {"SourceFile": "UI\\Framework\\ToolBars\\ToolBarItemGroupDefinition.cs"}, {"SourceFile": "UI\\Framework\\Utils\\ItemsControlUtility.cs"}, {"SourceFile": "UI\\Framework\\VisualTreeUtility.cs"}, {"SourceFile": "UI\\Framework\\Win32\\NativeMethods.cs"}, {"SourceFile": "UI\\Framework\\WindowBase.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Behaviors\\MenuBehavior.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Controls\\MenuEx.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Controls\\MenuItemEx.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Converters\\CultureInfoNameConverter.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\IMenu.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\IMenuBuilder.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\MenuBuilder.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\MenuDefinitions.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Models\\CommandMenuItem.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Models\\MenuItemBase.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Models\\MenuItemSeparator.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Models\\MenuModel.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Models\\StandardMenuItem.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Models\\TextMenuItem.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\ViewModels\\MainMenuSettingsViewModel.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\ViewModels\\MainMenuViewModel.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Views\\MainMenuSettingsView.xaml.cs"}, {"SourceFile": "UI\\Modules\\MainMenu\\Views\\MainMenuView.xaml.cs"}, {"SourceFile": "UI\\Modules\\MainWindow\\ViewModels\\MainWindowViewModel.cs"}, {"SourceFile": "UI\\Modules\\MainWindow\\Views\\MainWindowView.xaml.cs"}, {"SourceFile": "UI\\Modules\\Settings\\Commands\\OpenSettingsCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Settings\\Commands\\OpenSettingsCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Settings\\ISettingsEditor.cs"}, {"SourceFile": "UI\\Modules\\Settings\\ISettingsEditorAsync.cs"}, {"SourceFile": "UI\\Modules\\Settings\\MenuDefinitions.cs"}, {"SourceFile": "UI\\Modules\\Settings\\ViewModels\\SettingsEditorWrapper.cs"}, {"SourceFile": "UI\\Modules\\Settings\\ViewModels\\SettingsPageViewModel.cs"}, {"SourceFile": "UI\\Modules\\Settings\\ViewModels\\SettingsViewModel.cs"}, {"SourceFile": "UI\\Modules\\Settings\\Views\\SettingsView.xaml.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\CloseFileCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\CloseFileCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\ExitCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\ExitCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\NewFileCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\NewFileCommandListDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\OpenFileCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\OpenFileCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\SaveAllFilesCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\SaveAllFilesCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\SaveFileAsCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\SaveFileCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\SwitchToDocumentCommandListDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\SwitchToDocumentListCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\ViewFullscreenCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Commands\\ViewFullscreenCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Controls\\LayoutInitializer.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Controls\\PanesStyleSelector.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Controls\\PanesTemplateSelector.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Converters\\NullableValueConverter.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Converters\\TruncateMiddleConverter.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Converters\\UriToImageSourceConverter.cs"}, {"SourceFile": "UI\\Modules\\Shell\\MenuDefinitions.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Services\\ILayoutItemStatePersister.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Services\\LayoutItemStatePersister.cs"}, {"SourceFile": "UI\\Modules\\Shell\\ToolBarDefinitions.cs"}, {"SourceFile": "UI\\Modules\\Shell\\ViewModels\\ShellViewModel.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Views\\IShellView.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Views\\LayoutUtility.cs"}, {"SourceFile": "UI\\Modules\\Shell\\Views\\ShellView.xaml.cs"}, {"SourceFile": "UI\\Modules\\StatusBar\\IStatusBar.cs"}, {"SourceFile": "UI\\Modules\\StatusBar\\IStatusBarView.cs"}, {"SourceFile": "UI\\Modules\\StatusBar\\ViewModels\\StatusBarItemViewModel.cs"}, {"SourceFile": "UI\\Modules\\StatusBar\\ViewModels\\StatusBarViewModel.cs"}, {"SourceFile": "UI\\Modules\\StatusBar\\Views\\StatusBarView.xaml.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Controls\\CustomToggleButton.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Controls\\MainToolBar.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Controls\\ToolBarBase.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Controls\\ToolBarTrayContainer.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Controls\\ToolPaneToolBar.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\IToolBar.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\IToolBarBuilder.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\IToolBars.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Models\\CommandToolBarItem.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Models\\ToolBarItemBase.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Models\\ToolBarItemSeparator.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Models\\ToolBarModel.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Module.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\ToolBarBuilder.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\ToolBarDefinitions.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\ViewModels\\ToolBarsViewModel.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Views\\IToolBarView.cs"}, {"SourceFile": "UI\\Modules\\ToolBars\\Views\\ToolBarsView.xaml.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\Commands\\ViewToolboxCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\Commands\\ViewToolboxCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\Design\\DesignTimeToolboxViewModel.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\IToolbox.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\MenuDefinitions.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\Models\\ToolboxItem.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\Services\\IToolboxService.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\Services\\ToolboxService.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\ToolboxDragDrop.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\ToolboxItemAttribute.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\ViewModels\\ToolboxItemViewModel.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\ViewModels\\ToolboxViewModel.cs"}, {"SourceFile": "UI\\Modules\\Toolbox\\Views\\ToolboxView.xaml.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\Commands\\RedoCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\Commands\\UndoCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\Commands\\ViewHistoryCommandDefinition.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\Commands\\ViewHistoryCommandHandler.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\Design\\DesignTimeHistoryViewModel.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\IHistoryTool.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\IUndoableAction.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\IUndoRedoManager.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\MenuDefinitions.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\Services\\UndoRedoManager.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\ToolBarDefinitions.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\ViewModels\\HistoryItemType.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\ViewModels\\HistoryItemViewModel.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\ViewModels\\HistoryViewModel.cs"}, {"SourceFile": "UI\\Modules\\UndoRedo\\Views\\HistoryView.xaml.cs"}, {"SourceFile": "UI\\Themes\\VS2013\\Controls\\Converters\\TreeViewIndentConverter.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\MainMenu\\Views\\MainMenuSettingsView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\MainMenu\\Views\\MainMenuView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\MainWindow\\Views\\MainWindowView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\Settings\\Views\\SettingsView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\Shell\\Views\\ShellView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\StatusBar\\Views\\StatusBarView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\ToolBars\\Views\\ToolBarsView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\Toolbox\\Views\\ToolboxView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\UI\\Modules\\UndoRedo\\Views\\HistoryView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\obj\\Debug\\GeneratedInternalTypeHelper.g.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\AvalonDock.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\ControlzEx.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\DotNetProjects.Wpf.Extended.Toolkit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MahApps.Metro.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\Microsoft.Xaml.Behaviors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationFramework.Aero.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsFormsIntegration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MoonLight.dll", "OutputItemRelativePath": "MoonLight.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}