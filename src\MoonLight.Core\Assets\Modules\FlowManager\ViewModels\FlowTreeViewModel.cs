﻿using System;
using System.ComponentModel.Composition;
using System.Windows.Media;
using MoonLight.Core.Views;
using MoonLight.Core.Common.RightControl;
using Microsoft.Win32;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Common.Log;
using System.Threading;
using MoonLight.Core.Enums;
using MoonLight.Core.Common.Helper;
using MoonLight.Core.Services;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework;

namespace MoonLight.Modules.FlowManager.ViewModels
{
    [Export]
    public class FlowTreeViewModel : Tool
    {
        private Brush _color;
        public Brush Status
        {
            get { return _color; }
            set { Set(ref _color, value); }
        }

        private string _label;
        public string Label
        {
            get { return _label; }
            set { Set(ref _label, value); }
        }

        public FlowTreeViewModel()
        {
            DisplayName = "流程";
            CurrentSolutionName = Solution.Ins.Name;
        }



        public override PaneLocation PreferredLocation => PaneLocation.Left;

        // 当前解决方案
        private string _CurrentSolutionName;
        public string CurrentSolutionName
        {
            get { return _CurrentSolutionName; }
            set { Set(ref _CurrentSolutionName, value); }
        }

        [NonSerialized]
        private CommandBase _CreateSolCommand;
        public CommandBase CreateSolCommand
        {
            get
            {
                if (_CreateSolCommand == null)
                {
                    _CreateSolCommand = new CommandBase((obj) =>
                    {
                        //Solution.Ins.CreateSolution();
                    });
                }
                return _CreateSolCommand;
            }
        }

        [NonSerialized]
        private CommandBase _OpenHardwareCommand;
        public CommandBase OpenHardwareCommand
        {
            get
            {
                if (_OpenHardwareCommand == null)
                {
                    _OpenHardwareCommand = new CommandBase((obj) =>
                    {
                        HardwareConfigView.Ins.Show();
                    });
                }
                return _OpenHardwareCommand;
            }
        }

        [NonSerialized]
        private CommandBase _LoadSolCommand;
        public CommandBase LoadSolCommand
        {
            get
            {
                if (_LoadSolCommand == null)
                {
                    _LoadSolCommand = new CommandBase((obj) =>
                    {
                        if (!IsEnableControl.Ins.Open)
                        {
                            MessageBoxView.Ins.MessageBoxShow("程序正在运行中，不允许打开新的方案", MsgType.Error);
                        }
                        OpenFileDialog openFileDialog = new OpenFileDialog();
                        openFileDialog.Filter = "解决方案 (*.vm)|*.vm";
                        openFileDialog.DefaultExt = "vm";
                        if (openFileDialog.ShowDialog() == true)
                        {
                            var view = LoadingView.Ins;
                            view.LoadingShow("加载项目中，请稍等...");
                            try
                            {
                                Solution.Ins.Load(openFileDialog.FileName);
                                CurrentSolutionName = Solution.Ins.Name;
                                // 设置显示窗口个数
                                IoC.Get<IRenderViewManager>().SetVieMode(Solution.Ins.ViewMode);
                                // 更新流程栏
                                IoC.Get<IFlow>().UpdateTree();
                                // 更新运行栏
                                IoC.Get<IProcess>().UpdateTree();
                                // 更改标题
                                IoC.Get<IMainWindow>().UpdateTitle(Solution.Ins.Name, openFileDialog.FileName);
                            }
                            catch (Exception ex)
                            {
                                Logger.AddLog($"解决方案加载失败！{ex.Message}", MsgType.Error);
                            }

                            //Thread.Sleep(200);
                            view.Close();
                        }
                    });
                }
                return _LoadSolCommand;
            }
        }

        [NonSerialized]
        private CommandBase _SaveSolCommand;
        public CommandBase SaveSolCommand
        {
            get
            {
                if (_SaveSolCommand == null)
                {
                    _SaveSolCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            if (Solution.Ins.Save())
                            {
                                Logger.AddLog("解决方案保存成功！", MsgType.Success, isDispGrowl: true);
                            }
                            else
                            {
                                Logger.AddLog("解决方案保存失败！", MsgType.Error);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"解决方案保存失败！{ex.Message}", MsgType.Error);
                        }

                    });
                }
                return _SaveSolCommand;
            }
        }



    }
}
