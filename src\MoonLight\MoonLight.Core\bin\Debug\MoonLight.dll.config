﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="MoonLight.Platform.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <MoonLight.Platform.Properties.Settings>
            <setting name="AutoHideMainMenu" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="ThemeName" serializeAs="String">
                <value>LightTheme</value>
            </setting>
            <setting name="LanguageCode" serializeAs="String">
                <value />
            </setting>
        </MoonLight.Platform.Properties.Settings>
    </userSettings>
</configuration>