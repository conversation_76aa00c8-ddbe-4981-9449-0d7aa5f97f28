﻿using HalconDotNet;
using MoonLight.Core.Common.Helper;
using MoonLight.Core.Enums;
using MoonLight.Core.Modules;
using MoonLight.Core.Services;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;

namespace MoonLight.App.Demo
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        MoonLight.UI.AppBootstrapper appBootstrapper;
        private static readonly Dictionary<string, Assembly> _loadedAssemblies = new Dictionary<string, Assembly>();

        public App()
        {
            AppDomain.CurrentDomain.AssemblyResolve += CurrentDomain_AssemblyResolve;
            appBootstrapper = new UI.AppBootstrapper(false);
            appBootstrapper.Initialize();
        }

        private static Assembly CurrentDomain_AssemblyResolve(object sender, ResolveEventArgs args)
        {
            string assemblyName = new AssemblyName(args.Name).Name;

            // 检查缓存中是否已加载
            if (_loadedAssemblies.TryGetValue(assemblyName, out Assembly loadedAssembly))
            {
                return loadedAssembly;
            }

            // 是不是当前文件夹下的程序集
            string basePath = FilePaths.ThirdPartyLibsPath;
            string path = Path.Combine(basePath, assemblyName + ".dll");
            if (File.Exists(path))
            {
                // 加载程序集并缓存
                var assembly = Assembly.LoadFrom(path);
                _loadedAssemblies[assemblyName] = assembly;
                return assembly;
            }


            // 遍历所有子文件夹
            foreach (string directory in Directory.GetDirectories(basePath))
            {
                string assemblyPath = Path.Combine(directory, assemblyName + ".dll");

                if (File.Exists(assemblyPath))
                {
                    // 加载程序集并缓存
                    var assembly = Assembly.LoadFrom(assemblyPath);
                    _loadedAssemblies[assemblyName] = assembly;
                    return assembly;
                }
            }

            //判断是否是插件库
            string pluginPath = Path.Combine(FilePaths.PluginsPath, assemblyName + ".dll");
            if (File.Exists(pluginPath))
            {
                // 加载程序集并缓存
                var assembly = Assembly.LoadFrom(pluginPath);
                _loadedAssemblies[assemblyName] = assembly;
                return assembly;
            }


            // 如果未找到程序集，返回 null
            return null;
        }



        [DllImport("kernel32.dll")]
        private static extern IntPtr SetThreadAffinityMask(IntPtr hThread, IntPtr dwAffinityMask);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentThread();

        private static void SetThreadAffinity(int core)
        {
            var mask = (IntPtr)(1 << core);
            SetThreadAffinityMask(GetCurrentThread(), mask);
        }


        protected override void OnStartup(StartupEventArgs e)
        {

            base.OnStartup(e);
            //Process.GetCurrentProcess().ProcessorAffinity = (IntPtr)0x08;

            //try
            //{
            //    Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.High;
            //}
            //catch { }

            //SetThreadAffinity(3);


            PluginService.InitPlugin();
            HOperatorSet.GetSystem("image_cache_capacity", out HTuple count);
            HOperatorSet.GetSystem("temporary_mem_cache", out HTuple cache);
            HOperatorSet.GetSystem("sse_enable", out HTuple sse);
            HOperatorSet.GetSystem("sse2_enable", out HTuple sse2);
            HOperatorSet.GetSystem("sse3_enable", out HTuple sse3);
            HOperatorSet.GetSystem("sse41_enable", out HTuple sse41);
            HOperatorSet.GetSystem("sse42_enable", out HTuple sse42);
            HOperatorSet.GetSystem("avx_enable", out HTuple avx_enable);
            HOperatorSet.GetSystem("avx2_enable", out HTuple avx_enable2);


            HOperatorSet.SetSystem("temporary_mem_cache", "idle");
            HOperatorSet.SetSystem("image_cache_capacity", 1000);
            DeviceManager.Instance.LoadDeviceConfiguration();
        }
    }
}
