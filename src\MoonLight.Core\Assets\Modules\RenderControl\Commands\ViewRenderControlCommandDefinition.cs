﻿using MoonLight.UI.Framework.Commands;

namespace MoonLight.Modules.RenderControl.Commands
{
    [CommandDefinition]
    public class ViewRenderControlCommandDefinition : CommandDefinition
    {
        public const string CommandName = "View.RenderControl";

        public override string Name
        {
            get { return CommandName; }
        }

        public override string Text
        {
            get { return "图像控件"; }
        }

        public override string ToolTip
        {
            get { return "图像控件"; }
        }
    }
}
