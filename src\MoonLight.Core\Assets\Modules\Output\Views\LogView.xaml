﻿<UserControl x:Class="MoonLight.Modules.Output.Views.LogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition />
        </Grid.RowDefinitions>
        <StackPanel Orientation="Horizontal" Margin="0 2 0 2">
            <StackPanel.Resources>
                <Style TargetType="ToggleButton" x:Key="ToggleButtonLogView" BasedOn="{StaticResource MahApps.Styles.ToggleButton.Flat}">
                    <Setter Property="Margin" Value="0 0 2 0"/>
                    <Setter Property="Height" Value="26"/>
                    <Setter Property="FontSize" Value="13"/>
                    <Setter Property="FontWeight" Value="Normal"/>
                    <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Accent3}"/>
                    <Setter Property="FontFamily" Value="{StaticResource iconfont}"/>
                    <Setter Property="Padding" Value="2"/>
                    <Style.Triggers>
                        <Trigger Property="IsChecked" Value="False">
                            <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.ThemeForeground}"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="FontWeight" Value="Bold"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.Accent2}"/>
                            <Setter Property="FontWeight" Value="Bold"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </StackPanel.Resources>
            <ToggleButton IsChecked="{Binding InfoFilter}" Style="{StaticResource ToggleButtonLogView}">
                <StackPanel Orientation="Horizontal">
                    <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Info.png" Width="16" Height="16"/>
                    <TextBlock VerticalAlignment="Center" Margin="5 0">
            <Run Text="信息"/>
            <Run Text="{Binding InfoCount}"/>
                    </TextBlock>
                </StackPanel>
            </ToggleButton>
            <ToggleButton IsChecked="{Binding WarnFilter}" Style="{StaticResource ToggleButtonLogView}">
                <StackPanel Orientation="Horizontal">
                    <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Warn.png" Width="16" Height="16"/>
                    <TextBlock VerticalAlignment="Center" Margin="5 0">
                    <Run Text="警告"/>
                    <Run Text="{Binding WarnCount}"/>
                    </TextBlock>
                </StackPanel>
            </ToggleButton>
            <ToggleButton IsChecked="{Binding ErrorFilter}" Style="{StaticResource ToggleButtonLogView}">
                <StackPanel Orientation="Horizontal">
                    <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Error.png" Width="16" Height="16"/>
                    <TextBlock VerticalAlignment="Center" Margin="5 0">
                    <Run Text="错误"/>
                    <Run Text="{Binding ErrorCount}"/>
                    </TextBlock>
                </StackPanel>
            </ToggleButton>
            <ToggleButton IsChecked="{Binding AlarmFilter}" Style="{StaticResource ToggleButtonLogView}">
                <StackPanel Orientation="Horizontal">
                    <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Alarm.png" Width="16" Height="16"/>
                    <TextBlock VerticalAlignment="Center" Margin="5 0">
                    <Run Text="报警"/>
                    <Run Text="{Binding AlarmCount}"/>
                    </TextBlock>
                </StackPanel>
            </ToggleButton>

        </StackPanel>
        <DataGrid Grid.Row="1" AutoGenerateColumns="False" x:Name="dg"
            EnableColumnVirtualization="True"
            EnableRowVirtualization="True"
            VirtualizingPanel.IsVirtualizing="True"
            ItemsSource="{Binding DisplayLogCollection}" 
            SelectedItem="{Binding SelectedItem}"
            FontSize="12" 
            RowHeaderWidth="20"
            SelectionUnit="FullRow"
            HeadersVisibility="Column"
            GridLinesVisibility="All"
            IsReadOnly="True" CanUserAddRows="False">
            <DataGrid.ContextMenu>
                <ContextMenu StaysOpen="True">
                    <MenuItem Header="清除警报" Command="{Binding ClearAlarmCommand}"/>
                    <MenuItem Header="清除所有" Command="{Binding ClearAllLogCommand}"/>
                    <MenuItem Header="复制" Command="{Binding CopyLogCommand}"/>
                </ContextMenu>
            </DataGrid.ContextMenu>
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="时间"  IsReadOnly="True" Width="160">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding CreateTime,StringFormat=\{0:yyyy/MM/dd HH:mm:ss fff\}}" Margin="5 0" HorizontalAlignment="Left"  Foreground="{DynamicResource MahApps.Brushes.ThemeForeground}" FontWeight="Medium"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn  Header="类型" IsReadOnly="True" Width="50">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding LogType}" HorizontalAlignment="Left" Margin="5 0"  Foreground="{DynamicResource MahApps.Brushes.ThemeForeground}" FontWeight="Medium"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="日志内容"  IsReadOnly="True" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Content}" HorizontalAlignment="Left" Margin="5 0" Foreground="{DynamicResource MahApps.Brushes.ThemeForeground}" FontFamily="微软雅黑" FontWeight="Medium"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
