﻿using MoonLight.Core.Assets.Modules.PosManager.ViewModels;
using MoonLight.UI.Framework.Commands;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MoonLight.Core.Assets.Modules.PosManager.Commands
{
    [CommandHandler]
    public class OpenPosManagerCommandHandler : CommandHandlerBase<OpenPosManagerCommandDefinition>
    {

        private readonly IWindowManager _windowManager;

        [ImportingConstructor]
        public OpenPosManagerCommandHandler(IWindowManager windowManager)
        {
            _windowManager = windowManager;
        }

        public override async Task Run(Command command)
        {
            await _windowManager.ShowDialogAsync(IoC.Get<PosManagerViewModel>());
        }
    }
}
