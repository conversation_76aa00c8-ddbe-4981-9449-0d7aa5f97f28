using MoonLight.Core.Devices;
using System.Collections.ObjectModel;

namespace MoonLight.Core.Modules
{
    public class DeviceToolboxGroup
    {
        public string GroupName { get; }
        public ObservableCollection<DeviceTemplate> Items { get; } = new ObservableCollection<DeviceTemplate>();

        public DeviceToolboxGroup(string groupName)
        {
            GroupName = groupName;
        }
    }

    public class DeviceTemplate
    {
        public string Name { get; }
        public DeviceCategory Category { get; }
        public string IconPath { get; }

        public DeviceTemplate(string name, DeviceCategory category, string iconPath)
        {
            Name = name;
            Category = category;
            IconPath = iconPath;
        }
    }


}
