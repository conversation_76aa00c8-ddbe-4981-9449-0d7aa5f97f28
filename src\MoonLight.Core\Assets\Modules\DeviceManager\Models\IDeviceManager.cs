﻿using MoonLight.Core.Devices;
using MoonLight.Core.Devices.Camera;
using MoonLight.Core.Motion;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace MoonLight.Core.Modules
{
    public interface IDeviceManager
    {
        ObservableCollection<DeviceCategoryGroup> DeviceGroups { get; }
        IDevice SelectedDevice { get; set; }
        FrameworkElement CurrentConfigurationUI { get; set; }

        event EventHandler ConfigurationUIChanged;

        IDevice GetDeviceById(string id);
        IDevice GetDeviceByName(string name);
        List<CardBase> GetCardDevices();
        List<CameraBase> GetCamDevices();

        void AddDevice(IDevice device);
        void RemoveDevice(string deviceId);
        void SaveDeviceConfiguration();
        void LoadDeviceConfiguration();

    }
}
