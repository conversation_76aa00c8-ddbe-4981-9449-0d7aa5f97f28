﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{08C42464-E6EA-4010-A329-F7D4C6706D22}</ProjectGuid>
    <OutputType>library</OutputType>
    <RootNamespace>MoonLight.Core</RootNamespace>
    <AssemblyName>MoonLight.Core</AssemblyName>
    <LangVersion>11.0</LangVersion>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>5</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CommunityToolkit.Mvvm, Version=8.4.0.0, Culture=neutral, PublicKeyToken=4aff67a105548ee2, processorArchitecture=MSIL">
      <HintPath>..\packages\CommunityToolkit.Mvvm.8.4.0\lib\netstandard2.0\CommunityToolkit.Mvvm.dll</HintPath>
    </Reference>
    <Reference Include="EventMgrLib">
      <HintPath>..\..\dlls\Common\EventMgrLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="GxIAPINET">
      <HintPath>..\..\dlls\DaHeng\GxIAPINET.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="halcondotnetxl">
      <HintPath>..\..\dlls\halcon\halcondotnetxl.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="HandyControl">
      <HintPath>..\..\dlls\UI\HandyControl.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="HslCommunication">
      <HintPath>..\..\dlls\Communication\HslCommunication.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ICSharpCode.CodeCompletion, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\dlls\ICSharpCode\ICSharpCode.CodeCompletion.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MahApps.Metro, Version=2.0.0.0, Culture=neutral, PublicKeyToken=51482d6f650b2b3f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\MoonLight.Platform_V3_20150523\bin\MahApps.Metro.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Xaml.Behaviors, Version=1.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\MoonLight.Platform_V3_20150523\bin\Microsoft.Xaml.Behaviors.dll</HintPath>
    </Reference>
    <Reference Include="MvCameraControl.Net, Version=3.4.0.1, Culture=neutral, PublicKeyToken=52fddfb3f94be800, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\dlls\Hik\MvCameraControl.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Controls\Attatch\TitleElement.cs" />
    <Compile Include="Assets\Controls\Attatch\VisualElement.cs" />
    <Compile Include="Assets\Controls\Button\ButtonGroup.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\DatePropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\DateTimePropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\EnumPropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\HorizontalAlignmentPropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\ImagePropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\NumberPropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\PlainTextPropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\ReadOnlyTextPropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\SwitchPropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\TimePropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\Editors\VerticalAlignmentPropertyEditor.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\PropertyEditorBase.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\PropertyGrid.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\PropertyItem.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\PropertyItemsControl.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\PropertyResolver.cs" />
    <Compile Include="Assets\Controls\PropertyGrid\SearchBar.cs" />
    <Compile Include="Assets\Converter\Bool2ColorConverter.cs" />
    <Compile Include="Assets\Converter\Bool2GreenColorConverter.cs" />
    <Compile Include="Assets\Converter\Bool2LimeConverter.cs" />
    <Compile Include="Assets\Converter\Bool2VisibilityConverter.cs" />
    <Compile Include="Assets\Converter\Bool2VisibilityHiddenConverter.cs" />
    <Compile Include="Assets\Converter\BoolToBrushConverter.cs" />
    <Compile Include="Assets\Converter\BoolToInverseConverter.cs" />
    <Compile Include="Assets\Converter\BoolToLimeBrushConverter.cs" />
    <Compile Include="Assets\Converter\BoolToRedBrushConverter.cs" />
    <Compile Include="Assets\Converter\BrushRoundConverter.cs" />
    <Compile Include="Assets\Converter\EnumArrayToVisibilityConverter.cs" />
    <Compile Include="Assets\Converter\EnumConverter.cs" />
    <Compile Include="Assets\Converter\EnumToVisibilityConverter.cs" />
    <Compile Include="Assets\Converter\EnumToVisibilityConverter_2.cs" />
    <Compile Include="Assets\Converter\ExpanderToBooleanConverter.cs" />
    <Compile Include="Assets\Converter\IntToWobbleTypeConverter.cs" />
    <Compile Include="Assets\Converter\InvertBool2VisibilityConverter.cs" />
    <Compile Include="Assets\Converter\InvertBoolConverter.cs" />
    <Compile Include="Assets\Converter\MediaColorToDrawingColorConverter.cs" />
    <Compile Include="Assets\Converter\NullableToVisibilityConverter.cs" />
    <Compile Include="Assets\Converter\Object2IntOrDoubleConverter.cs" />
    <Compile Include="Assets\Converter\ObjectToStringConverter.cs" />
    <Compile Include="Assets\Converter\PathToImageConverter.cs" />
    <Compile Include="Assets\Converter\RowToIndexConverter.cs" />
    <Compile Include="Assets\Converter\StatusConverter.cs" />
    <Compile Include="Assets\Data\ResourceToken.cs" />
    <Compile Include="Assets\Data\ValueBoxes.cs" />
    <Compile Include="Assets\Extension\EnumerableExtension.cs" />
    <Compile Include="Assets\Extension\UIElementExtension.cs" />
    <Compile Include="Assets\Interactivity\ControlCommands.cs" />
    <Compile Include="Assets\Location\Resources.Designer.cs" />
    <Compile Include="Assets\Modules\Algorithms\VisionException.cs" />
    <Compile Include="Assets\Modules\Algorithms\VisionLib.cs" />
    <Compile Include="Assets\Modules\DeviceManager\Models\DeviceCategoryGroup.cs" />
    <Compile Include="Assets\Modules\DeviceManager\Models\IDeviceManager.cs" />
    <Compile Include="Assets\Modules\FlowManager\Commands\ViewFlowCommandDefinition.cs" />
    <Compile Include="Assets\Modules\FlowManager\Commands\ViewFlowCommandHandler.cs" />
    <Compile Include="Assets\Modules\FlowManager\Commands\ViewPropertyPanelCommandDefinition.cs" />
    <Compile Include="Assets\Modules\FlowManager\Commands\ViewPropertyPanelCommandHandler.cs" />
    <Compile Include="Assets\Modules\FlowManager\Commands\ViewRunManagerCommandDefinition.cs" />
    <Compile Include="Assets\Modules\FlowManager\Commands\ViewRunManagerCommandHandler.cs" />
    <Compile Include="Assets\Modules\FlowManager\ViewModels\FlowTreeViewModel.cs" />
    <Compile Include="Assets\Modules\FlowManager\ViewModels\PropertyPanelViewModel.cs" />
    <Compile Include="Assets\Modules\FlowManager\ViewModels\RunManagerViewModel.cs" />
    <Compile Include="Assets\Modules\FlowManager\Views\FlowTreeView.xaml.cs">
      <DependentUpon>FlowTreeView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\FlowManager\Views\PropertyPanelView.xaml.cs">
      <DependentUpon>PropertyPanelView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\FlowManager\Views\RunManagerView.xaml.cs">
      <DependentUpon>RunManagerView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\MenuDefinitions.cs" />
    <Compile Include="Assets\Modules\Modules.cs" />
    <Compile Include="Assets\Modules\Output\Commands\ViewOutputCommandDefinition.cs" />
    <Compile Include="Assets\Modules\Output\Commands\ViewOutputCommandHandler.cs" />
    <Compile Include="Assets\Modules\Output\Models\IOutput.cs" />
    <Compile Include="Assets\Modules\Output\Models\IOutputView.cs" />
    <Compile Include="Assets\Modules\Output\Models\OutputWriter.cs" />
    <Compile Include="Assets\Modules\Output\ViewModels\LogViewModel.cs" />
    <Compile Include="Assets\Modules\Output\ViewModels\OutputViewModel.cs" />
    <Compile Include="Assets\Modules\Output\Views\LogView.xaml.cs">
      <DependentUpon>LogView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\Output\Views\OutputView.xaml.cs">
      <DependentUpon>OutputView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\PosManager\Commands\OpenPosManagerCommandDefinition.cs" />
    <Compile Include="Assets\Modules\PosManager\Commands\OpenPosManagerCommandHandler.cs" />
    <Compile Include="Assets\Modules\PosManager\ViewModels\PosManagerViewModel.cs" />
    <Compile Include="Assets\Modules\PosManager\Views\PosManagerView.xaml.cs">
      <DependentUpon>PosManagerView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\RenderControl\Commands\ViewRenderControlCommandDefinition.cs" />
    <Compile Include="Assets\Modules\RenderControl\Commands\ViewRenderControlCommandHandler.cs" />
    <Compile Include="Assets\Modules\RenderControl\Managers\RenderManager.cs" />
    <Compile Include="Assets\Modules\RenderControl\ViewModels\VisionViewModel.cs" />
    <Compile Include="Assets\Modules\RenderControl\Views\RenderView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Assets\Modules\RenderControl\Views\RenderView.Designer.cs">
      <DependentUpon>RenderView.cs</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\RenderControl\Views\RenderViewWpf.xaml.cs">
      <DependentUpon>RenderViewWpf.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\RenderControl\Views\VisionView.xaml.cs">
      <DependentUpon>VisionView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\Settings\Commands\OpenParamSettingCommandDefinition.cs" />
    <Compile Include="Assets\Modules\Settings\Commands\OpenParamSettingCommandHandler.cs" />
    <Compile Include="Assets\Modules\Settings\Models\SysConfig.cs" />
    <Compile Include="Assets\Modules\Settings\ViewModels\SettingSysViewModel.cs" />
    <Compile Include="Assets\Modules\Settings\Views\SettingSysView.xaml.cs">
      <DependentUpon>SettingSysView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\ToolBar\Commands\AddSolutionCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\AddSolutionCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\CommunicationSetCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\CommunicationSetCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\GlobalVarCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\GlobalVarCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\RunCycleCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\RunCycleCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\RunOnceCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\RunOnceCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\SaveFileCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\StopCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\StopCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\UIDesignCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\UIDesignCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\UserLoginCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBar\Commands\UserLoginCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBar\ToolBarDefinitions.cs" />
    <Compile Include="Assets\Modules\ToolBar\ViewModels\CanvasSetViewModel.cs" />
    <Compile Include="Assets\Modules\ToolBar\ViewModels\CommunicationSetViewModel.cs" />
    <Compile Include="Assets\Modules\ToolBar\ViewModels\GlobalVarViewModel.cs" />
    <Compile Include="Assets\Modules\ToolBar\Views\CanvasSetView.xaml.cs">
      <DependentUpon>CanvasSetView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\ToolBar\Views\CommunicationSetView.xaml.cs">
      <DependentUpon>CommunicationSetView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\ToolBar\Views\GlobalVarView.xaml.cs">
      <DependentUpon>GlobalVarView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\ToolBox\Commands\ViewToolBoxCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolBox\Commands\ViewToolBoxCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolBox\ViewModels\ToolBoxViewModel.cs" />
    <Compile Include="Assets\Modules\ToolBox\Views\ToolBoxView.xaml.cs">
      <DependentUpon>ToolBoxView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\ToolOutput\Commands\ViewDeviceStatusCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolOutput\Commands\ViewDeviceStatusCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolOutput\Commands\ViewToolOutputCommandDefinition.cs" />
    <Compile Include="Assets\Modules\ToolOutput\Commands\ViewToolOutputCommandHandler.cs" />
    <Compile Include="Assets\Modules\ToolOutput\Models\ToolOutputModel.cs" />
    <Compile Include="Assets\Modules\ToolOutput\ViewModels\DeviceStatusViewModel.cs" />
    <Compile Include="Assets\Modules\ToolOutput\ViewModels\ToolOutputViewModel.cs" />
    <Compile Include="Assets\Modules\ToolOutput\Views\DeviceStatusView.xaml.cs">
      <DependentUpon>DeviceStatusView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Modules\ToolOutput\Views\ToolOutputView.xaml.cs">
      <DependentUpon>ToolOutputView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Assets\Styles\Models\Chip.cs" />
    <Compile Include="Attributes\PropertyAtrribute.cs" />
    <Compile Include="Attributes\ToolImageNameAttribute.cs" />
    <Compile Include="Common\EComInfo.cs" />
    <Compile Include="Common\Helper\AsyncObservableCollection.cs" />
    <Compile Include="Common\Helper\CloneObject.cs" />
    <Compile Include="Common\Helper\CommandBase.cs" />
    <Compile Include="Common\Helper\DataConvert.cs" />
    <Compile Include="Common\Helper\EventTriggerAction.cs" />
    <Compile Include="Common\Helper\FilePaths.cs" />
    <Compile Include="Common\Helper\ImageTool.cs" />
    <Compile Include="Common\Helper\ItemsControlHelp.cs" />
    <Compile Include="Common\Helper\JsonSerializerHelper.cs" />
    <Compile Include="Common\Helper\MD5Provider.cs" />
    <Compile Include="Common\Helper\NotifyPropertyBase.cs" />
    <Compile Include="Common\Helper\SerializeHelp.cs" />
    <Compile Include="Common\Helper\SingleInstance.cs" />
    <Compile Include="Common\Helper\StringHelper.cs" />
    <Compile Include="Common\Helper\WPFCursorTool.cs" />
    <Compile Include="Common\Helper\WPFElementTool.cs" />
    <Compile Include="Common\Helper\XmlHelper.cs" />
    <Compile Include="Common\Log\Logger.cs" />
    <Compile Include="Common\RightControl\IsEnableControl.cs" />
    <Compile Include="Common\RightControl\RightControl.cs" />
    <Compile Include="Communacation\EComManageer.cs" />
    <Compile Include="Communacation\Ecommunacation.cs" />
    <Compile Include="Communacation\SerialPort\MySerialPort.cs" />
    <Compile Include="Communacation\Socket\DMTcpClient.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Communacation\Socket\DMTcpServer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Communacation\Socket\DMUdpClient.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Communacation\Socket\IDataCell.cs" />
    <Compile Include="Communacation\Socket\MsgCell.cs" />
    <Compile Include="Communacation\Socket\MsgTypeCell.cs" />
    <Compile Include="Communacation\Socket\ReceiveDataEventArgs.cs" />
    <Compile Include="Communacation\Socket\ReceiveDataEventHandler.cs" />
    <Compile Include="Communacation\Socket\SerHelper.cs" />
    <Compile Include="Communacation\Socket\SocketState.cs" />
    <Compile Include="Communacation\Socket\UdpLibrary.cs" />
    <Compile Include="Communacation\Tool\HexTool.cs" />
    <Compile Include="Defines\CameraType.cs" />
    <Compile Include="Defines\CardType.cs" />
    <Compile Include="Defines\RotateAngle.cs" />
    <Compile Include="Defines\StepType.cs" />
    <Compile Include="Defines\VelType.cs" />
    <Compile Include="Devices\Camera\CameraBase.cs" />
    <Compile Include="Devices\Camera\CameraInfo.cs" />
    <Compile Include="Devices\Camera\ICamera.cs" />
    <Compile Include="Devices\Camera\ImageData.cs" />
    <Compile Include="Devices\Camera\StatusCamera.cs" />
    <Compile Include="Devices\Communication\CommunicationBase.cs" />
    <Compile Include="Devices\Communication\CommunicationSetView.xaml.cs">
      <DependentUpon>CommunicationSetView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Devices\Communication\CommunicationSetViewModel.cs" />
    <Compile Include="Devices\Communication\DMTcpClient.cs" />
    <Compile Include="Devices\Communication\DMTcpServer.cs" />
    <Compile Include="Devices\Communication\DMUdpClient.cs" />
    <Compile Include="Devices\Communication\MySerialPort.cs" />
    <Compile Include="Devices\ConfigBase.cs" />
    <Compile Include="Devices\ConfigBaseEx.cs" />
    <Compile Include="Devices\ConfigDevice.cs" />
    <Compile Include="Devices\DeviceBase.cs" />
    <Compile Include="Devices\DeviceStatus.cs" />
    <Compile Include="Devices\DeviceTreeNode.cs" />
    <Compile Include="Devices\DeviceType.cs" />
    <Compile Include="Devices\IDevice.cs" />
    <Compile Include="Devices\IDeviceConfiguration.cs" />
    <Compile Include="Devices\Motion\IAxis.cs" />
    <Compile Include="Devices\Motion\IOBaseEx.cs" />
    <Compile Include="Devices\Motion\LimitBase.cs" />
    <Compile Include="Devices\Motion\StatusAxis.cs" />
    <Compile Include="Devices\Motion\StatusCard.cs" />
    <Compile Include="Devices\Motion\StatusPmac.cs" />
    <Compile Include="Devices\Motion\ViewModels\CardViewModel.cs" />
    <Compile Include="Devices\Motion\Views\CardView.xaml.cs">
      <DependentUpon>CardView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Devices\StatusBase.cs" />
    <Compile Include="Enums\EnumType.cs" />
    <Compile Include="Enums\UserType.cs" />
    <Compile Include="Events\AddCameraEvent.cs" />
    <Compile Include="Events\CurrentUserChangedEvent.cs" />
    <Compile Include="Events\FunctionEventArgs.cs" />
    <Compile Include="Events\HardwareChangedEvent.cs" />
    <Compile Include="Events\ModuleOutChangedEvent.cs" />
    <Compile Include="Events\OpenVarLinkViewEvent.cs" />
    <Compile Include="Events\SoftwareExitEvent.cs" />
    <Compile Include="Events\VarChangedEvent.cs" />
    <Compile Include="Extension\ObservableCollectionExtension.cs" />
    <Compile Include="Interfaces\IFileSystemFactory.cs" />
    <Compile Include="Interfaces\IFileSystemService.cs" />
    <Compile Include="Interfaces\IFlow.cs" />
    <Compile Include="Interfaces\IProcess.cs" />
    <Compile Include="Interfaces\IRenderView.cs" />
    <Compile Include="Interfaces\IRenderViewManager.cs" />
    <Compile Include="Interfaces\IToolUnit.cs" />
    <Compile Include="Models\AlarmSummaryModel.cs" />
    <Compile Include="Models\CommonMethods.cs" />
    <Compile Include="Models\DragDropModel.cs" />
    <Compile Include="Models\Fit.cs" />
    <Compile Include="Models\Gen.cs" />
    <Compile Include="Models\IHierarchical.cs" />
    <Compile Include="Models\Line.cs" />
    <Compile Include="Models\LinkVarModel.cs" />
    <Compile Include="Models\LogModel.cs" />
    <Compile Include="Models\MachineModel.cs" />
    <Compile Include="Models\ToolViewBase.cs" />
    <Compile Include="Models\MotionBase.cs" />
    <Compile Include="Models\PluginsInfo.cs" />
    <Compile Include="Models\ProjectInfo.cs" />
    <Compile Include="Models\Sol\Drive.cs" />
    <Compile Include="Models\Sol\DriveNode.cs" />
    <Compile Include="Models\Sol\File.cs" />
    <Compile Include="Models\Sol\FileNode.cs" />
    <Compile Include="Models\Sol\FileSystemObject.cs" />
    <Compile Include="Models\Sol\FileSystemObjectNode.cs" />
    <Compile Include="Models\Sol\Folder.cs" />
    <Compile Include="Models\Sol\FolderNode.cs" />
    <Compile Include="Models\ToolList.cs" />
    <Compile Include="Models\ToolNode.cs" />
    <Compile Include="Models\ToolUnit.cs" />
    <Compile Include="Models\TreeNode.cs" />
    <Compile Include="Models\UserModel.cs" />
    <Compile Include="Models\VarModel.cs" />
    <Compile Include="Models\VisionInfo.cs" />
    <Compile Include="Models\VisionParam.cs" />
    <Compile Include="Models\VisionResult.cs" />
    <Compile Include="Devices\Motion\AxisBase.cs" />
    <Compile Include="Devices\Motion\AxisStatus.cs" />
    <Compile Include="Devices\Motion\CardBase.cs" />
    <Compile Include="Devices\Motion\ICard.cs" />
    <Compile Include="Devices\Motion\IOBase.cs" />
    <Compile Include="Devices\Motion\MotionHelper.cs" />
    <Compile Include="Devices\Motion\UnitStatus.cs" />
    <Compile Include="Devices\Motion\ViewModels\AxisControlViewModel.cs" />
    <Compile Include="Devices\Motion\Views\AxisControlView.xaml.cs">
      <DependentUpon>AxisControlView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Modules\PosManager\IPosManager.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="ROIs\HRoi.cs" />
    <Compile Include="ROIs\HText.cs" />
    <Compile Include="ROIs\ROI.cs" />
    <Compile Include="ROIs\ROICaliper.cs" />
    <Compile Include="ROIs\ROICaliper1D.cs" />
    <Compile Include="ROIs\ROICircle.cs" />
    <Compile Include="ROIs\ROICircleCaliper.cs" />
    <Compile Include="ROIs\ROICoordinates.cs" />
    <Compile Include="ROIs\ROIInfo.cs" />
    <Compile Include="ROIs\ROILine.cs" />
    <Compile Include="ROIs\ROIRectangle.cs" />
    <Compile Include="ROIs\ROIRectangle2.cs" />
    <Compile Include="ROIs\ROIRectCaliper.cs" />
    <Compile Include="ROIs\ROIText.cs" />
    <Compile Include="Scrip\BoolScriptSupport.cs" />
    <Compile Include="Scrip\BoolScriptTemplate.cs" />
    <Compile Include="Scrip\ExpressionScriptSupport.cs" />
    <Compile Include="Scrip\ExpressionScriptTemplate.cs" />
    <Compile Include="Scrip\ScriptMethods.cs" />
    <Compile Include="Scrip\ScriptProvider.cs" />
    <Compile Include="Services\EngineService.cs" />
    <Compile Include="Services\FileIconService.cs" />
    <Compile Include="Services\FileSystemFactory.cs" />
    <Compile Include="Services\FileSystemService.cs" />
    <Compile Include="Services\PluginService.cs" />
    <Compile Include="Services\Project.cs" />
    <Compile Include="Services\ProjectFlowTree.cs" />
    <Compile Include="Services\ResourceHelper.cs" />
    <Compile Include="Services\SolManager.cs" />
    <Compile Include="Services\Solution.cs" />
    <Compile Include="Config\RImage.cs" />
    <Compile Include="Services\SolutionInfo.cs" />
    <Compile Include="ViewModels\EditRemarksViewModel.cs" />
    <Compile Include="ViewModels\HardwareConfigViewModel.cs" />
    <Compile Include="ViewModels\LoadingViewModel.cs" />
    <Compile Include="ViewModels\MessageBoxViewModel.cs" />
    <Compile Include="ViewModels\VarLinkViewModel.cs" />
    <Compile Include="Views\ChangePwdView.xaml.cs">
      <DependentUpon>ChangePwdView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\EditRemarksView.xaml.cs">
      <DependentUpon>EditRemarksView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\HardwareConfigView.xaml.cs">
      <DependentUpon>HardwareConfigView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\LoadingView.xaml.cs">
      <DependentUpon>LoadingView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\LoginView.xaml.cs">
      <DependentUpon>LoginView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MessageBoxView.xaml.cs">
      <DependentUpon>MessageBoxView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\VarLinkView.xaml.cs">
      <DependentUpon>VarLinkView.xaml</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Assets\Location\Resource.en.resx" />
    <EmbeddedResource Include="Assets\Location\Resource.resx" />
    <EmbeddedResource Include="Assets\Location\Resource.zh.resx" />
    <EmbeddedResource Include="Assets\Modules\RenderControl\Views\RenderView.resx">
      <DependentUpon>RenderView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <Resource Include="Assets\Fonts\digital_display.ttf" />
    <Resource Include="Assets\Fonts\iconfont.ttf" />
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Page Include="Assets\Collection.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\FlowManager\Views\FlowTreeView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\FlowManager\Views\PropertyPanelView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\FlowManager\Views\RunManagerView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\Output\Views\LogView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\Output\Views\OutputView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\PosManager\Views\PosManagerView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Assets\Modules\RenderControl\Views\RenderViewWpf.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\RenderControl\Views\VisionView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Assets\Modules\Settings\Views\SettingSysView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Assets\Modules\ToolBar\Views\CanvasSetView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\ToolBar\Views\CommunicationSetView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\ToolBar\Views\GlobalVarView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\ToolBox\Views\ToolBoxView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\ToolOutput\Views\DeviceStatusView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Modules\ToolOutput\Views\ToolOutputView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\ButtonGroupStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\ButtonStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\Converter.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\Icon.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\MenuStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\PropertyGridStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\RadioButtonStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\TabControlStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\TextBoxStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Assets\Styles\TreeViewStyle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Devices\Communication\CommunicationSetView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Devices\Motion\Views\AxisControlView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Devices\Motion\Views\CardView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ChangePwdView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\EditRemarksView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\HardwareConfigView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\LoadingView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\LoginView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\MessageBoxView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\VarLinkView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Flow\主流程.png" />
    <Resource Include="Assets\Images\Flow\子流程.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Flow\方法.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Flow\Folder.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Flow\Default.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Alarm.png" />
    <Resource Include="Assets\Images\Error.png" />
    <Resource Include="Assets\Images\Info.png" />
    <Resource Include="Assets\Images\Warn.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Flow\btnAddProject.png" />
    <Resource Include="Assets\Images\Flow\btnDeleteProject.png" />
    <Resource Include="Assets\Images\Flow\function.png" />
    <Resource Include="Assets\Images\Flow\tsbSettingProject.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\GrabImage.png" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Assets\Modules\PosManager\Models\" />
    <Folder Include="Assets\Tools\" />
    <Folder Include="Common\Container\" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\Matching.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\ToolBar\tsbOpenSolution.png" />
    <Resource Include="Assets\Images\ToolBar\tsbSaveSolution.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\PerProcessing.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\NPointCal.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\MeasureLine.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\Coordinate.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\BuildLl.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\QRCode.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\Measuring.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\StopWhile.png" />
    <Resource Include="Assets\Images\Tool\While.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\recycleStart.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\recycleEnd.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\Delay.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\else.png" />
    <Resource Include="Assets\Images\Tool\elseif.png" />
    <Resource Include="Assets\Images\Tool\end.png" />
    <Resource Include="Assets\Images\Tool\If.png" />
    <Resource Include="Assets\Images\Tool\IfStart.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\Folder.png" />
    <Resource Include="Assets\Images\Tool\SystemTime.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\TimeSlice.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\VarDefine.png" />
    <Resource Include="Assets\Images\Tool\VarSet.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\Blob.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\MeasureCircle.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\DataCheck.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\SplitText.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\SplitString.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\BuildPp.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\BuildPL.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\ParallelBlock.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\SingleAxisMove.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Solution\File.png" />
    <Resource Include="Assets\Images\Solution\Folder Open.png" />
    <Resource Include="Assets\Images\Solution\Folder.png" />
    <Resource Include="Assets\Images\Solution\Hard Drive.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Solution\Drive.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\BuildLC.png" />
    <Resource Include="Assets\Images\Tool\CoordinateMap.png" />
    <Resource Include="Assets\Images\Tool\FitCircle.png" />
    <Resource Include="Assets\Images\Tool\FitLine.png" />
    <Resource Include="Assets\Images\Tool\MeasureCalib.png" />
    <Resource Include="Assets\Images\Tool\RobotControl.png" />
    <Resource Include="Assets\Images\Tool\SaveImage.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\ReceiveStr.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\SendStr.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Common\down.png" />
    <Resource Include="Assets\Images\Common\right.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\forbid.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\ProjectOutput.png" />
    <Resource Include="Assets\Images\Tool\Text.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Solution\AddSol.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Solution\DeleteSol.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Solution\Sol.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Solution\UpdateSol.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Flow\调用执行流程.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\MetricToolkit.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\Tool\MeasureLines.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\MoonLight\MoonLight.csproj">
      <Project>{9f9ce548-5f95-482f-b2cb-6dfb2b458dd9}</Project>
      <Name>MoonLight</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\CommunityToolkit.Mvvm.8.4.0\build\CommunityToolkit.Mvvm.targets" Condition="Exists('..\packages\CommunityToolkit.Mvvm.8.4.0\build\CommunityToolkit.Mvvm.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\CommunityToolkit.Mvvm.8.4.0\build\CommunityToolkit.Mvvm.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\CommunityToolkit.Mvvm.8.4.0\build\CommunityToolkit.Mvvm.targets'))" />
  </Target>
</Project>