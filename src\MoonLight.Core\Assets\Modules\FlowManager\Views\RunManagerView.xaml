﻿<UserControl
    x:Class="MoonLight.Modules.FlowManager.Views.RunManagerView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:model="clr-namespace:MoonLight.Core.Models;assembly=MoonLight.Core"
    xmlns:rightControl="clr-namespace:MoonLight.Core.Common.RightControl"
    xmlns:sys="clr-namespace:System;assembly=mscorlib"
    UseLayoutRounding="True">
    <UserControl.Resources>
        <rightControl:IsEnableControl x:Key="IsEnableControl" />

        <ObjectDataProvider
            x:Key="colorsTypeOdp"
            MethodName="GetType"
            ObjectType="{x:Type sys:Type}">
            <ObjectDataProvider.MethodParameters>
                <sys:String>System.Windows.Media.Colors, PresentationCore, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35</sys:String>
            </ObjectDataProvider.MethodParameters>
        </ObjectDataProvider>
        <ObjectDataProvider
            x:Key="colorPropertiesOdp"
            MethodName="GetProperties"
            ObjectInstance="{StaticResource colorsTypeOdp}" />

        <!--<DataTemplate x:Key="ColorEditTemplate" DataType="{x:Type vm:ColorItemViewModel}">
        <StackPanel Orientation="Horizontal">
            <TextBlock Text="Colour:" VerticalAlignment="Center"/>
            <ComboBox
			Width="170" Margin="6,0,0,0"
			ItemsSource="{Binding Source={StaticResource colorPropertiesOdp}}"
			SelectedValue="{Binding Color}"
			SelectedValuePath="Name">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <Rectangle Margin="0,1,4,1" Width="15" Fill="{Binding Name}"/>
                            <TextBlock Text="{Binding Name}"/>
                        </StackPanel>
                    </DataTemplate>
                </ComboBox.ItemTemplate>
            </ComboBox>
        </StackPanel>
    </DataTemplate>-->


        <!--<local:TypeTemplateSelector x:Key="EditorTemplateSelector">
        <local:TypeTemplateSelector.TemplateDefinitions>
            -->
        <!--<local:TypeTemplateDefinition Type="{x:Type vm:ColorItemViewModel}" Template="{StaticResource ColorEditTemplate}"/>-->
        <!--
            <local:TypeTemplateDefinition Type="{x:Type vm:TreeItemViewModel}" Template="{StaticResource EditTemplate}"/>
        </local:TypeTemplateSelector.TemplateDefinitions>
    </local:TypeTemplateSelector>-->

        <BooleanToVisibilityConverter x:Key="BoolToVisibility" />

    </UserControl.Resources>



    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="36" />

            <RowDefinition Height="3*" />
            <RowDefinition Height="20" />
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="auto" />
                </Grid.ColumnDefinitions>

                <Button
                    x:Name="btnRunOnce"
                    Height="30"
                    Command="{Binding RunOnceCommand}"
                    Content="&#xe67b;"
                    Foreground="Lime"
                    Style="{StaticResource ProcessBarButtonStyle}"
                    ToolTip="当前项目单次执行" />
                <Button
                    x:Name="btnRunCycle"
                    Grid.Column="1"
                    Height="30"
                    Command="{Binding RunCycleCommand}"
                    Content="&#xe612;"
                    Foreground="Lime"
                    Style="{StaticResource ProcessBarButtonStyle}"
                    ToolTip="当前项目循环执行" />
                <Button
                    x:Name="btnStop"
                    Grid.Column="2"
                    Height="30"
                    Command="{Binding StopCommand}"
                    Content="&#xe8fa;"
                    Foreground="Red"
                    Style="{StaticResource ProcessBarButtonStyle}"
                    ToolTip="停止当前项目" />
                <CheckBox
                    x:Name="ck_IsRunning"
                    Grid.Column="3"
                    Margin="10"
                    Content="是否启用运行模式"
                    IsChecked="{Binding IsRunning}" />
            </Grid>
        </StackPanel>

        <DockPanel Grid.Row="1" LastChildFill="True">

            <TreeView
                AllowDrop="True"
                Cursor=""
                DragLeave="toolTree_DragLeave"
                DragOver="toolTree_DragOver"
                Drop="toolTree_Drop"
                GiveFeedback="toolTree_GiveFeedback"
                ItemsSource="{Binding Project.ToolList,Mode=OneWay}"
                MouseDoubleClick="toolTree_PreviewMouseDoubleClick"
                MouseMove="toolTree_MouseMove"
                Name="toolTree"
                PreviewKeyDown="toolTree_KeyDown"
                PreviewMouseDoubleClick="toolTree_PreviewMouseDoubleClick"
                PreviewMouseLeftButtonDown="toolTree_PreviewMouseLeftButtonDown"
                PreviewMouseLeftButtonUp="ToolTree_PreviewMouseLeftButtonUp"
                PreviewMouseRightButtonDown="toolTree_PreviewMouseRightButtonDown"
                PreviewMouseUp="toolTree_PreviewMouseUp"
                SelectedItemChanged="toolTree_SelectedItemChanged"
                Style="{StaticResource ProcessTreeView}"
                VirtualizingStackPanel.IsVirtualizing="True"
                VirtualizingStackPanel.VirtualizationMode="Recycling">

                <TreeView.ContextMenu>

                    <ContextMenu FontFamily="{StaticResource iconfont}">
                        <ContextMenu.Resources>
                            <Style TargetType="MenuItem">
                                <Setter Property="IsEnabled" Value="{ Binding Source={StaticResource IsEnableControl}, Path=Ins.Stop, Converter={StaticResource InvertBoolConverter}}" />
                            </Style>
                        </ContextMenu.Resources>
                        <MenuItem
                            Click="miRename_Click"
                            Header="重命名"
                            Icon="&#xe6e3;" />
                        <MenuItem
                            Click="miEditRemarks_Click"
                            Header="编辑注释"
                            Icon="&#xe61e;" />
                        <Separator />
                        <MenuItem
                            Click="miExcuteSelectedTool_Click"
                            Header="执行选中模块"
                            Icon="&#xe67b;" />
                        <MenuItem
                            Click="miExcuteMultiTool_Click"
                            Header="从此处执行"
                            Icon="&#xe63d;" />
                        <!--<MenuItem Header="显示所有" Icon="&#xe62b;"/>-->
                        <!--<MenuItem Header="启用超级工具" Icon="&#xe65d;"/>-->
                        <!--<Separator/>-->
                        <MenuItem
                            Click="miEnableBreakPoint_Click"
                            Header="设置断点"
                            Icon="&#xe62c;" />
                        <!--<MenuItem Header="模块参数" Icon="&#xe652;"/>-->
                        <Separator />
                        <!--<MenuItem Header="剪切" Icon="&#xe61b;" Click="miCut_Click"/>-->
                        <MenuItem
                            Click="miCopy_Click"
                            Header="复制"
                            Icon="&#xe623;" />
                        <MenuItem
                            Click="miPaste_Click"
                            Header="粘贴"
                            Icon="&#xe624;" />
                        <MenuItem
                            Click="miDisable_Click"
                            Header="禁用"
                            Icon="&#xe627;" />
                        <MenuItem
                            Click="miDeleteTool_Click"
                            Header="删除模块"
                            Icon="&#xe8b6;" />

                    </ContextMenu>
                </TreeView.ContextMenu>



                <TreeView.ItemTemplate>

                    <HierarchicalDataTemplate ItemsSource="{Binding ToolList, Mode=OneWay}">
                        <Grid
                            Width="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Grid}}, Path= Width}"
                            Background="{Binding IsRunning, Mode=OneWay,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource Bool2LimeConverter}}"
                            IsEnabled="{Binding IsUse}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="14" />
                                <ColumnDefinition Width="18" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="auto" MinWidth="60" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition />
                            </Grid.RowDefinitions>


                            <Border
                                Width="11"
                                Height="11"
                                Background="Red"
                                CornerRadius="6"
                                Visibility="{Binding  IsEnableBreakPoint,Converter={StaticResource Bool2VisibilityHiddenConverter}}" />
                            <Image
                                Grid.Column="1"
                                Width="18"
                                Height="18"
                                MouseEnter="Image_MouseEnter"
                                MouseLeave="Image_MouseLeave"
                                PreviewDragOver="Image_PreviewDragOver"
                                Source="{Binding IconImage}" />

                            <StackPanel
                                Grid.Column="2"
                                VerticalAlignment="Center"
                                Orientation="Horizontal"
                                PreviewDragOver="Tool_PreviewDragOver">
                                <TextBlock Margin="10,0,0,0" VerticalAlignment="Center">
                                    <Run Text="{Binding Encode}" />
                                    <Run Text="." />
                                    <Run Text="{Binding Name}" />
                                </TextBlock>
                                <TextBlock
                                    Margin="10,0,0,0"
                                    VerticalAlignment="Center"
                                    Foreground="Yellow"
                                    Text="{Binding Remarks,Mode=TwoWay}" />
                            </StackPanel>



                            <Grid
                                Grid.Column="3"
                                HorizontalAlignment="Right"
                                PreviewDragOver="Tool_PreviewDragOver">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition />
                                    <ColumnDefinition Width="35" />
                                </Grid.ColumnDefinitions>

                                <TextBlock
                                    Margin="3 0"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center">
                                    <Run Text="{Binding ConsumingTime}" />
                                    <Run Text="ms" />
                                </TextBlock>


                                <mah:ProgressRing
                                    Grid.Column="1"
                                    Width="20"
                                    Height="20"
                                    Foreground="Yellow"
                                    Padding="5"
                                    Visibility="{Binding IsRunning,Mode=OneWay,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource Bool2VisibilityConverter}}" />

                                <TextBlock
                                    Grid.Column="1"
                                    Margin="2 0 0 0"
                                    VerticalAlignment="Center"
                                    FontFamily="{StaticResource iconfont}"
                                    Foreground="{Binding StatusResultColor}"
                                    Text="{Binding StatusImage,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />

                            </Grid>

                            <Grid
                                Grid.Row="1"
                                Grid.ColumnSpan="5"
                                Height="{Binding DragOverHeight}"
                                Margin="0" />
                        </Grid>
                    </HierarchicalDataTemplate>
                </TreeView.ItemTemplate>
            </TreeView>

        </DockPanel>

        <TextBlock
            Grid.Row="2"
            Margin="3 0"
            HorizontalAlignment="Right"
            VerticalAlignment="Center">
            <Run Text="{Binding Project.ConsumingTime}" />
            <Run Text="ms" />
        </TextBlock>
    </Grid>
</UserControl>