using MoonLight.UI.Framework.Commands;
using MoonLight.UI.Framework.Services;
using System.ComponentModel.Composition;
using System.Threading.Tasks;
using MoonLight.Modules.DeviceManager.Views;
using MoonLight.UI.Framework.Threading;

namespace MoonLight.Modules.DeviceManager.Commands
{
    [CommandHandler]
    public class OpenDeviceManagerCommandHandler : CommandHandlerBase<OpenDeviceManagerCommandDefinition>
    {

        private readonly IWindowManager _windowManager;

        [ImportingConstructor]
        public OpenDeviceManagerCommandHandler(IWindowManager windowManager)
        {
            _windowManager = windowManager;
        }

        public override async Task Run(Command command)
        {
            var view = new DeviceManagerView();
            view.ShowDialog();
            await TaskUtility.Completed;
            //await _windowManager.ShowDialogAsync(new DeviceManagerView());
        }
    }
}
