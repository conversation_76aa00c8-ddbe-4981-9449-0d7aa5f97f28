﻿using EventMgrLib;
using Microsoft.Win32;
using MoonLight.Core.Common.Helper;
using MoonLight.Core.Common.Log;
using MoonLight.Core.Enums;
using MoonLight.Core.Events;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Core.Services;
using MoonLight.UI.Framework.Services;
using System;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.Windows.Controls;
using System.Windows.Forms.Integration;

namespace MoonLight.App.ViewModels
{
    internal class TestViewModel : NotifyPropertyBase
    {
        public IRenderView RenderView { get; set; }

        //public ObservableCollection<Solution> Solutions { get; set; } = SolManager.Instance.Solutions;

        public ObservableCollection<Project> Projects { get; set; } = new ObservableCollection<Project>();

        private string _SelectedSolPath;
        public string SelectedSolPath
        {
            get { return _SelectedSolPath; }
            set { Set(ref _SelectedSolPath, value); }
        }

        private bool _IsEnableSelectedSol = false;
        public bool IsEnableSelectedSol
        {
            get { return _IsEnableSelectedSol; }
            set { Set(ref _IsEnableSelectedSol, value); }
        }

        private Solution _SelectedSol = null;
        public Solution SelectedSol
        {
            get { return _SelectedSol; }
            set { Set(ref _SelectedSol, value); }
        }

        private Project _SelectedProject = null;
        public Project SelectedProject
        {
            get { return _SelectedProject; }
            set { Set(ref _SelectedProject, value); }
        }

        private ToolUnit _SelectedTool = null;
        public ToolUnit SelectedTool
        {
            get { return _SelectedTool; }
            set
            {
                if (value == null)
                {
                    PropertyControl = null;

                    SelectedTool?.ClearWindow(RenderView);
                    return;
                }
                value.IsEdit = IsEnabled;
                Set(ref _SelectedTool, value);
                PropertyControl = SelectedTool.GetUserControl();
                SelectedTool.Loaded(RenderView, PropertyControl);
            }
        }

        private bool _IsEnabled = true;
        public bool IsEnabled
        {
            get { return _IsEnabled; }
            set
            {
                Set(ref _IsEnabled, value);
                if (SelectedTool != null)
                {
                    SelectedTool.IsEdit = IsEnabled;
                    SelectedTool.ShowHRoi();
                }
            }
        }

        private double _CrossX;
        public double CrossX
        {
            get { return _CrossX; }
            set { Set(ref _CrossX, value); }
        }

        private double _CrossY;
        public double CrossY
        {
            get { return _CrossY; }
            set { Set(ref _CrossY, value); }
        }

        private UserControl _PropertyControl;
        public UserControl PropertyControl
        {
            get { return _PropertyControl; }
            set { Set(ref _PropertyControl, value); }
        }

        private WindowsFormsHost _VisionControl;
        public WindowsFormsHost VisionControl
        {
            get { return _VisionControl; }
            set { Set(ref _VisionControl, value); }
        }

        internal void UpdateProjects()
        {
            Projects.Clear();
            foreach (var project in SelectedSol.ProjectList)
            {
                Projects.Add(project);
            }
            SelectedProject = SelectedSol.CurrentProject;
            SelectedProject.ProjectOutputChangedEvent -= ProjectOutput;
            SelectedProject.ProjectOutputChangedEvent += ProjectOutput;
        }

        

        [NonSerialized]
        private CommandBase _LoadedCommand;
        public CommandBase LoadedCommand
        {
            get
            {
                if (_LoadedCommand == null)
                {
                    _LoadedCommand = new CommandBase((obj) =>
                    {
                        if(RenderView == null)
                        {
                            RenderView = IoC.Get<IRenderViewManager>().GenRenderView();
                            //VisionControl = new WindowsFormsHost() { Child = (System.Windows.Forms.Control)RenderView };  
                        }
                    });
                }
                return _LoadedCommand;
            }
        }

        private void ProjectOutput(ConcurrentDictionary<string, string> data)
        {
            if(data == null)
            {
                return;
            }
            CrossX = Convert.ToDouble(data["&线线构建.交点X"]);
            CrossY = Convert.ToDouble(data["&线线构建.交点Y"]);
        }


        [NonSerialized]
        private CommandBase _LoadPathCommand;
        public CommandBase LoadPathCommand
        {
            get
            {
                if (_LoadPathCommand == null)
                {
                    _LoadPathCommand = new CommandBase((obj) =>
                    {
                        OpenFileDialog openFileDialog = new OpenFileDialog();
                        openFileDialog.Filter = "解决方案 (*.vm)|*.vm";
                        openFileDialog.DefaultExt = "vm";
                        if (openFileDialog.ShowDialog() == true)
                        {
                            try
                            {
                                Solution.Ins.Load(openFileDialog.FileName);
                                // 设置显示窗口个数
                                IoC.Get<IRenderViewManager>().SetVieMode(Solution.Ins.ViewMode);
                                // 更新流程栏
                                IoC.Get<IFlow>().UpdateTree();
                                // 更新运行栏
                                IoC.Get<IProcess>().UpdateTree();
                                // 更改标题
                                IoC.Get<IMainWindow>().UpdateTitle(Solution.Ins.Name, openFileDialog.FileName);

                                SelectedSolPath = openFileDialog.FileName;
                                SelectedSol = Solution.Ins;
                                UpdateProjects();

                                IsEnableSelectedSol = true;
                            }
                            catch (Exception ex)
                            {
                                Logger.AddLog($"解决方案加载失败！{ex.Message}", MsgType.Error);
                            }
                        }
                    });
                }
                return _LoadPathCommand;
            }
        }

        [NonSerialized]
        private CommandBase _SaveSolCommand;
        public CommandBase SaveSolCommand
        {
            get
            {
                if (_SaveSolCommand == null)
                {
                    _SaveSolCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            if (Solution.Ins.Save())
                            {
                                Logger.AddLog("解决方案保存成功！", MsgType.Success, isDispGrowl: true);
                            }
                            else
                            {
                                Logger.AddLog("解决方案保存失败！", MsgType.Error);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"解决方案保存失败！{ex.Message}", MsgType.Error);
                        }
                    });
                }
                return _SaveSolCommand;
            }
        }

        [NonSerialized]
        private CommandBase _DeleteProjectCommand;
        public CommandBase DeleteProjectCommand
        {
            get
            {
                if (_DeleteProjectCommand == null)
                {
                    _DeleteProjectCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            if (SelectedProject == null) return;
                            bool res = SelectedSol.DeleteProjectByName(SelectedProject.ProjectInfo.ProcessName);
                            if (!res)
                            {
                                Logger.AddLog("删除失败");
                            }
                            else
                            {
                                UpdateProjects();
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"删除失败！{ex.Message}", MsgType.Error);
                        }
                    });
                }
                return _DeleteProjectCommand;
            }
        }

        [NonSerialized]
        private CommandBase _MatchCommand;
        public CommandBase MatchCommand
        {
            get
            {
                if (_MatchCommand == null)
                {
                    _MatchCommand = new CommandBase((obj) =>
                    {
                        ToolUnit match = SelectedProject.GetToolByName("模板匹配");
                        SelectedTool = match;
                    });
                }
                return _MatchCommand;
            }
        }

        [NonSerialized]
        private CommandBase _ExecuteCommand;
        public CommandBase ExecuteCommand
        {
            get
            {
                if (_ExecuteCommand == null)
                {
                    _ExecuteCommand = new CommandBase((obj) =>
                    {
                        //SelectedProject.ExcuteOneToolByName(SelectedTool.Name);
                        SelectedTool.InternalRun();
                        SelectedTool.ShowHRoi();
                    });
                }
                return _ExecuteCommand;
            }
        }

        [NonSerialized]
        private CommandBase _FindLineCommand;
        public CommandBase FindLineCommand
        {
            get
            {
                if (_FindLineCommand == null)
                {
                    _FindLineCommand = new CommandBase((obj) =>
                    {
                        ToolUnit findLine = SelectedProject.GetToolByName("直线工具");
                        SelectedTool = findLine;
                    });
                }
                return _FindLineCommand;
            }
        }

        [NonSerialized]
        private CommandBase _FindLine1Command;
        public CommandBase FindLine1Command
        {
            get
            {
                if (_FindLine1Command == null)
                {
                    _FindLine1Command = new CommandBase((obj) =>
                    {
                        ToolUnit findLine = SelectedProject.GetToolByName("直线工具1");
                        SelectedTool = findLine;
                    });
                }
                return _FindLine1Command;
            }
        }


        [NonSerialized]
        private CommandBase _RunOnceCommand;
        public CommandBase RunOnceCommand
        {
            get
            {
                if (_RunOnceCommand == null)
                {
                    _RunOnceCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            if (SelectedProject == null) return;
                            SelectedSol.ExecuteOnce(SelectedProject.ProjectInfo.ProcessName);
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"执行一次解决方案失败,{ex.Message}");
                        }
                    });
                }
                return _RunOnceCommand;
            }
        }

        [NonSerialized]
        private CommandBase _StartRunCommand;
        public CommandBase StartRunCommand
        {
            get
            {
                if (_StartRunCommand == null)
                {
                    _StartRunCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            if (SelectedProject == null) return;
                            SelectedSol.StartRun(SelectedProject.ProjectInfo.ProcessName);
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"循环执行解决方案失败,{ex.Message}");
                        }
                    });
                }
                return _StartRunCommand;
            }
        }

        [NonSerialized]
        private CommandBase _StopRunCommand;
        public CommandBase StopRunCommand
        {
            get
            {
                if (_StopRunCommand == null)
                {
                    _StopRunCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            if (SelectedProject == null) return;
                            SelectedSol.StopRun(SelectedProject.ProjectInfo.ProcessName);
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"停止解决方案失败,{ex.Message}");
                        }
                    });
                }
                return _StopRunCommand;
            }
        }
    }
}
