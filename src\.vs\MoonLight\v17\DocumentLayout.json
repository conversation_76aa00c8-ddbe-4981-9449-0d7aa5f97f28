{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|c:\\users\\<USER>\\desktop\\视觉平台优化\\moonlight.platform_v2_20150514\\src\\moonlight.core\\assets\\modules\\rendercontrol\\managers\\rendermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\rendercontrol\\managers\\rendermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\algorithms\\visionlib.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\algorithms\\visionlib.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ACFCE13D-24B6-432C-AF63-9E061041A935}|MoonLight.Modules.BuildLl\\MoonLight.Modules.BuildLl.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.buildll\\viewmodels\\buildllviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{ACFCE13D-24B6-432C-AF63-9E061041A935}|MoonLight.Modules.BuildLl\\MoonLight.Modules.BuildLl.csproj|solutionrelative:moonlight.modules.buildll\\viewmodels\\buildllviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\views\\devicemanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\views\\devicemanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\models\\devicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\models\\devicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\viewmodels\\devicemanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\viewmodels\\devicemanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\views\\devicemanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\views\\devicemanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\models\\idevicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\models\\idevicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\viewmodels\\devicetemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\viewmodels\\devicetemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.app.demo\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|solutionrelative:moonlight.app.demo\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.app.demo\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|solutionrelative:moonlight.app.demo\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\cameraviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\cameraviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\daheng\\cameradaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\daheng\\cameradaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\camera\\camerabase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\camera\\camerabase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\cameraview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\cameraview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\commands\\opendevicemanagercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\commands\\opendevicemanagercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.grabimage\\viewmodels\\grabimageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|solutionrelative:moonlight.modules.grabimage\\viewmodels\\grabimageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\hik\\camerahik.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\hik\\camerahik.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\balser\\camerabasler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\balser\\camerabasler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\services\\pluginservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\services\\pluginservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\toolbar\\toolbardefinitions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\toolbar\\toolbardefinitions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.grabimage\\views\\grabimageview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|solutionrelative:moonlight.modules.grabimage\\views\\grabimageview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\events\\addcameraevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\events\\addcameraevent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\services\\solution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\services\\solution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\styles\\converter.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\styles\\converter.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\camera\\icamera.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\camera\\icamera.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\devicebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\devicebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\configbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\configbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\cameraview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\cameraview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\idevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\idevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D4DE70DA-1FC1-478C-8781-8BF7EA98B7A3}|MoonLight.Modules.MetricToolkit\\MoonLight.Modules.MetricToolkit.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.metrictoolkit\\views\\metrictoolkitview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{D4DE70DA-1FC1-478C-8781-8BF7EA98B7A3}|MoonLight.Modules.MetricToolkit\\MoonLight.Modules.MetricToolkit.csproj|solutionrelative:moonlight.modules.metrictoolkit\\views\\metrictoolkitview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A460D352-B73E-468F-B57D-B87269C197A3}|MoonLight.Modules.SendStr\\MoonLight.Modules.SendStr.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.sendstr\\viewmodels\\sendstrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A460D352-B73E-468F-B57D-B87269C197A3}|MoonLight.Modules.SendStr\\MoonLight.Modules.SendStr.csproj|solutionrelative:moonlight.modules.sendstr\\viewmodels\\sendstrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{50DB1135-5091-4644-9416-002CD4E7BC80}|MoonLight.Modules.ReceiveStr\\MoonLight.Modules.ReceiveStr.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.receivestr\\viewmodels\\receivestrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50DB1135-5091-4644-9416-002CD4E7BC80}|MoonLight.Modules.ReceiveStr\\MoonLight.Modules.ReceiveStr.csproj|solutionrelative:moonlight.modules.receivestr\\viewmodels\\receivestrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\menudefinitions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\menudefinitions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\flowmanager\\commands\\viewflowcommanddefinition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\flowmanager\\commands\\viewflowcommanddefinition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\posmanager\\commands\\openposmanagercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\posmanager\\commands\\openposmanagercommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{853B9914-2808-42E6-9483-F5E8AF91778D}|MoonLight.Modules.MeasureCircle\\MoonLight.Modules.MeasureCircle.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measurecircle\\views\\measurecircleview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{853B9914-2808-42E6-9483-F5E8AF91778D}|MoonLight.Modules.MeasureCircle\\MoonLight.Modules.MeasureCircle.csproj|solutionrelative:moonlight.modules.measurecircle\\views\\measurecircleview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{22F4163F-6B5D-487D-9C2E-972742DEB665}|MoonLight.Modules.CoordinateMap\\MoonLight.Modules.CoordinateMap.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.coordinatemap\\views\\coordinatemapview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{22F4163F-6B5D-487D-9C2E-972742DEB665}|MoonLight.Modules.CoordinateMap\\MoonLight.Modules.CoordinateMap.csproj|solutionrelative:moonlight.modules.coordinatemap\\views\\coordinatemapview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{D869379C-0349-4D10-9FD4-EC3CEE4600ED}|MoonLight.Modules.ProjectOutput\\MoonLight.Modules.ProjectOutput.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.projectoutput\\views\\projectoutputview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{D869379C-0349-4D10-9FD4-EC3CEE4600ED}|MoonLight.Modules.ProjectOutput\\MoonLight.Modules.ProjectOutput.csproj|solutionrelative:moonlight.modules.projectoutput\\views\\projectoutputview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{925A877D-946E-42B9-B1D8-3D18174EC92B}|MoonLight.Modules.MeasureLines\\MoonLight.Modules.MeasureLines.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measurelines\\views\\measurelinesview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{925A877D-946E-42B9-B1D8-3D18174EC92B}|MoonLight.Modules.MeasureLines\\MoonLight.Modules.MeasureLines.csproj|solutionrelative:moonlight.modules.measurelines\\views\\measurelinesview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{99762013-03CE-4C6B-A7AB-166DC7577F94}|MoonLight.Modules.MeasureLine\\MoonLight.Modules.MeasureLine.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measureline\\views\\measurelineview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{99762013-03CE-4C6B-A7AB-166DC7577F94}|MoonLight.Modules.MeasureLine\\MoonLight.Modules.MeasureLine.csproj|solutionrelative:moonlight.modules.measureline\\views\\measurelineview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\commands\\opendevicemanagercommanddefinition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\commands\\opendevicemanagercommanddefinition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\models\\devicecategorygroup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\models\\devicecategorygroup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.g.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.grabimage\\views\\grabimagepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|solutionrelative:moonlight.modules.grabimage\\views\\grabimagepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{9B4DFB06-26BB-4D61-8BC8-407DAE95A9DA}|MoonLight.Modules.If\\MoonLight.Modules.If.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.if\\views\\ifpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{9B4DFB06-26BB-4D61-8BC8-407DAE95A9DA}|MoonLight.Modules.If\\MoonLight.Modules.If.csproj|solutionrelative:moonlight.modules.if\\views\\ifpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\flowmanager\\views\\flowtreeview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\flowmanager\\views\\flowtreeview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.app.demo\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|solutionrelative:moonlight.app.demo\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\posmanager\\views\\posmanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\posmanager\\views\\posmanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\services\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\services\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\settings\\models\\sysconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\settings\\models\\sysconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\statusbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\statusbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\services\\engineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\services\\engineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\converter\\bool2greencolorconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\converter\\bool2greencolorconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\flowmanager\\viewmodels\\propertypanelviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\flowmanager\\viewmodels\\propertypanelviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\flowmanager\\viewmodels\\runmanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\flowmanager\\viewmodels\\runmanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\framework\\relaycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\framework\\relaycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\daheng\\statusdaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\daheng\\statusdaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\devicetype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\devicetype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\motion\\icard.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\motion\\icard.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\configdevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\configdevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\motion\\cardbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\motion\\cardbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\communication\\communicationbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\communication\\communicationbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\defines\\cameratype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\defines\\cameratype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\defines\\rotateangle.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\defines\\rotateangle.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\camera\\statuscamera.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\camera\\statuscamera.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\models\\toolunit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\models\\toolunit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D4DE70DA-1FC1-478C-8781-8BF7EA98B7A3}|MoonLight.Modules.MetricToolkit\\MoonLight.Modules.MetricToolkit.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.metrictoolkit\\viewmodels\\metrictoolkitviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D4DE70DA-1FC1-478C-8781-8BF7EA98B7A3}|MoonLight.Modules.MetricToolkit\\MoonLight.Modules.MetricToolkit.csproj|solutionrelative:moonlight.modules.metrictoolkit\\viewmodels\\metrictoolkitviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C9AEC89A-77D4-408E-B97B-02ADDCADA342}|MoonLight.Modules.Blob\\MoonLight.Modules.Blob.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.blob\\viewmodels\\blobviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C9AEC89A-77D4-408E-B97B-02ADDCADA342}|MoonLight.Modules.Blob\\MoonLight.Modules.Blob.csproj|solutionrelative:moonlight.modules.blob\\viewmodels\\blobviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C9AEC89A-77D4-408E-B97B-02ADDCADA342}|MoonLight.Modules.Blob\\MoonLight.Modules.Blob.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.blob\\models\\dealresultmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C9AEC89A-77D4-408E-B97B-02ADDCADA342}|MoonLight.Modules.Blob\\MoonLight.Modules.Blob.csproj|solutionrelative:moonlight.modules.blob\\models\\dealresultmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C9AEC89A-77D4-408E-B97B-02ADDCADA342}|MoonLight.Modules.Blob\\MoonLight.Modules.Blob.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.blob\\models\\binarizationparam.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C9AEC89A-77D4-408E-B97B-02ADDCADA342}|MoonLight.Modules.Blob\\MoonLight.Modules.Blob.csproj|solutionrelative:moonlight.modules.blob\\models\\binarizationparam.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.grabimage\\models\\imagenamemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|solutionrelative:moonlight.modules.grabimage\\models\\imagenamemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\rois\\roirectangle2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\rois\\roirectangle2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\rois\\roirectcaliper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\rois\\roirectcaliper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\models\\visionresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\models\\visionresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\common\\helper\\imagetool.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\common\\helper\\imagetool.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\flowmanager\\views\\runmanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\flowmanager\\views\\runmanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.app.demo\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|solutionrelative:moonlight.app.demo\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6169DFDA-5242-4AAB-8557-1FFA13A12984}|MoonLight.Module.Matching\\MoonLight.Modules.Matching.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.module.matching\\viewmodels\\matchingviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6169DFDA-5242-4AAB-8557-1FFA13A12984}|MoonLight.Module.Matching\\MoonLight.Modules.Matching.csproj|solutionrelative:moonlight.module.matching\\viewmodels\\matchingviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\rendercontrol\\views\\renderview.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\rendercontrol\\views\\renderview.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\flowmanager\\views\\flowtreeview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\flowmanager\\views\\flowtreeview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\common\\helper\\filepaths.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\common\\helper\\filepaths.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\toolbox\\views\\toolboxview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\toolbox\\views\\toolboxview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\toolbox\\commands\\viewtoolboxcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\toolbox\\commands\\viewtoolboxcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\toolbox\\commands\\viewtoolboxcommanddefinition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\toolbox\\commands\\viewtoolboxcommanddefinition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\toolbox\\viewmodels\\toolboxviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\toolbox\\viewmodels\\toolboxviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\flowmanager\\views\\runmanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\flowmanager\\views\\runmanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\toolbar\\commands\\addsolutioncommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\toolbar\\commands\\addsolutioncommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9DEDB416-9028-403C-8D4D-EDB08034FE78}|MoonLight.Modules.SaveImage\\MoonLight.Modules.SaveImage.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.saveimage\\viewmodels\\saveimageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9DEDB416-9028-403C-8D4D-EDB08034FE78}|MoonLight.Modules.SaveImage\\MoonLight.Modules.SaveImage.csproj|solutionrelative:moonlight.modules.saveimage\\viewmodels\\saveimageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "BuildLlViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.BuildLl\\ViewModels\\BuildLlViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.BuildLl\\ViewModels\\BuildLlViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.BuildLl\\ViewModels\\BuildLlViewModel.cs", "RelativeToolTip": "MoonLight.Modules.BuildLl\\ViewModels\\BuildLlViewModel.cs", "ViewState": "AgIAAMsBAAAAAAAAAAAwwNgBAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T11:52:09.882Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "RenderManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\RenderControl\\Managers\\RenderManager.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\RenderControl\\Managers\\RenderManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\RenderControl\\Managers\\RenderManager.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\RenderControl\\Managers\\RenderManager.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAewFMAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T11:23:58.255Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DeviceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "ViewState": "AgIAAK0AAAAAAAAAAAAewLkAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T09:40:59.397Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DeviceManagerViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAewDoAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T03:41:38.658Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "DeviceManagerView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-13T09:39:56.873Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "DeviceManagerView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml.cs", "ViewState": "AgIAAG8AAAAAAAAAAAAqwHoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:31:39.499Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "VisionLib.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAtwDoAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T01:32:57.443Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "App.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\App.xaml.cs", "RelativeDocumentMoniker": "MoonLight.App.Demo\\App.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\App.xaml.cs", "RelativeToolTip": "MoonLight.App.Demo\\App.xaml.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAUwIEAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:10:45.142Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "DeviceTemplate.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceTemplate.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceTemplate.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceTemplate.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceTemplate.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T09:40:52.76Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "IDeviceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABUAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T11:32:00.06Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "MoonLight.App.Demo\\MainWindowViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindowViewModel.cs", "RelativeToolTip": "MoonLight.App.Demo\\MainWindowViewModel.cs", "ViewState": "AgIAAJUAAAAAAAAAAAAmwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T09:47:55.317Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "CameraViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\CameraViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraViewModel.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\CameraViewModel.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAAAGMAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T13:19:43.459Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "CameraBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\CameraBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Camera\\CameraBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\CameraBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Camera\\CameraBase.cs", "ViewState": "AgIAAKwAAAAAAAAAAAA7wLcAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:41:12.596Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "CameraView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraView.xaml", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\CameraView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraView.xaml", "RelativeToolTip": "MoonLight.Devices.Camera\\CameraView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T01:42:39.797Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "OpenDeviceManagerCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandHandler.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandHandler.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandHandler.cs", "ViewState": "AgIAABAAAAAAAAAAAAAnwBsAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:37:03.085Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "ToolBarDefinitions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBar\\ToolBarDefinitions.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\ToolBar\\ToolBarDefinitions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBar\\ToolBarDefinitions.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\ToolBar\\ToolBarDefinitions.cs", "ViewState": "AgIAABoAAAAAAAAAAAAvwCgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:00:59.742Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "CameraDaheng.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "ViewState": "AgIAACEAAAAAAAAAAAAzwC0AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:08:35.596Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "GrabImageViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "RelativeToolTip": "MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "ViewState": "AgIAAMcBAAAAAAAAAAAcwNYBAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T03:33:51.328Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "CameraHIK.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "ViewState": "AgIAAM0AAAAAAAAAAAArwNIAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T06:56:54.689Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "CameraBasler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Balser\\CameraBasler.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\Balser\\CameraBasler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Balser\\CameraBasler.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\Balser\\CameraBasler.cs", "ViewState": "AgIAAJ8AAAAAAAAAAAAUwKYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T09:17:50.948Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "GrabImageView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Views\\GrabImageView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.GrabImage\\Views\\GrabImageView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Views\\GrabImageView.xaml", "RelativeToolTip": "MoonLight.Modules.GrabImage\\Views\\GrabImageView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T09:15:07.619Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "PluginService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\PluginService.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Services\\PluginService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\PluginService.cs", "RelativeToolTip": "MoonLight.Core\\Services\\PluginService.cs", "ViewState": "AgIAAGEAAAAAAAAAAAAvwDoAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T02:40:33.851Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "AddCameraEvent.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Events\\AddCameraEvent.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Events\\AddCameraEvent.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Events\\AddCameraEvent.cs", "RelativeToolTip": "MoonLight.Core\\Events\\AddCameraEvent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAYAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T09:14:36.37Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "Converter.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Styles\\Converter.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Styles\\Converter.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Styles\\Converter.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Styles\\Converter.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T02:29:08.753Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "Solution.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Solution.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Services\\Solution.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Solution.cs", "RelativeToolTip": "MoonLight.Core\\Services\\Solution.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAvwD0AAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:57:25.646Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ICamera.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\ICamera.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Camera\\ICamera.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\ICamera.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Camera\\ICamera.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAuwFcAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:38:29.352Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "DeviceBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\DeviceBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\DeviceBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\DeviceBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\DeviceBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA4AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:35:56.018Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "ConfigBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\ConfigBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\ConfigBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA8AAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:37:13.491Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "CameraView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\CameraView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraView.xaml.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\CameraView.xaml.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAMwBkAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:27:22.391Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "IDevice.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\IDevice.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\IDevice.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\IDevice.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\IDevice.cs", "ViewState": "AgIAABsAAAAAAAAAAAAtwCMAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:34:58.878Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "MeasureCircleView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureCircle\\Views\\MeasureCircleView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MeasureCircle\\Views\\MeasureCircleView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureCircle\\Views\\MeasureCircleView.xaml", "RelativeToolTip": "MoonLight.Modules.MeasureCircle\\Views\\MeasureCircleView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T05:46:54.008Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "CoordinateMapView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapView.xaml", "RelativeToolTip": "MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T05:46:19.998Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "ViewFlowCommandDefinition.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Commands\\ViewFlowCommandDefinition.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Commands\\ViewFlowCommandDefinition.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Commands\\ViewFlowCommandDefinition.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Commands\\ViewFlowCommandDefinition.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:47:29.65Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "MenuDefinitions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAAwAcAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:48:21.671Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "MetricToolkitView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitView.xaml", "RelativeToolTip": "MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T05:50:39.222Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "ReceiveStrViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "RelativeToolTip": "MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "ViewState": "AgIAACIAAAAAAAAAAAAYwDEAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:49:48.477Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "SendStrViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "RelativeToolTip": "MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "ViewState": "AgIAAEIAAAAAAAAAAAA9wFIAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:50:15.136Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "ProjectOutputView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ProjectOutput\\Views\\ProjectOutputView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.ProjectOutput\\Views\\ProjectOutputView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ProjectOutput\\Views\\ProjectOutputView.xaml", "RelativeToolTip": "MoonLight.Modules.ProjectOutput\\Views\\ProjectOutputView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T05:46:13.458Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "OpenPosManagerCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\PosManager\\Commands\\OpenPosManagerCommandHandler.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\PosManager\\Commands\\OpenPosManagerCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\PosManager\\Commands\\OpenPosManagerCommandHandler.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\PosManager\\Commands\\OpenPosManagerCommandHandler.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABkAAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:36:45.058Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "MeasureLinesView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLines\\Views\\MeasureLinesView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MeasureLines\\Views\\MeasureLinesView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLines\\Views\\MeasureLinesView.xaml", "RelativeToolTip": "MoonLight.Modules.MeasureLines\\Views\\MeasureLinesView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T05:46:05.713Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "MeasureLineView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "RelativeToolTip": "MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T05:45:58.379Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "OpenDeviceManagerCommandDefinition.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandDefinition.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandDefinition.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandDefinition.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Commands\\OpenDeviceManagerCommandDefinition.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:43:15.606Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "DeviceCategoryGroup.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T13:58:17.223Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "DeviceManagerView.g.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.g.cs", "RelativeDocumentMoniker": "..\\..\\MoonLight.Platform_0506\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.g.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.g.cs", "RelativeToolTip": "..\\..\\MoonLight.Platform_0506\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.g.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAvwBgAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:36:52.47Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "GrabImagePropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T03:38:12.928Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "IfPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-12T14:01:19.727Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "FlowTreeView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-12T14:31:03.805Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "MainWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindow.xaml", "RelativeDocumentMoniker": "MoonLight.App.Demo\\MainWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindow.xaml", "RelativeToolTip": "MoonLight.App.Demo\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-13T00:32:49.207Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "PosManagerView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T05:39:40.158Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "Project.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Project.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Services\\Project.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Project.cs", "RelativeToolTip": "MoonLight.Core\\Services\\Project.cs", "ViewState": "AgIAAAkBAAAAAAAAAAAUwBEBAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:26:35.491Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "SysConfig.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Settings\\Models\\SysConfig.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\Settings\\Models\\SysConfig.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Settings\\Models\\SysConfig.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\Settings\\Models\\SysConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T03:40:41.152Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "StatusBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\StatusBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\StatusBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\StatusBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\StatusBase.cs", "ViewState": "AgIAABcAAAAAAAAAAIA8wCQAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:38:14.593Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "EngineService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\EngineService.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Services\\EngineService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\EngineService.cs", "RelativeToolTip": "MoonLight.Core\\Services\\EngineService.cs", "ViewState": "AgIAAMAAAAAAAAAAAAArwMsAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T00:33:08.151Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "Bool2GreenColorConverter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Converter\\Bool2GreenColorConverter.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Converter\\Bool2GreenColorConverter.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Converter\\Bool2GreenColorConverter.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Converter\\Bool2GreenColorConverter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T02:28:02.451Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "PropertyPanelViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\PropertyPanelViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\PropertyPanelViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\PropertyPanelViewModel.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\PropertyPanelViewModel.cs", "ViewState": "AgIAADwAAAAAAAAAAAAYwDAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T09:05:10.95Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "RunManagerViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\RunManagerViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\RunManagerViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\RunManagerViewModel.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\FlowManager\\ViewModels\\RunManagerViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:20:31.65Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "RelayCommand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\RelayCommand.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Framework\\RelayCommand.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\RelayCommand.cs", "RelativeToolTip": "MoonLight\\UI\\Framework\\RelayCommand.cs", "ViewState": "AgIAAAYAAAAAAAAAAAArwBQAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T01:37:25.852Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "StatusDaheng.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:15:55.529Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "DeviceType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\DeviceType.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\DeviceType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\DeviceType.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\DeviceType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T14:21:07.545Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "ConfigDevice.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigDevice.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\ConfigDevice.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigDevice.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\ConfigDevice.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAJgAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T13:50:21.392Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "ICard.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Motion\\ICard.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Motion\\ICard.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Motion\\ICard.cs", "RelativeToolTip": "MoonLight.Core\\Motion\\ICard.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T13:04:32.325Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "CommunicationBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAQwBAAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:51:51.142Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "CardBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Motion\\CardBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Motion\\CardBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Motion\\CardBase.cs", "RelativeToolTip": "MoonLight.Core\\Motion\\CardBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:51:33.532Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "CameraType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\CameraType.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Defines\\CameraType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\CameraType.cs", "RelativeToolTip": "MoonLight.Core\\Defines\\CameraType.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAgwBcAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:50:26.303Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "RotateAngle.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\RotateAngle.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Defines\\RotateAngle.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\RotateAngle.cs", "RelativeToolTip": "MoonLight.Core\\Defines\\RotateAngle.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAQwB8AAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:47:07.25Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "StatusCamera.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T12:42:56.286Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "ToolUnit.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Models\\ToolUnit.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Models\\ToolUnit.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Models\\ToolUnit.cs", "RelativeToolTip": "MoonLight.Core\\Models\\ToolUnit.cs", "ViewState": "AgIAALMBAAAAAAAAAAApwMQBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:13:22.027Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "MetricToolkitViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MetricToolkit\\ViewModels\\MetricToolkitViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.MetricToolkit\\ViewModels\\MetricToolkitViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MetricToolkit\\ViewModels\\MetricToolkitViewModel.cs", "RelativeToolTip": "MoonLight.Modules.MetricToolkit\\ViewModels\\MetricToolkitViewModel.cs", "ViewState": "AgIAAJsDAAAAAAAAAAAAwK8DAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T08:33:56.278Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "DealResultModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Blob\\Models\\DealResultModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.Blob\\Models\\DealResultModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Blob\\Models\\DealResultModel.cs", "RelativeToolTip": "MoonLight.Modules.Blob\\Models\\DealResultModel.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAowDIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:12:02.169Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "ROIRectangle2.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\ROIs\\ROIRectangle2.cs", "RelativeDocumentMoniker": "MoonLight.Core\\ROIs\\ROIRectangle2.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\ROIs\\ROIRectangle2.cs", "RelativeToolTip": "MoonLight.Core\\ROIs\\ROIRectangle2.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:10:04.062Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "ROIRectCaliper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\ROIs\\ROIRectCaliper.cs", "RelativeDocumentMoniker": "MoonLight.Core\\ROIs\\ROIRectCaliper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\ROIs\\ROIRectCaliper.cs", "RelativeToolTip": "MoonLight.Core\\ROIs\\ROIRectCaliper.cs", "ViewState": "AgIAALkAAAAAAAAAAAAgwMoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:09:55.952Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "VisionResult.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Models\\VisionResult.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Models\\VisionResult.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Models\\VisionResult.cs", "RelativeToolTip": "MoonLight.Core\\Models\\VisionResult.cs", "ViewState": "AgIAAEABAAAAAAAAAAAkwFUBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:09:38.181Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "BlobViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Blob\\ViewModels\\BlobViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.Blob\\ViewModels\\BlobViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Blob\\ViewModels\\BlobViewModel.cs", "RelativeToolTip": "MoonLight.Modules.Blob\\ViewModels\\BlobViewModel.cs", "ViewState": "AgIAALIBAAAAAAAAAAAgwLoBAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T08:32:55.724Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "BinarizationParam.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Blob\\Models\\BinarizationParam.cs", "RelativeDocumentMoniker": "MoonLight.Modules.Blob\\Models\\BinarizationParam.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Blob\\Models\\BinarizationParam.cs", "RelativeToolTip": "MoonLight.Modules.Blob\\Models\\BinarizationParam.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:11:52.3Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "ImageNameModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Models\\ImageNameModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.GrabImage\\Models\\ImageNameModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Models\\ImageNameModel.cs", "RelativeToolTip": "MoonLight.Modules.GrabImage\\Models\\ImageNameModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T06:12:08.82Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MoonLight.App.Demo\\MainWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindow.xaml.cs", "RelativeToolTip": "MoonLight.App.Demo\\MainWindow.xaml.cs", "ViewState": "AgIAACAAAAAAAAAAAAAuwD8AAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T05:41:10.945Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "RenderView.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\RenderControl\\Views\\RenderView.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\RenderControl\\Views\\RenderView.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\RenderControl\\Views\\RenderView.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\RenderControl\\Views\\RenderView.cs", "ViewState": "AgIAAHwEAAAAAAAAAAAjwIUEAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T01:04:00.618Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "ImageTool.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\ImageTool.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Common\\Helper\\ImageTool.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\ImageTool.cs", "RelativeToolTip": "MoonLight.Core\\Common\\Helper\\ImageTool.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAhwFoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T03:25:17.924Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "RunManagerView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml.cs", "ViewState": "AgIAADkAAAAAAAAAAAD4v0IAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:42:01.393Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "MatchingViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Module.Matching\\ViewModels\\MatchingViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Module.Matching\\ViewModels\\MatchingViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Module.Matching\\ViewModels\\MatchingViewModel.cs", "RelativeToolTip": "MoonLight.Module.Matching\\ViewModels\\MatchingViewModel.cs", "ViewState": "AgIAAK8CAAAAAAAAAAAQwLwCAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T01:03:21.242Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "FilePaths.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\FilePaths.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Common\\Helper\\FilePaths.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\FilePaths.cs", "RelativeToolTip": "MoonLight.Core\\Common\\Helper\\FilePaths.cs", "ViewState": "AgIAABsAAAAAAAAAAAAewCMAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:25:25.847Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "ToolBoxViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\ViewModels\\ToolBoxViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\ToolBox\\ViewModels\\ToolBoxViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\ViewModels\\ToolBoxViewModel.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\ToolBox\\ViewModels\\ToolBoxViewModel.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAAwA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:21:59.673Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "FlowTreeView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml.cs", "ViewState": "AgIAABkAAAAAAAAAAAAuwCQAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:53:12.145Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "ViewToolBoxCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandHandler.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandHandler.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandHandler.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAAwBsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:22:56.802Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "ToolBoxView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\Views\\ToolBoxView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\ToolBox\\Views\\ToolBoxView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\Views\\ToolBoxView.xaml.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\ToolBox\\Views\\ToolBoxView.xaml.cs", "ViewState": "AgIAABcAAAAAAAAAAAAAwCMAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:23:42.61Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "ViewToolBoxCommandDefinition.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandDefinition.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandDefinition.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandDefinition.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandDefinition.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:22:55.534Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "RunManagerView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-12T14:20:24.208Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "AddSolutionCommandHandler.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBar\\Commands\\AddSolutionCommandHandler.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\ToolBar\\Commands\\AddSolutionCommandHandler.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolBar\\Commands\\AddSolutionCommandHandler.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\ToolBar\\Commands\\AddSolutionCommandHandler.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAABYAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:56:05.71Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "SaveImageViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SaveImage\\ViewModels\\SaveImageViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.SaveImage\\ViewModels\\SaveImageViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SaveImage\\ViewModels\\SaveImageViewModel.cs", "RelativeToolTip": "MoonLight.Modules.SaveImage\\ViewModels\\SaveImageViewModel.cs", "ViewState": "AgIAAEsBAAAAAAAAAAApwFIBAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T13:54:19.123Z"}]}]}]}