{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|c:\\users\\<USER>\\desktop\\视觉平台优化\\moonlight.platform_v2_20150514\\src\\moonlight.core\\common\\helper\\jsonserializerhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\common\\helper\\jsonserializerhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\common\\helper\\serializehelp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\common\\helper\\serializehelp.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\services\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\services\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\services\\solution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\services\\solution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.grabimage\\viewmodels\\grabimageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|solutionrelative:moonlight.modules.grabimage\\viewmodels\\grabimageviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A460D352-B73E-468F-B57D-B87269C197A3}|MoonLight.Modules.SendStr\\MoonLight.Modules.SendStr.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.sendstr\\viewmodels\\sendstrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A460D352-B73E-468F-B57D-B87269C197A3}|MoonLight.Modules.SendStr\\MoonLight.Modules.SendStr.csproj|solutionrelative:moonlight.modules.sendstr\\viewmodels\\sendstrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E4EE3AF6-6C66-4B54-9029-64E160229023}|MoonLight.Modules.While\\MoonLight.Modules.While.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.while\\views\\whilepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{E4EE3AF6-6C66-4B54-9029-64E160229023}|MoonLight.Modules.While\\MoonLight.Modules.While.csproj|solutionrelative:moonlight.modules.while\\views\\whilepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.devicemanager\\models\\devicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|solutionrelative:moonlight.modules.devicemanager\\models\\devicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6E154BD2-F6EE-46DF-9FA5-15F1345BA4AA}|MoonLight.Modules.Measuring\\MoonLight.Modules.Measuring.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measuring\\views\\measuringpropertyview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6E154BD2-F6EE-46DF-9FA5-15F1345BA4AA}|MoonLight.Modules.Measuring\\MoonLight.Modules.Measuring.csproj|solutionrelative:moonlight.modules.measuring\\views\\measuringpropertyview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6E154BD2-F6EE-46DF-9FA5-15F1345BA4AA}|MoonLight.Modules.Measuring\\MoonLight.Modules.Measuring.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measuring\\views\\measuringpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6E154BD2-F6EE-46DF-9FA5-15F1345BA4AA}|MoonLight.Modules.Measuring\\MoonLight.Modules.Measuring.csproj|solutionrelative:moonlight.modules.measuring\\views\\measuringpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{853B9914-2808-42E6-9483-F5E8AF91778D}|MoonLight.Modules.MeasureCircle\\MoonLight.Modules.MeasureCircle.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measurecircle\\views\\measurecirclepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{853B9914-2808-42E6-9483-F5E8AF91778D}|MoonLight.Modules.MeasureCircle\\MoonLight.Modules.MeasureCircle.csproj|solutionrelative:moonlight.modules.measurecircle\\views\\measurecirclepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\models\\idevicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\models\\idevicemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.devicemanager\\views\\devicemanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|solutionrelative:moonlight.modules.devicemanager\\views\\devicemanagerview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.devicemanager\\viewmodels\\devicemanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|solutionrelative:moonlight.modules.devicemanager\\viewmodels\\devicemanagerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.app.demo\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|solutionrelative:moonlight.app.demo\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E7DCA6F1-827F-43EC-8784-B12839234AD9}|MoonLight.Modules.BuildPp\\MoonLight.Modules.BuildPp.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.buildpp\\views\\buildpppropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{E7DCA6F1-827F-43EC-8784-B12839234AD9}|MoonLight.Modules.BuildPp\\MoonLight.Modules.BuildPp.csproj|solutionrelative:moonlight.modules.buildpp\\views\\buildpppropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.grabimage\\views\\grabimagepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F7132A01-96F2-478C-8550-A88D9421D156}|MoonLight.Modules.GrabImage\\MoonLight.Modules.GrabImage.csproj|solutionrelative:moonlight.modules.grabimage\\views\\grabimagepropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\tooloutput\\views\\tooloutputview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\tooloutput\\views\\tooloutputview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\styles\\textboxstyle.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\styles\\textboxstyle.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{50DB1135-5091-4644-9416-002CD4E7BC80}|MoonLight.Modules.ReceiveStr\\MoonLight.Modules.ReceiveStr.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.receivestr\\views\\receivestrview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{50DB1135-5091-4644-9416-002CD4E7BC80}|MoonLight.Modules.ReceiveStr\\MoonLight.Modules.ReceiveStr.csproj|solutionrelative:moonlight.modules.receivestr\\views\\receivestrview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A460D352-B73E-468F-B57D-B87269C197A3}|MoonLight.Modules.SendStr\\MoonLight.Modules.SendStr.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.sendstr\\views\\sendstrpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A460D352-B73E-468F-B57D-B87269C197A3}|MoonLight.Modules.SendStr\\MoonLight.Modules.SendStr.csproj|solutionrelative:moonlight.modules.sendstr\\views\\sendstrpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\modules\\mainwindow\\views\\mainwindowview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\modules\\mainwindow\\views\\mainwindowview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.devicemanager\\views\\devicemanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{B8E5F2A1-9C3D-4E2F-8A7B-1D5E6F9C8B4A}|MoonLight.Modules.DeviceManager\\MoonLight.Modules.DeviceManager.csproj|solutionrelative:moonlight.modules.devicemanager\\views\\devicemanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\converter\\bool2visibilityconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\converter\\bool2visibilityconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9B4DFB06-26BB-4D61-8BC8-407DAE95A9DA}|MoonLight.Modules.If\\MoonLight.Modules.If.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.if\\views\\ifpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{9B4DFB06-26BB-4D61-8BC8-407DAE95A9DA}|MoonLight.Modules.If\\MoonLight.Modules.If.csproj|solutionrelative:moonlight.modules.if\\views\\ifpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{22F4163F-6B5D-487D-9C2E-972742DEB665}|MoonLight.Modules.CoordinateMap\\MoonLight.Modules.CoordinateMap.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.coordinatemap\\views\\coordinatemappropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{22F4163F-6B5D-487D-9C2E-972742DEB665}|MoonLight.Modules.CoordinateMap\\MoonLight.Modules.CoordinateMap.csproj|solutionrelative:moonlight.modules.coordinatemap\\views\\coordinatemappropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{925A877D-946E-42B9-B1D8-3D18174EC92B}|MoonLight.Modules.MeasureLines\\MoonLight.Modules.MeasureLines.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measurelines\\views\\measurelinespropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{925A877D-946E-42B9-B1D8-3D18174EC92B}|MoonLight.Modules.MeasureLines\\MoonLight.Modules.MeasureLines.csproj|solutionrelative:moonlight.modules.measurelines\\views\\measurelinespropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{D4DE70DA-1FC1-478C-8781-8BF7EA98B7A3}|MoonLight.Modules.MetricToolkit\\MoonLight.Modules.MetricToolkit.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.metrictoolkit\\views\\metrictoolkitpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{D4DE70DA-1FC1-478C-8781-8BF7EA98B7A3}|MoonLight.Modules.MetricToolkit\\MoonLight.Modules.MetricToolkit.csproj|solutionrelative:moonlight.modules.metrictoolkit\\views\\metrictoolkitpropertyview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{50DB1135-5091-4644-9416-002CD4E7BC80}|MoonLight.Modules.ReceiveStr\\MoonLight.Modules.ReceiveStr.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.receivestr\\viewmodels\\receivestrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{50DB1135-5091-4644-9416-002CD4E7BC80}|MoonLight.Modules.ReceiveStr\\MoonLight.Modules.ReceiveStr.csproj|solutionrelative:moonlight.modules.receivestr\\viewmodels\\receivestrviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\menudefinitions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\menudefinitions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{99762013-03CE-4C6B-A7AB-166DC7577F94}|MoonLight.Modules.MeasureLine\\MoonLight.Modules.MeasureLine.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.modules.measureline\\views\\measurelineview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{99762013-03CE-4C6B-A7AB-166DC7577F94}|MoonLight.Modules.MeasureLine\\MoonLight.Modules.MeasureLine.csproj|solutionrelative:moonlight.modules.measureline\\views\\measurelineview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\devicemanager\\models\\devicecategorygroup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\devicemanager\\models\\devicecategorygroup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\framework\\themes\\thememanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\framework\\themes\\thememanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\modules\\shell\\viewmodels\\shellviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\modules\\shell\\viewmodels\\shellviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.app.demo\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|solutionrelative:moonlight.app.demo\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.app.demo\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8A9B470B-6676-44D2-B64B-44D8C3D4B321}|MoonLight.App.Demo\\MoonLight.App.Demo.csproj|solutionrelative:moonlight.app.demo\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\framework\\services\\resourcemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\framework\\services\\resourcemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\framework\\persisteddocument.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\framework\\persisteddocument.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\cameraview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\cameraview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\modules\\mainwindow\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\modules\\mainwindow\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\viewmodels\\cardviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\viewmodels\\cardviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\collection.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\collection.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\modules\\posmanager\\iposmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\modules\\posmanager\\iposmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\posmanager\\views\\posmanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\posmanager\\views\\posmanagerview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\views\\cardview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\views\\cardview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\viewmodels\\axiscontrolviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\viewmodels\\axiscontrolviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\cardpmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\cardpmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\cardbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\cardbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\daheng\\cameradaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\daheng\\cameradaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\hik\\camerahik.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\hik\\camerahik.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\views\\cardview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\views\\cardview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\axispmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\axispmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\limitbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\limitbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\output\\views\\logview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\output\\views\\logview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\views\\axiscontrolview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\views\\axiscontrolview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\settings\\views\\settingsysview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\settings\\views\\settingsysview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\common\\helper\\dataconvert.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\common\\helper\\dataconvert.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\iopmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\iopmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\iobase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\iobase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\defines\\cardtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\defines\\cardtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\iobaseex.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\iobaseex.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\configbaseex.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\configbaseex.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\statusbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\statusbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\statusiopmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\statusiopmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\statusio.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\statusio.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\statusaxis.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\statusaxis.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\statusaxispmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\statusaxispmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\statuspmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\statuspmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\defines\\veltype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\defines\\veltype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\icard.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\icard.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\defines\\cameratype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\defines\\cameratype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\cardcoordinate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\cardcoordinate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\coordinatestatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\coordinatestatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\axispmacstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\axispmacstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.motion\\pmac\\limitpmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{456BF73E-6B91-48CE-A564-6F3A2FBA5F98}|MoonLight.Devices.Motion\\MoonLight.Devices.Motion.csproj|solutionrelative:moonlight.devices.motion\\pmac\\limitpmac.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\configbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\configbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\motionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\motionhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\axisstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\axisstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\devicebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\devicebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\motion\\unitstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\motion\\unitstatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\framework\\itool.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\framework\\itool.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\framework\\ipersisteddocument.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\framework\\ipersisteddocument.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.devices.camera\\daheng\\statusdaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{73D6FC2C-3BC6-4879-8FA0-9B6E85D73F12}|MoonLight.Devices.Camera\\MoonLight.Devices.Camera.csproj|solutionrelative:moonlight.devices.camera\\daheng\\statusdaheng.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\assets\\modules\\algorithms\\visionlib.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\assets\\modules\\algorithms\\visionlib.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight\\ui\\appbootstrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}|MoonLight\\MoonLight.csproj|solutionrelative:moonlight\\ui\\appbootstrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight.Core\\Services\\Solution.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\services\\engineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\services\\engineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\camera\\statuscamera.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\camera\\statuscamera.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\communication\\communicationbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\communication\\communicationbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\configdevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\configdevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\communication\\myserialport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\communication\\myserialport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\moonlight.core\\devices\\communication\\communicationsetview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{08C42464-E6EA-4010-A329-F7D4C6706D22}|MoonLight.Core\\MoonLight.Core.csproj|solutionrelative:moonlight.core\\devices\\communication\\communicationsetview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "JsonSerializerHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\JsonSerializerHelper.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Common\\Helper\\JsonSerializerHelper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\JsonSerializerHelper.cs", "RelativeToolTip": "MoonLight.Core\\Common\\Helper\\JsonSerializerHelper.cs", "ViewState": "AgIAACIBAAAAAAAAAAAYwDcBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:12:22.552Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SerializeHelp.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\SerializeHelp.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Common\\Helper\\SerializeHelp.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\SerializeHelp.cs", "RelativeToolTip": "MoonLight.Core\\Common\\Helper\\SerializeHelp.cs", "ViewState": "AgIAAEkAAAAAAAAAAAAkwDMAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T11:47:48.246Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "MeasuringPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T11:41:36.691Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "SendStrViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "RelativeToolTip": "MoonLight.Modules.SendStr\\ViewModels\\SendStrViewModel.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAlwE0AAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T11:32:33.464Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "DeviceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\Models\\DeviceManager.cs", "RelativeDocumentMoniker": "MoonLight.Modules.DeviceManager\\Models\\DeviceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\Models\\DeviceManager.cs", "RelativeToolTip": "MoonLight.Modules.DeviceManager\\Models\\DeviceManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T03:45:01.509Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "WhilePropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.While\\Views\\WhilePropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.While\\Views\\WhilePropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.While\\Views\\WhilePropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.While\\Views\\WhilePropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:39:01.57Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "GrabImageViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "RelativeToolTip": "MoonLight.Modules.GrabImage\\ViewModels\\GrabImageViewModel.cs", "ViewState": "AgIAAC8AAAAAAAAAAAASwFQAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T03:34:26.761Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Solution.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Solution.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Services\\Solution.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Solution.cs", "RelativeToolTip": "MoonLight.Core\\Services\\Solution.cs", "ViewState": "AgIAADMAAAAAAAAAAAAawHEAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T07:36:58.465Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Project.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Project.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Services\\Project.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\Project.cs", "RelativeToolTip": "MoonLight.Core\\Services\\Project.cs", "ViewState": "AgIAABUCAAAAAAAAAAAuwBwCAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T08:08:39.245Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Views\\MeasuringPropertyView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml.cs", "RelativeToolTip": "MoonLight.Modules.Measuring\\Views\\MeasuringPropertyView.xaml.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAwB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T11:48:52.562Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "DeviceManagerView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml.cs", "RelativeToolTip": "MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAawIEAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T11:20:23.253Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "MeasureCirclePropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureCircle\\Views\\MeasureCirclePropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MeasureCircle\\Views\\MeasureCirclePropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureCircle\\Views\\MeasureCirclePropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.MeasureCircle\\Views\\MeasureCirclePropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T11:35:52.682Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "DeviceManagerViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "RelativeToolTip": "MoonLight.Modules.DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "ViewState": "AgIAABMAAAAAAAAAAAAawCMAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T03:45:34.23Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "IDeviceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAuwBYAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T09:03:13.788Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "BuildPpPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.BuildPp\\Views\\BuildPpPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.BuildPp\\Views\\BuildPpPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.BuildPp\\Views\\BuildPpPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.BuildPp\\Views\\BuildPpPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:46:13.738Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "GrabImagePropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.GrabImage\\Views\\GrabImagePropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:48:19.144Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "ToolOutputView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolOutput\\Views\\ToolOutputView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\ToolOutput\\Views\\ToolOutputView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\ToolOutput\\Views\\ToolOutputView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\ToolOutput\\Views\\ToolOutputView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T05:54:24.474Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "TextBoxStyle.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Styles\\TextBoxStyle.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Styles\\TextBoxStyle.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Styles\\TextBoxStyle.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Styles\\TextBoxStyle.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T05:41:20.967Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "ReceiveStrView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ReceiveStr\\Views\\ReceiveStrView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.ReceiveStr\\Views\\ReceiveStrView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ReceiveStr\\Views\\ReceiveStrView.xaml", "RelativeToolTip": "MoonLight.Modules.ReceiveStr\\Views\\ReceiveStrView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T05:54:15.252Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "App.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\App.xaml.cs", "RelativeDocumentMoniker": "MoonLight.App.Demo\\App.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\App.xaml.cs", "RelativeToolTip": "MoonLight.App.Demo\\App.xaml.cs", "ViewState": "AgIAAHAAAAAAAAAAAAAAwIUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T10:17:38.806Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "SendStrPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SendStr\\Views\\SendStrPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.SendStr\\Views\\SendStrPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.SendStr\\Views\\SendStrPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.SendStr\\Views\\SendStrPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:46:10.765Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "MainWindowView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Modules\\MainWindow\\Views\\MainWindowView.xaml", "RelativeDocumentMoniker": "MoonLight\\UI\\Modules\\MainWindow\\Views\\MainWindowView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Modules\\MainWindow\\Views\\MainWindowView.xaml", "RelativeToolTip": "MoonLight\\UI\\Modules\\MainWindow\\Views\\MainWindowView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T05:41:52.869Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "DeviceManagerView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml", "RelativeToolTip": "MoonLight.Modules.DeviceManager\\Views\\DeviceManagerView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T05:38:18.866Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "Bool2VisibilityConverter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Converter\\Bool2VisibilityConverter.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Converter\\Bool2VisibilityConverter.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Converter\\Bool2VisibilityConverter.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Converter\\Bool2VisibilityConverter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T03:18:41.108Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "IfPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.If\\Views\\IfPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:46:05.715Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "CoordinateMapPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.CoordinateMap\\Views\\CoordinateMapPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:50:55.1Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "MeasureLinesPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLines\\Views\\MeasureLinesPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MeasureLines\\Views\\MeasureLinesPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLines\\Views\\MeasureLinesPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.MeasureLines\\Views\\MeasureLinesPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:50:42.814Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "MetricToolkitPropertyView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitPropertyView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitPropertyView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitPropertyView.xaml", "RelativeToolTip": "MoonLight.Modules.MetricToolkit\\Views\\MetricToolkitPropertyView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:46:18.527Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "ReceiveStrViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "RelativeToolTip": "MoonLight.Modules.ReceiveStr\\ViewModels\\ReceiveStrViewModel.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAAwDEAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T03:46:25.898Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "MenuDefinitions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\MenuDefinitions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T03:41:04.996Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "MeasureLineView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "RelativeDocumentMoniker": "MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "RelativeToolTip": "MoonLight.Modules.MeasureLine\\Views\\MeasureLineView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:40:29.279Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "ThemeManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "RelativeToolTip": "MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "ViewState": "AgIAABMAAAAAAAAAAAAAwB0AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:35:32.912Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "ResourceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\Services\\ResourceManager.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Framework\\Services\\ResourceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\Services\\ResourceManager.cs", "RelativeToolTip": "MoonLight\\UI\\Framework\\Services\\ResourceManager.cs", "ViewState": "AgIAAAQAAAAAAAAAAAArwBAAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:35:26.701Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "PersistedDocument.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\PersistedDocument.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Framework\\PersistedDocument.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\PersistedDocument.cs", "RelativeToolTip": "MoonLight\\UI\\Framework\\PersistedDocument.cs", "ViewState": "AgIAACMAAAAAAAAAAAArwC8AAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:35:22.164Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Modules\\MainWindow\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Modules\\MainWindow\\ViewModels\\MainWindowViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Modules\\MainWindow\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "MoonLight\\UI\\Modules\\MainWindow\\ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAAG0AAAAAAAAAAAAmwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T09:34:48.397Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "DeviceManagerViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\ViewModels\\DeviceManagerViewModel.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T07:53:29.668Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "DeviceCategoryGroup.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAuwAcAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T07:57:43.603Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MoonLight.App.Demo\\MainWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindow.xaml.cs", "RelativeToolTip": "MoonLight.App.Demo\\MainWindow.xaml.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAYwCkAAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:22:00.427Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "ShellViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Modules\\Shell\\ViewModels\\ShellViewModel.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Modules\\Shell\\ViewModels\\ShellViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Modules\\Shell\\ViewModels\\ShellViewModel.cs", "RelativeToolTip": "MoonLight\\UI\\Modules\\Shell\\ViewModels\\ShellViewModel.cs", "ViewState": "AgIAAGoAAAAAAAAAAAAiwHUAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:23:41.899Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "ThemeManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "RelativeDocumentMoniker": "..\\..\\MoonLight.Platform_0506\\src\\MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "RelativeToolTip": "..\\..\\MoonLight.Platform_0506\\src\\MoonLight\\UI\\Framework\\Themes\\ThemeManager.cs", "ViewState": "AgIAAFoAAAAAAAAAAADwv1YAAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T10:27:20.28Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "Collection.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Collection.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Collection.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Collection.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Collection.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-22T09:08:44.895Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "MoonLight.App.Demo\\MainWindowViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.App.Demo\\MainWindowViewModel.cs", "RelativeToolTip": "MoonLight.App.Demo\\MainWindowViewModel.cs", "ViewState": "AgIAAAYBAAAAAAAAAAAQwBMBAABTAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T12:55:23.89Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "PosManagerView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-22T07:06:02.104Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "IPosManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Modules\\PosManager\\IPosManager.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Modules\\PosManager\\IPosManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Modules\\PosManager\\IPosManager.cs", "RelativeToolTip": "MoonLight.Core\\Modules\\PosManager\\IPosManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T07:10:39.926Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "CardViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\ViewModels\\CardViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\ViewModels\\CardViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\ViewModels\\CardViewModel.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\ViewModels\\CardViewModel.cs", "ViewState": "AgIAADoAAAAAAAAAAADwvzwAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T08:04:01.048Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "CardPmac.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\CardPmac.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\CardPmac.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\CardPmac.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\CardPmac.cs", "ViewState": "AgIAAJoBAAAAAAAAAAAuwKMBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:39:16.633Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "CardView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-21T08:03:45.325Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "CameraView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraView.xaml", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\CameraView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\CameraView.xaml", "RelativeToolTip": "MoonLight.Devices.Camera\\CameraView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-22T01:17:31.627Z"}, {"$type": "Document", "DocumentIndex": 94, "Title": "DeviceManagerView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Views\\DeviceManagerView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-22T05:43:20.167Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 51, "Title": "CameraHIK.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\Hik\\CameraHIK.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAABoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T13:19:38.148Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "CardBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\CardBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\CardBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\CardBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\CardBase.cs", "ViewState": "AgIAAC8BAAAAAAAAAAAmwEABAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:07:13.439Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "AxisControlViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\ViewModels\\AxisControlViewModel.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\ViewModels\\AxisControlViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\ViewModels\\AxisControlViewModel.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\ViewModels\\AxisControlViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA4AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T01:50:33.527Z"}, {"$type": "Document", "DocumentIndex": 95, "Title": "DeviceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\DeviceManager\\Models\\DeviceManager.cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T12:56:14.69Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "CameraDaheng.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\Daheng\\CameraDaheng.cs", "ViewState": "AgIAADUAAAAAAAAAAAA+wEAAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T08:22:43.824Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "CardView.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\Views\\CardView.xaml.cs", "ViewState": "AgIAABIAAAAAAAAAAAAqwBsAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-21T13:17:01.349Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "AxisPmac.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\AxisPmac.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\AxisPmac.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\AxisPmac.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\AxisPmac.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAhwAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:22:58.881Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "LimitBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\LimitBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\LimitBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\LimitBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\LimitBase.cs", "ViewState": "AgIAAD8AAAAAAAAAAIA6wAoAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:27:54.467Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "LogView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Output\\Views\\LogView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\Output\\Views\\LogView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Output\\Views\\LogView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\Output\\Views\\LogView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-21T08:28:08.138Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "AxisControlView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\Views\\AxisControlView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\Views\\AxisControlView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\Views\\AxisControlView.xaml", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\Views\\AxisControlView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-20T07:07:06.808Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "SettingSysView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Settings\\Views\\SettingSysView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\Settings\\Views\\SettingSysView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Settings\\Views\\SettingSysView.xaml", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\Settings\\Views\\SettingSysView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-21T06:36:03.908Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "DataConvert.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\DataConvert.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Common\\Helper\\DataConvert.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Common\\Helper\\DataConvert.cs", "RelativeToolTip": "MoonLight.Core\\Common\\Helper\\DataConvert.cs", "ViewState": "AgIAACYBAAAAAAAAAAAawDYBAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T14:16:27.625Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "IOPmac.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\IOPmac.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\IOPmac.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\IOPmac.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\IOPmac.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA8AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T09:56:25.625Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "IOBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\IOBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\IOBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\IOBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\IOBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAYAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:19:37.478Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "CardType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\CardType.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Defines\\CardType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\CardType.cs", "RelativeToolTip": "MoonLight.Core\\Defines\\CardType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACYAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T09:53:18.193Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "StatusBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\StatusBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\StatusBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\StatusBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\StatusBase.cs", "ViewState": "AgIAACAAAAAAAAAAAAAmwC4AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T10:55:48.659Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "StatusIOPmac.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\StatusIOPmac.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\StatusIOPmac.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\StatusIOPmac.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\StatusIOPmac.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAgAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T09:57:01.169Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "StatusIO.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\StatusIO.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\StatusIO.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\StatusIO.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\StatusIO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T11:48:58.801Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "IOBaseEx.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\IOBaseEx.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\IOBaseEx.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\IOBaseEx.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\IOBaseEx.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T11:50:27.495Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "StatusAxis.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\StatusAxis.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\StatusAxis.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\StatusAxis.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\StatusAxis.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:33:50.39Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "ConfigBaseEx.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigBaseEx.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\ConfigBaseEx.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigBaseEx.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\ConfigBaseEx.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAkAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T12:49:56.778Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "StatusAxisPmac.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\StatusAxisPmac.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\StatusAxisPmac.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\StatusAxisPmac.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\StatusAxisPmac.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:33:13.797Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "StatusPmac.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\StatusPmac.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\StatusPmac.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\StatusPmac.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\StatusPmac.cs", "ViewState": "AgIAAD4AAAAAAAAAAAAAwGAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T10:13:27.148Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "VelType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\VelType.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Defines\\VelType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\VelType.cs", "RelativeToolTip": "MoonLight.Core\\Defines\\VelType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAmwBAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T09:54:28.868Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "ICard.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\ICard.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\ICard.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\ICard.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\ICard.cs", "ViewState": "AgIAACAAAAAAAAAAAAA0wAkAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:09:30.719Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "CameraType.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\CameraType.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Defines\\CameraType.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Defines\\CameraType.cs", "RelativeToolTip": "MoonLight.Core\\Defines\\CameraType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T08:05:24.945Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "CardCoordinate.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\CardCoordinate.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\CardCoordinate.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\CardCoordinate.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\CardCoordinate.cs", "ViewState": "AgIAAAIBAAAAAAAAAAAAAAoAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:38:02.03Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "CoordinateStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\CoordinateStatus.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\CoordinateStatus.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\CoordinateStatus.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\CoordinateStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:38:24.446Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "AxisPmacStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\AxisPmacStatus.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\AxisPmacStatus.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\AxisPmacStatus.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\AxisPmacStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:36:49.205Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "LimitPmac.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\LimitPmac.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Motion\\Pmac\\LimitPmac.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Motion\\Pmac\\LimitPmac.cs", "RelativeToolTip": "MoonLight.Devices.Motion\\Pmac\\LimitPmac.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:32:02.429Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "MotionHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\MotionHelper.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\MotionHelper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\MotionHelper.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\MotionHelper.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:19:34.883Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "ConfigBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\ConfigBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\ConfigBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwA4AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T12:49:53.86Z"}, {"$type": "Document", "DocumentIndex": 79, "Title": "AxisStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\AxisStatus.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\AxisStatus.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\AxisStatus.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\AxisStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:19:30.945Z"}, {"$type": "Document", "DocumentIndex": 81, "Title": "UnitStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\UnitStatus.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Motion\\UnitStatus.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Motion\\UnitStatus.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Motion\\UnitStatus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-20T07:07:12.337Z"}, {"$type": "Document", "DocumentIndex": 82, "Title": "ITool.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\ITool.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Framework\\ITool.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\ITool.cs", "RelativeToolTip": "MoonLight\\UI\\Framework\\ITool.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T08:17:06.913Z"}, {"$type": "Document", "DocumentIndex": 83, "Title": "IPersistedDocument.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\IPersistedDocument.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\Framework\\IPersistedDocument.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\Framework\\IPersistedDocument.cs", "RelativeToolTip": "MoonLight\\UI\\Framework\\IPersistedDocument.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T08:17:13.824Z"}, {"$type": "Document", "DocumentIndex": 84, "Title": "StatusDaheng.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "RelativeDocumentMoniker": "MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "RelativeToolTip": "MoonLight.Devices.Camera\\Daheng\\StatusDaheng.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-19T00:14:03.627Z"}, {"$type": "Document", "DocumentIndex": 80, "Title": "DeviceBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\DeviceBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\DeviceBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\DeviceBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\DeviceBase.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAuwA8AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T00:18:06.108Z"}, {"$type": "Document", "DocumentIndex": 88, "Title": "EngineService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\EngineService.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Services\\EngineService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Services\\EngineService.cs", "RelativeToolTip": "MoonLight.Core\\Services\\EngineService.cs", "ViewState": "AgIAAEkAAAAAAAAAAADwv1gAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T07:11:08.379Z"}, {"$type": "Document", "DocumentIndex": 86, "Title": "AppBootstrapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\AppBootstrapper.cs", "RelativeDocumentMoniker": "MoonLight\\UI\\AppBootstrapper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight\\UI\\AppBootstrapper.cs", "RelativeToolTip": "MoonLight\\UI\\AppBootstrapper.cs", "ViewState": "AgIAAF4AAAAAAAAAAAAwwHIAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T03:48:03.66Z"}, {"$type": "Document", "DocumentIndex": 85, "Title": "VisionLib.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "RelativeToolTip": "MoonLight.Core\\Assets\\Modules\\Algorithms\\VisionLib.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAgwD4AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T00:28:06.825Z"}, {"$type": "Document", "DocumentIndex": 87, "Title": "Solution.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight.Core\\Services\\Solution.cs", "RelativeDocumentMoniker": "..\\..\\MoonLight.Platform_0506\\src\\MoonLight.Core\\Services\\Solution.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_0506\\src\\MoonLight.Core\\Services\\Solution.cs", "RelativeToolTip": "..\\..\\MoonLight.Platform_0506\\src\\MoonLight.Core\\Services\\Solution.cs", "ViewState": "AgIAAGUAAAAAAAAAAAAewGsAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T07:51:13.953Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 89, "Title": "StatusCamera.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Camera\\StatusCamera.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAA4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T00:22:00.74Z"}, {"$type": "Document", "DocumentIndex": 91, "Title": "ConfigDevice.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigDevice.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\ConfigDevice.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\ConfigDevice.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\ConfigDevice.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T12:50:01.96Z"}, {"$type": "Document", "DocumentIndex": 90, "Title": "CommunicationBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Communication\\CommunicationBase.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAuwBAAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T12:49:09.025Z"}, {"$type": "Document", "DocumentIndex": 92, "Title": "MySerialPort.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\MySerialPort.cs", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Communication\\MySerialPort.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\MySerialPort.cs", "RelativeToolTip": "MoonLight.Core\\Devices\\Communication\\MySerialPort.cs", "ViewState": "AgIAAGYAAAAAAAAAAAAkwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T12:49:17.142Z"}, {"$type": "Document", "DocumentIndex": 93, "Title": "CommunicationSetView.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\CommunicationSetView.xaml", "RelativeDocumentMoniker": "MoonLight.Core\\Devices\\Communication\\CommunicationSetView.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\Devices\\Communication\\CommunicationSetView.xaml", "RelativeToolTip": "MoonLight.Core\\Devices\\Communication\\CommunicationSetView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-14T12:48:54.049Z"}]}]}]}