﻿

FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\FlowManager\Views\FlowTreeView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\FlowManager\Views\RunManagerView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\Output\Views\OutputView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\PosManager\Views\PosManagerView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\RenderControl\Views\RenderViewWpf.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\RenderControl\Views\VisionView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\Settings\Views\SettingSysView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\ToolBar\Views\CanvasSetView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\ToolBar\Views\CommunicationSetView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\ToolBar\Views\GlobalVarView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Modules\ToolOutput\Views\DeviceStatusView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Styles\ButtonGroupStyle.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Styles\ButtonStyle.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Styles\Converter.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Assets\Styles\PropertyGridStyle.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Devices\Communication\CommunicationSetView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Devices\Motion\Views\AxisControlView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Devices\Motion\Views\CardView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Views\ChangePwdView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Views\EditRemarksView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Views\LoginView.xaml;;
FC:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\Views\VarLinkView.xaml;;

