<mahapps:MetroWindow x:Class="MoonLight.App.Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MoonLight.App.Demo"
        xmlns:mahapps="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro" 
        xmlns:view="clr-namespace:RenderControl.Views;assembly=MoonLight.Core"
        mc:Ignorable="d"
        Title="MainWindow" Height="850" Width="1300" Loaded="Window_Loaded" >

    <b:Interaction.Triggers>
        <b:EventTrigger EventName="Loaded">
            <b:InvokeCommandAction Command="{Binding LoadedCommand}"/>
        </b:EventTrigger>
    </b:Interaction.Triggers>
    <Grid>


        <TabControl >


            <TabItem Header="Vision UI" Selector.Selected="OnTabSelected">
                <ContentControl Name="vision"/>
            </TabItem>

            <TabItem Header="Demo UI" Selector.Selected="OnTabSelected">
                <Grid Background="{DynamicResource MahApps.Brushes.ThemeBackground}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="auto"/>
                        <RowDefinition Height="auto"/>
                        <RowDefinition/>
                        <RowDefinition/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="550"/>
                        <ColumnDefinition Width="350"/>
                        <ColumnDefinition/>
                    </Grid.ColumnDefinitions>
                    <GroupBox Header="方案操作接口">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="5">
                                <Button Content="加载方案" Width="90" Margin="5 0" Command="{Binding LoadPathCommand}"/>
                                <Button Content="保存方案" Width="90"  Command="{Binding SaveSolCommand}"/>
                                <Button Content="Halcon序列化测试" Width="120" Margin="5 0" Click="HalconTestButton_Click"/>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="5" IsEnabled="{Binding IsEnableSelectedSol}">
                                <TextBlock Text="当前方案：" Margin="5"/>
                                <TextBlock Text="{Binding CurPath,Mode=OneWay}" Margin="5"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="流程操作接口" Grid.Row="1">
                        <StackPanel Orientation="Vertical">
                            <Button Content="执行" Command="{Binding RunOnceCommand}"/>
                            <ListBox Grid.Column="0" ItemsSource="{Binding ListProjectName}" SelectedItem="{Binding SelectedProjectName}">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"/>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </GroupBox>

                    <GroupBox Header="流程操作接口" Grid.Row="2">
                        <StackPanel Orientation="Vertical">
                            <ListBox Grid.Column="0" ItemsSource="{Binding ListToolName}" SelectedItem="{Binding SelectedToolName}">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}"/>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </GroupBox>


                    <GroupBox Header="流程控制接口" Grid.Row="3">
                        <StackPanel>
                             
                            <StackPanel Orientation="Horizontal" Margin="15 5">
                                <TextBlock Margin="3 0" FontSize="18">
                                <Run Text="执行结果："/>
                                <Run Text="{Binding Status}"/>
                                </TextBlock>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="角点X：" FontSize="16"/>
                                        <TextBlock Text="{Binding CrossX}" FontSize="16"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="角点Y：" FontSize="16"/>
                                        <TextBlock Text="{Binding CrossY}" FontSize="16"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
              
                    
                    
                    <GroupBox Header="属性栏" Grid.Column="1" Grid.RowSpan="4" Margin="1 0" IsEnabled="{Binding IsEnabled}">
                        <ContentControl Content="{Binding PropertyControl}" Margin="-3 0 -3 0"/>
                    </GroupBox>
                    <ContentControl   x:Name="VisionView" Grid.Column="2" Grid.RowSpan="4" />
                    
                </Grid>

            </TabItem>



        </TabControl>

    </Grid>
</mahapps:MetroWindow>
