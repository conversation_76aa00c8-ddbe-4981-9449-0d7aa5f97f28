﻿using MoonLight.Modules.RenderControl.ViewModels;
using MoonLight.UI.Framework.Commands;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework.Threading;
using System.ComponentModel.Composition;
using System.Threading.Tasks;

namespace MoonLight.Modules.RenderControl.Commands
{
    [CommandHandler]
    public class ViewRenderControlCommandHandler : CommandHandlerBase<ViewRenderControlCommandDefinition>
    {
        private readonly IShell _shell;

        [ImportingConstructor]
        public ViewRenderControlCommandHandler(IShell shell)
        {
            _shell = shell;
        }

        public override Task Run(Command command)
        {
            _shell.ShowTool(IoC.Get<VisionViewModel>());
            //_shell.OpenDocumentAsync(IoC.Get<VisionViewModel>());
            return TaskUtility.Completed;
        }
    }
}
