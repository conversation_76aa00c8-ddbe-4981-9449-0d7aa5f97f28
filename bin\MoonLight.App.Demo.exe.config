﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
	</startup>
	<runtime>
		<!--<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />
			</dependentAssembly>
		</assemblyBinding>-->

		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<!--<probing privatePath="dlls;dlls/Common;dlls/Communication;dlls/DaHeng;dlls/GSN;dlls/halcon;dlls/Hik;dlls/ICSharpCode;dlls/PMAC;dlls/UI" />
			--><!-- 指定子目录 -->
			<dependentAssembly>

				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />

				<bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />

			</dependentAssembly>

			<dependentAssembly>

				<assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />

				<bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />

			</dependentAssembly>

			<dependentAssembly>

				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />

				<bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />

			</dependentAssembly>

		</assemblyBinding>
		
		
	</runtime>
</configuration>
