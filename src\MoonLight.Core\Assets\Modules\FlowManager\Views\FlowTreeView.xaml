﻿<UserControl x:Class="MoonLight.Modules.FlowManager.Views.FlowTreeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:enum="clr-namespace:MoonLight.Core.Enums"
             xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
             xmlns:rightControl ="clr-namespace:MoonLight.Core.Common.RightControl"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <rightControl:IsEnableControl x:Key="IsEnableControl"></rightControl:IsEnableControl>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/MoonLight.Core;component/Assets/Collection.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="auto"/>
            <RowDefinition/>
        </Grid.RowDefinitions>
        <StackPanel Orientation="Horizontal" VerticalAlignment="Top" HorizontalAlignment="Left">
            <Button  ToolTip="创建流程" Click="btnCreateProcess_Click" Style="{StaticResource ToolBarButtonStyle}" >
                <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Flow/btnAddProject.png" />
            </Button>
            <Button  ToolTip="删除流程" Click="btnDeleteProcess_Click" Style="{StaticResource ToolBarButtonStyle}">
                <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Flow/btnDeleteProject.png" />
            </Button>
            <Button ToolTip="加载解决方案" Command="{Binding LoadSolCommand}" Style="{StaticResource ToolBarButtonStyle}">
                <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/ToolBar/tsbOpenSolution.png" />
            </Button>
            <Button ToolTip="保存解决方案" Command="{Binding SaveSolCommand}" Style="{StaticResource ToolBarButtonStyle}">
                <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/ToolBar/tsbSaveSolution.png" />
            </Button>
            <!--<Button  ToolTip="创建方法" Click="btnCreateMethod_Click" Style="{StaticResource ToolBarButtonStyle}">
            <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Flow/function.png" />
        </Button>-->
            <!--<Button  ToolTip="设置流程" Click="btnSetProcess_Click" Style="{StaticResource ToolBarButtonStyle}">
            <Image Source="pack://application:,,,/MoonLight.Core;component/Assets/Images/Flow/tsbSettingProject.png"/>
        </Button>-->
           
        </StackPanel>
        <TreeView Grid.Row="2" x:Name="processTree" AllowDrop="True" FontSize="13"  Grid.Column="1" Margin="5 0 5 10"
            Style="{StaticResource ProcessTreeView}"
            SelectedItemChanged="processTree_SelectedItemChanged"
            Drop="processTree_Drop" DragOver="processTree_DragOver" DragLeave="processTree_DragLeave" 
            MouseMove="processTree_MouseMove" 
            GiveFeedback="processTree_GiveFeedback"
            PreviewKeyDown="processTree_KeyDown"   
            PreviewMouseLeftButtonUp="processTree_PreviewMouseLeftButtonUp"
            PreviewMouseLeftButtonDown="processTree_PreviewMouseLeftButtonDown" 
                  PreviewMouseRightButtonDown="processTree_PreviewMouseRightButtonDown"
            PreviewMouseUp="processTree_PreviewMouseUp"
            PreviewMouseDoubleClick="processTree_PreviewMouseDoubleClick">

       
            
            
            <TreeView.ItemTemplate >
                <HierarchicalDataTemplate>
                    <ScrollViewer CanContentScroll="True" HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Hidden">
                        <DockPanel
                        Width="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type Grid}}, Path=Width}" Cursor="Hand">
                            <TextBlock Text="&#xe670;" Width="15" FontFamily="{StaticResource iconfont}" VerticalAlignment="Center" Visibility="{Binding ProjectInfo.IsEncypt,Converter={StaticResource BooleanToVisibilityConverter}}"/>

                            <Image Width="18" Height="18">
                                <Image.Style>
                                    <Style TargetType="Image">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding ProjectInfo.ProjectRunMode}" Value="{x:Static enum:ProjectAutoRunMode.调用执行}">
                                                <Setter Property="Source"  Value="/MoonLight.Core;component/Assets/Images/Flow/调用执行流程.png"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding ProjectInfo.ProjectRunMode}" Value="{x:Static enum:ProjectAutoRunMode.主动执行}">
                                                <Setter Property="Source"  Value="{Binding ProjectInfo.IconImage}"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Image.Style>
                            </Image>
                            <Grid >
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="20"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition/>
                                    <ColumnDefinition Width="auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Orientation="Horizontal" Grid.Row="0">
                                    <TextBlock Margin="10,0,0,0" Text="{Binding ProjectInfo.ProcessName}" VerticalAlignment="Center"/>
                                    <!--<Border HorizontalAlignment="Right" VerticalAlignment="Center" Width="15" Height="15" Margin="10 0 10 0" CornerRadius="3" Background="YellowGreen">
                                    <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="LightYellow" FontSize="10">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding ProjectInfo.ProjectRunMode}" Value="{x:Static enum:ProjectAutoRunMode.主动执行}">
                                                        <Setter Property="Text" Value="A"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding ProjectInfo.ProjectRunMode}" Value="{x:Static enum:ProjectAutoRunMode.调用执行}">
                                                        <Setter Property="Text" Value="P"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>-->
                                    <!--<Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ProjectInfo.ProjectRunMode}" Value="{x:Static enum:ProjectAutoRunMode.主动执行}">
                                                    <Setter Property="Visibility" Value="Hidden"/>
                
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ProjectInfo.ProjectRunMode}" Value="{x:Static enum:ProjectAutoRunMode.调用执行}">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                    <Setter Property="Background" Value="OrangeRed"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>-->
                                    <!--</Border>-->
                                    <TextBlock Margin="10,0,0,0" VerticalAlignment="Center" Text="{Binding ProjectInfo.Remarks,Mode=TwoWay}" Foreground="Lime"/>
                                </StackPanel>
                                <mah:ProgressRing 
                                    Grid.Column="1"
                                    HorizontalAlignment="Right"
                                    Width="20" Foreground="Lime"
                                    Height="20" Padding="5"
                                    Visibility="{Binding ThreadStatus,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource Bool2VisibilityConverter}}" />
                            </Grid>
                            <!--<TextBlock HorizontalAlignment="Right" Margin="5 0" VerticalAlignment="Center" Text="{Binding ExeToolName, Mode=TwoWay}" Visibility="{Binding ThreadStatus, Converter={StaticResource Bool2VisibilityConverter}}" FontSize="10.5"/>-->
                        </DockPanel>
                        <ScrollViewer.ContextMenu >
                            <ContextMenu FontFamily="{StaticResource iconfont}"  DataContext="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource Self}}">
                                <MenuItem Header="重命名" Icon="&#xe6e3;" Click="miRename_Click" IsEnabled="{Binding Source={StaticResource IsEnableControl}, Path=Ins.Stop, Converter={StaticResource InvertBoolConverter}}"/>
                                <MenuItem Header="执行模式" Icon="&#xe652;" IsEnabled="{Binding Source={StaticResource IsEnableControl}, Path=Ins.Stop, Converter={StaticResource InvertBoolConverter}}">
                                    <MenuItem Header="主动执行" IsCheckable="True"  IsChecked="{Binding ProjectInfo.ProjectRunMode, Converter={StaticResource EnumConverter}, ConverterParameter={x:Static enum:ProjectAutoRunMode.主动执行}}"/>
                                    <MenuItem Header="调用执行" IsCheckable="True" IsChecked="{Binding ProjectInfo.ProjectRunMode, Converter={StaticResource EnumConverter}, ConverterParameter={x:Static enum:ProjectAutoRunMode.调用执行}}"/>
                                </MenuItem>

                                <!--<MenuItem Header="流程加密" Icon="&#xe7a8;" IsEnabled="{Binding Source={StaticResource IsEnableControl}, Path=Ins.Stop, Converter={StaticResource InvertBoolConverter}}">
                                    <MenuItem Header="加密流程" Icon="&#xe670;"/>
                                    <MenuItem Header="解密流程" Icon="&#xe667;"/>
                                    <MenuItem Header="取消所有加密" Icon="&#xe667;"/>
                                </MenuItem>-->
                                <!--<MenuItem Header="创建文件夹" Icon="&#xe646;" Click="miCreateFolder_Click"/>-->
                                <Separator/>
                                <MenuItem Header="编辑注释" Icon="&#xe61e;" Click="miEditRemarks_Click" IsEnabled="{Binding Source={StaticResource IsEnableControl}, Path=Ins.Stop, Converter={StaticResource InvertBoolConverter}}"/>
                                <!--<MenuItem Header="剪切" Icon="&#xe61b;" Click="miCut_Click"/>-->
                                <!--<MenuItem Header="复制" Icon="&#xe623;" Click="miCopy_Click" IsEnabled="{Binding Source={StaticResource IsEnableControl}, Path=Ins.Stop, Converter={StaticResource InvertBoolConverter}}"/>-->
                                <MenuItem Header="删除流程" Icon="&#xe8b6;" Click="miDeleteProcess_Click" IsEnabled="{Binding Source={StaticResource IsEnableControl}, Path=Ins.Stop, Converter={StaticResource InvertBoolConverter}}"/>
                            </ContextMenu>
                        </ScrollViewer.ContextMenu>
                    </ScrollViewer>
                </HierarchicalDataTemplate>
            </TreeView.ItemTemplate>
        </TreeView>
    </Grid>
</UserControl>
