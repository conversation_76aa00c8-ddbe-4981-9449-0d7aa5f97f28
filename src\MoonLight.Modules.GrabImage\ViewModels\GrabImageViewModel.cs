﻿using MoonLight.Core.Attributes;
using MoonLight.Core.Common.Log;
using MoonLight.Core.Config;
using MoonLight.Core.Enums;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Modules.GrabImage.Models;
using MoonLight.Modules.GrabImage.Views;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Windows.Forms;
using System.Linq;
using MoonLight.Core.Events;
using EventMgrLib;
using MoonLight.Core.ViewModels;
using MoonLight.Modules.Algorithms.Algorithms;
using MoonLight.Core.Devices.Camera;
using HalconDotNet;
using MoonLight.Modules.RenderControl.Managers;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Windows.Input;
using MoonLight.UI.Framework;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using MoonLight.Core.Modules;
using MoonLight.Core.Devices;

namespace MoonLight.Modules.GrabImage.ViewModels
{
    #region enum
    public enum Link
    {
        ExposureTime,
        Gain
    }
    #endregion

    [Category("图像处理")]
    [DisplayName("采集图像")]
    [ToolImageName("GrabImage")]
    [Serializable]
    public class GrabImageViewModel : ToolUnit
    {
        [NonSerialized]
        private IRenderView mWindowH;

        [NonSerialized]
        public CameraBase SelectedCamera;

        // 采集魔术数据,用来显示列表数据源
        [field: NonSerialized()]
        public Array AcquisitionModes { get; set; } = Enum.GetValues(typeof(TrigMode));

        // 相机列表
        public ObservableCollection<ImageNameModel> ImageNameModels { get; set; } = new ObservableCollection<ImageNameModel>();

        // 图像索引
        public int _SelectedIndex = 0;
        public int SelectedIndex
        {
            get { return _SelectedIndex; }
            set { Set(ref _SelectedIndex, value); }
        }

        // 窗口ID
        private int _DispViewID = 0;
        public int DispViewID
        {
            get { return _DispViewID; }
            set
            {
                Set(ref _DispViewID, value, new System.Action(() =>
                {
                    if (DispImage != null)
                    {
                        DispImage.DispViewID = _DispViewID;
                    }
                    Prj.DispViewID = _DispViewID;
                }));
            }
        }  

        // 循环读取
        private bool _IsCyclicRead = true;
        public bool IsCyclicRead
        {
            get { return _IsCyclicRead; }
            set { Set(ref _IsCyclicRead, value); }
        }

        // 图片路径
        private string _ImagePath;
        public string ImagePath
        {
            get { return _ImagePath; }
            set { Set(ref _ImagePath, value); }
        }

        // 图片文件夹路径
        private string _FilePath;
        public string FilePath
        {
            get { return _FilePath; }
            set { Set(ref _FilePath, value); }
        }

        // 当前采集模式
        private TrigMode _AcquisitionMode = TrigMode.软触发;
        public TrigMode AcquisitionMode
        {
            get { return _AcquisitionMode; }
            set
            {
                Set(ref _AcquisitionMode, value);
            }
        }

        // 曝光时间
        private LinkVarModel _ExposureTime = new LinkVarModel() { Text = "10000" };
        public LinkVarModel ExposureTime
        {
            get { return _ExposureTime; }
            set { Set(ref _ExposureTime, value); }
        }

        // 增益
        private LinkVarModel _Gain = new LinkVarModel() { Text = "0" };
        public LinkVarModel Gain
        {
            get { return _Gain; }
            set { Set(ref _Gain, value); }
        }

        // 内容标题
        private string _ContentHeader = "文件目录";
        public string ContentHeader
        {
            get { return _ContentHeader; }
            set { Set(ref _ContentHeader, value); }
        }

        // 指定图像
        private ImageSource _ImageSource = ImageSource.指定图像;
        public ImageSource ImageSource
        {
            get { return _ImageSource; }
            set
            {
                Set(ref _ImageSource, value, new System.Action(() =>
                {
                    switch (_ImageSource)
                    {
                        case ImageSource.指定图像:
                            ContentHeader = "指定图像";
                            break;
                        case ImageSource.文件目录:
                            ContentHeader = "文件目录";
                            break;
                        case ImageSource.相机采集:
                            ContentHeader = "相机采集";
                            break;
                        default:
                            break;
                    }
                }));
            }
        }

        // 选择文件
        private bool _SpecifiedImage_SelectFile = true;
        public bool SpecifiedImage_SelectFile
        {
            get { return _SpecifiedImage_SelectFile; }
            set
            {
                Set(ref _SpecifiedImage_SelectFile, value);
            }
        }

        // 链接路径
        private bool _SpecifiedImage_LinkPath;
        public bool SpecifiedImage_LinkPath
        {
            get { return _SpecifiedImage_LinkPath; }
            set
            {
                Set(ref _SpecifiedImage_LinkPath, value);
            }
        }

        [field: NonSerialized]
        public List<string> CameraNames
        {
            get
            {
                List<string> names = new List<string>();
                var list = IoC.Get<IDeviceManager>().GetCamDevices();
                
                foreach (var item in list)
                {
                    names.Add(item.Name);
                }
                return names;
            }
        }

        private string _SelectedCamName;
        public string SelectedCamName
        {
            get { return _SelectedCamName; }
            set { Set(ref _SelectedCamName, value, () => { SelectedCamera = (CameraBase)IoC.Get<IDeviceManager>().GetDeviceByName(_SelectedCamName); }); }
        }

        [field: NonSerialized]
        public ICommand SelectedImagePathCommand { get; private set; }
        [field: NonSerialized]
        public ICommand SelectedFilePathCommand { get; private set; }
        [field: NonSerialized]
        public ICommand ExecuteCommand { get; private set; }
        [field: NonSerialized]
        public ICommand ConfirmCommand { get; private set; }
        [field: NonSerialized]
        public ICommand PreviewMouseDoubleClickCommand { get; private set; }
        [field: NonSerialized]
        public ICommand SelectedLinkCommand { get; private set; }
        [field: NonSerialized]
        public ICommand DropDownOpenedCommand { get; private set; }

        public GrabImageViewModel()
        {
            //以GUID+类名作为筛选器
            EventMgr.Ins.GetEvent<VarChangedEvent>().Subscribe(OnVarChanged, item => item.SendName.StartsWith($"{GUID}"));
            SelectedFilePathCommand = new RelayCommand(SelectedFilePath);
            SelectedImagePathCommand = new RelayCommand(SelectedImagePath);
            ExecuteCommand = new RelayCommand(Execute);
            ConfirmCommand = new RelayCommand(Confirm);
            PreviewMouseDoubleClickCommand = new RelayCommand(PreviewMouseDoubleClick);
            SelectedLinkCommand = new RelayCommand(SelectedLink);
            DropDownOpenedCommand = new RelayCommand(DropDownOpened);
        }

        private void SelectedImagePath(object o)
        {
            Microsoft.Win32.OpenFileDialog dlg = new Microsoft.Win32.OpenFileDialog();
            dlg.Filter = "所有图像文件 | *.bmp; *.pcx; *.png; *.jpg; *.gif;*.tif; *.ico; *.dxf; *.cgm; *.cdr; *.wmf; *.eps; *.emf";
            if (dlg.ShowDialog() == true)
            {
                ImagePath = dlg.FileName;
                InternalRun();
            }
        }

        private void SelectedFilePath(object o)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                FilePath = dialog.SelectedPath;
                var files = Directory.GetFiles(FilePath, "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".jpg") || s.EndsWith(".gif") || s.EndsWith(".png") || s.EndsWith(".bmp") || s.EndsWith(".jpg") || s.EndsWith(".eps") || s.EndsWith(".tif"));
                if (files.Any())
                {
                    var names = files.ToList();
                    ImageNameModels.Clear();
                    for (int i = 0; i < names.Count; i++)
                    {
                        ImageNameModels.Add(new ImageNameModel() { ID = i + 1, ImagePath = names[i], ImageName = Path.GetFileName(names[i]), IsSelected = true });
                    }
                    DispImage = new RImage(DispViewID);
                    if (ImageNameModels != null && ImageNameModels.Count > 0 && File.Exists(ImageNameModels[0].ImagePath))
                    {
                        SelectedIndex = 0;
                        DispImage.ReadImage(ImageNameModels[0].ImagePath);
                        if (DispImage != null && DispImage.IsInitialized())
                        {
                            mWindowH.DispImage(DispImage);
                            mWindowH.SetFullImagePart();
                            VisionLib.MV_GetImageSize(DispImage, out DispImage.Width, out DispImage.Height);
                        }
                    }
                    if (DispImage != null && DispImage.IsInitialized())
                    {
                        var view = ToolView as GrabImageView;
                        if (view == null || view.IsClosed)
                        {
                            IoC.Get<IRenderViewManager>().GetView(DispViewID).DispImage(DispImage);
                        }
                        VisionLib.MV_GetImageSize(DispImage, out DispImage.Width, out DispImage.Height);
                        ChangeToolRunStatus(RunStatus.OK);
                    }
                    else
                    {
                        ChangeToolRunStatus(RunStatus.NG);
                    }

                }
            }
        }

        private void Execute(object o)
        {
            InternalRun();
        }

        private void Confirm(object o)
        {
            var view = this.ToolView as GrabImageView;
            if (view != null)
            {
                view.Close();
            }
        }

        private void PreviewMouseDoubleClick(object o)
        {
            DispImage = new RImage(DispViewID);
            if (File.Exists(ImageNameModels[SelectedIndex].ImagePath))
            {
                DispImage.ReadImage(ImageNameModels[SelectedIndex].ImagePath);
                if (DispImage != null && DispImage.IsInitialized())
                {
                    mWindowH.DispImage(DispImage);
                    mWindowH.SetFullImagePart();
                }
            }
            if (DispImage != null && DispImage.IsInitialized())
            {
                //GrabImageView view = ToolView as GrabImageView;
                //if (view == null || view.IsClosed)
                //{
                //    IoC.Instance.Get<IRenderViewManager>().GetView(DispViewID).DispImage(DispImage);
                //}
                //ChangeToolRunStatus(RunStatus.OK);
            }
            else
            {
                ChangeToolRunStatus(RunStatus.NG);
            }
        }

        private void SelectedLink(object o)
        {
            Link link = (Link)o;
            switch (link)
            {
                case Link.ExposureTime:
                    CommonMethods.GetToolList(this, VarLinkViewModel.Ins.Tools, "int");
                    EventMgr.Ins.GetEvent<OpenVarLinkViewEvent>().Publish($"{GUID},ExposureTimeLink");
                    break;
                case Link.Gain:
                    CommonMethods.GetToolList(this, VarLinkViewModel.Ins.Tools, "int");
                    EventMgr.Ins.GetEvent<OpenVarLinkViewEvent>().Publish($"{GUID},GainLink");
                    break;
                default:
                    break;
            }
        }

        private void DropDownOpened(object o)
        {

        }

        [NonSerialized]
        private GrabImagePropertyView propertyView;
        public override System.Windows.Controls.UserControl GetUserControl()
        {
            if(propertyView == null)
            {
                propertyView = new GrabImagePropertyView();
                propertyView.DataContext = this;
            }
           
            return propertyView;
        }

        public override void Loaded(IRenderView renderView, System.Windows.Controls.UserControl propertyView = null)
        {
            var view = ToolView as GrabImageView;
            if (view != null && !view.IsClosed)
            {
                ClosedView = true;
                mWindowH = IoC.Get<IRenderViewManager>().GenRenderView();
                view.winFormHost.Child = (System.Windows.Forms.Control)mWindowH;
            }
            else
            {
                if (renderView == null)
                    renderView = IoC.Get<IRenderViewManager>().GetView(Prj.DispViewID);
                mWindowH = renderView;
            }
            if (DispImage != null && DispImage.IsInitialized())
            {
                mWindowH.ClearWindow();
                mWindowH.DispImage(DispImage);
                mWindowH.SetFullImagePart();
            }
            else
            {
                mWindowH.ClearWindow();
            }
        }

        private void OnVarChanged(VarChangedEventParamModel obj)
        {
            switch (obj.SendName.Split(',')[1])
            {
                case "ExposureTimeLink":
                    ExposureTime.Text = obj.LinkName;
                    break;
                case "GainLink":
                    Gain.Text = obj.LinkName;
                    break;
                default:
                    break;
            }
        }

        public override bool Run()
        {

            try
            {
                if (DispImage != null && DispImage.IsInitialized())
                {
                    DispImage.Dispose();
                }
                DispImage = new RImage(DispViewID);

                switch (ImageSource)
                {

                    case ImageSource.指定图像:
                        if (!File.Exists(ImagePath))
                        {
                            break;
                        }
                        DispImage.ReadImage(ImagePath);
                        break;

                    case ImageSource.文件目录:
                        if (ImageNameModels == null || ImageNameModels.Count == 0)
                        {
                            break;
                        }
                        if (IsCyclicRead)
                        {
                            SelectedIndex++;
                            SelectedIndex = SelectedIndex >= ImageNameModels.Count ? 0 : SelectedIndex;
                        }
                        if (File.Exists(ImageNameModels[SelectedIndex].ImagePath))
                        {
                            //RenderViewManager.ViewList[ViewerId].SetViewParam("flush_graphic", true);
                            DispImage.ReadImage(ImageNameModels[SelectedIndex].ImagePath);
                         
                            //Thread.Sleep(10);
                        }

                        break;

                    case ImageSource.相机采集:
                        if (SelectedCamera == null)
                        {
                            ChangeToolRunStatus(RunStatus.NG);
                            break;
                        }
                        if (SelectedCamera.IsOpen())
                        {
                            var ret =  VisionLib.MV_ImageGrab(SelectedCamera, out HImage hImage);
                  
                            if (hImage != null && hImage.IsInitialized())
                            {
                                int viewId = DispImage.DispViewID;
                                DispImage = new RImage(hImage);
                                DispImage.DispViewID = viewId;
                            }
                            ChangeToolRunStatus(RunStatus.OK);
                        }
                        else
                        {
                            //Logger.AddLog(Name + ":" + SelectedCamera.CameraNo + "相机未连接!", MsgType.Warn);
                            ChangeToolRunStatus(RunStatus.NG);
                        }
                        break;

                    default:
                        break;
                }
                //AddOutputParams();

                if (DispImage != null && DispImage.IsInitialized())
                {
                    VisionLib.MV_GetImageSize(DispImage, out DispImage.Width, out DispImage.Height);
                    if (NeedRender)
                    {
                        RenderViewManager.RegisterFrame(() =>
                        {
                            System.Windows.Application.Current.Dispatcher.Invoke(() =>
                            {
                                RenderViewManager.ViewList[ViewerId].SetViewParam("flush_graphic", false);
                                RenderViewManager.ViewList[ViewerId].ClearWindow();
                                RenderViewManager.ViewList[ViewerId].DispImage(DispImage);
                                RenderViewManager.ViewList[ViewerId].SetViewParam("flush_graphic", true);
                            });

                        });
                    }
                    ChangeToolRunStatus(RunStatus.OK);
                    return true;
                }
                else
                {
                    ChangeToolRunStatus(RunStatus.NG);
                    return false;
                }




            }
            catch (Exception ex)
            {
                ChangeToolRunStatus(RunStatus.NG);
                Logger.AddLog(ex.Message, MsgType.Error);
                return false;
            }
        }

        public override void AddOutputParams()
        {
            base.AddOutputParams();
            AddOutputParam("图像", "HImage", DispImage);
        }

        [OnDeserialized()] //反序列化之后
        internal void OnDeserializedMethod(StreamingContext context)
        {
            //以GUID+类名作为筛选器
            EventMgr.Ins.GetEvent<VarChangedEvent>().Subscribe(OnVarChanged, item => item.SendName.StartsWith($"{GUID}"));
            SelectedFilePathCommand = new RelayCommand(SelectedFilePath);
            SelectedImagePathCommand = new RelayCommand(SelectedImagePath);
            ExecuteCommand = new RelayCommand(Execute);
            ConfirmCommand = new RelayCommand(Confirm);
            PreviewMouseDoubleClickCommand = new RelayCommand(PreviewMouseDoubleClick);
            SelectedLinkCommand = new RelayCommand(SelectedLink);
            DropDownOpenedCommand = new RelayCommand(DropDownOpened);

            if (SelectedCamName == null || SelectedCamName == "")
            {
                return;
            }
            SelectedCamera = (CameraBase)IoC.Get<IDeviceManager>().GetDeviceByName(_SelectedCamName);
        }

        protected override void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    DispImage?.Dispose();
                    DispImage = null;
                    HomMat2D?.Dispose();
                    HomMat2D = null;
                    HomMat2D_Inverse?.Dispose();
                    HomMat2D_Inverse = null;
                    HRoiList?.Clear();
                    HRoiList = null;
                }

                _disposed = true;
            }
        }
    }

   
}
