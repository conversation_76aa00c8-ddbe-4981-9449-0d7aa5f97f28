﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MoonLight.UI.Framework;

namespace MoonLight.Core.Assets.Modules.PosManager.ViewModels
{
    [DisplayName("点位管理器")]
    [Export]
    [PartCreationPolicy(CreationPolicy.Shared)]
    public class PosManagerViewModel : WindowBase
    {

    }
}
