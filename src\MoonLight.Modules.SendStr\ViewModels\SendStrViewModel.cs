﻿using EventMgrLib;
using MoonLight.Core.Attributes;
using MoonLight.Core.Common.Log;
using MoonLight.Core.Devices;
using MoonLight.Core.Devices.Communication;
using MoonLight.Core.Enums;
using MoonLight.Core.Events;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Core.Modules;
using MoonLight.Core.ViewModels;
using MoonLight.Modules.SendStr.Views;
using MoonLight.UI.Framework;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Runtime.Serialization;
using System.Windows.Controls;
using System.Windows.Input;

namespace MoonLight.Modules.SendStr.ViewModels
{
    [Category("文件通讯")]
    [DisplayName("发送文本")]
    [ToolImageName("SendStr")]
    [Serializable]
    public class SendStrViewModel : ToolUnit
    {
        [field: NonSerialized]
        public CommunicationBase SelectedComm { get; set; }

        private bool _Continue = false;
        public bool Continue
        {
            get { return _Continue; }
            set { _Continue = value; }
        }

        // 发送内容
        private LinkVarModel _SendStr = new LinkVarModel() { Value = "发送文本" };
        public LinkVarModel SendStr
        {
            get { return _SendStr; }
            set { Set(ref _SendStr, value); }
        }

        // 是否按16进制发送
        private bool _IsSendByHex = false;
        public bool IsSendByHex
        {
            get { return _IsSendByHex; }
            set { Set(ref _IsSendByHex, value); }
        }

        // 启用超时
        private bool _IsEnableTimeOut = false;
        public bool IsEnableTimeOut
        {
            get { return _IsEnableTimeOut; }
            set { Set(ref _IsEnableTimeOut, value); }
        }

        // 结尾字符串
        private EnableEndStr _EnableEndstr = EnableEndStr.No;
        public EnableEndStr EnableEndstr
        {
            get { return _EnableEndstr; }
            set { _EnableEndstr = value; }
        }

        // 当前Name
        private string _CurName = "";
        public string CurName
        {
            get { return _CurName; }
            set
            {
                Set(ref _CurName, value, new System.Action(() =>
                {
                    SelectedComm = null;
                    IDeviceManager manager = IoC.Get<IDeviceManager>();
                    if (manager != null)
                    {
                        SelectedComm = (CommunicationBase)manager.GetDeviceByName(CurName);
                        CommRemarks = SelectedComm?.Remarks;
                    }
                }));
            }
        }

        // 备注
        private string _CommRemarks;
        public string CommRemarks
        {
            get { return _CommRemarks; }
            set { Set(ref _CommRemarks, value); }
        }

        // 所有创建的通讯的名称
        private List<string> _ComNames = null;
        public List<string> ComNames
        {
            get
            {
                if (_ComNames == null)
                {
                    _ComNames = new List<string>();
                }
                return _ComNames;
            }
            set { Set(ref _ComNames, value); }
        }

        [field: NonSerialized]
        public ICommand SelectedLinkCommand { get; private set; }
        [field: NonSerialized]
        public ICommand ConfirmCommand { get; private set; }
        [field: NonSerialized]
        public ICommand CancelCommand {  get; private set; }
        [field: NonSerialized]
        public ICommand ExecuteCommand { get; private set; }

        public SendStrViewModel()
        {
            //以类名作为筛选器
            EventMgr.Ins.GetEvent<VarChangedEvent>().Subscribe(OnVarChanged, item => item.SendName.StartsWith($"{GUID}"));
            SelectedLinkCommand = new RelayCommand(SelectedLink);
            ConfirmCommand = new RelayCommand(Confirm);
            CancelCommand = new RelayCommand(Cancel);
            ExecuteCommand = new RelayCommand(Execute);
        }

        private void SelectedLink(object o)
        {
            CommonMethods.GetToolList(this, VarLinkViewModel.Ins.Tools, "string,double,int,bool");
            EventMgr.Ins.GetEvent<OpenVarLinkViewEvent>().Publish($"{GUID},StringLinkText");
        }

        private void Confirm(object o)
        {
            var view = this.ToolView as SendStrView;
            if (view != null)
            {
                view.Close();
            }
        }

        private void Cancel(object o)
        {
            var view = this.ToolView as SendStrView;
            if (view != null)
            {
                view.Close();
            }
        }

        private void Execute(object o)
        {
            InternalRun();
        }

        public override bool Run()
        {
            Stopwatch.Restart();
            try
            {
                if (SelectedComm == null)
                {
                    ChangeToolRunStatus(RunStatus.NG);
                    return false;
                }
                if (!SendStr.Text.StartsWith("&"))
                {
                    if (EnableEndstr == EnableEndStr.Have)
                    {
                        SelectedComm.IsSendByHex = IsSendByHex;
                        SelectedComm.SendStr(SendStr.Value.ToString() + "\r\n");
                    }
                    else
                    {
                        SelectedComm.IsSendByHex = IsSendByHex;
                        SelectedComm.SendStr(SendStr.Value.ToString());
                    }
                }
                else
                {
                    object str = Prj.GetParamByName(SendStr.Text).Value;
                    if (EnableEndstr == EnableEndStr.Have)
                    {
                        SelectedComm.IsSendByHex = IsSendByHex;
                        SelectedComm.SendStr(str.ToString() + "\r\n");
                    }
                    else
                    {
                        SelectedComm.IsSendByHex = IsSendByHex;
                        SelectedComm.SendStr(str.ToString());
                    }
                }
                if (Continue)
                {
                    Prj.ExeToolName = "";
                }
                ChangeToolRunStatus(RunStatus.OK);
                return true;
            }
            catch (Exception ex)
            {
                ChangeToolRunStatus(RunStatus.NG);
                Logger.AddLog(ex.Message, MsgType.Error);
                return false;
            }
        }
        public override void AddOutputParams()
        {
            base.AddOutputParams();
        }
        
        [NonSerialized]
        SendStrPropertyView propertyView = null;
        public override UserControl GetUserControl()
        {
            if (propertyView == null)
            {
                propertyView = new SendStrPropertyView();
                propertyView.DataContext = this;
            }
            return propertyView;
        }

        public override void Loaded(IRenderView renderView, UserControl propertyView = null)
        {
            var str = CurName;
            ComNames = new List<string>();
            IDeviceManager manager = IoC.Get<IDeviceManager>();
            if (manager != null)
            {
                var networkAndSerialDevices = manager.DeviceGroups
                    .Where(g => g.Category == DeviceCategory.NetworkDevice ||
                                g.Category == DeviceCategory.SerialDevice)
                    .SelectMany(g => g.Devices)
                    .Select(d => d.Name);
                ComNames.AddRange(networkAndSerialDevices);
                if (str != null && str != string.Empty)
                {
                    CurName = str;
                }
            }

            var view = ToolView as SendStrView;
            if (view != null && !view.IsClosed)
            {
                ClosedView = true;
            }
            else
            {
                ClearWindow(renderView);
            }
        }

        private void OnVarChanged(VarChangedEventParamModel obj)
        {

            switch (obj.SendName.Split(',')[1])
            {
                case "StringLinkText":
                    SendStr.Text = obj.LinkName;
                    break;
                default:
                    break;
            }
        }

        [OnDeserialized()] //反序列化之后
        internal void OnDeserializedMethod(StreamingContext context)
        {
            //以类名作为筛选器
            EventMgr.Ins.GetEvent<VarChangedEvent>().Subscribe(OnVarChanged, item => item.SendName.StartsWith($"{GUID}"));
            SelectedLinkCommand = new RelayCommand(SelectedLink);
            ConfirmCommand = new RelayCommand(Confirm);
            CancelCommand = new RelayCommand(Cancel);
            ExecuteCommand = new RelayCommand(Execute);
        }
    }
}
