﻿using MoonLight.App.ViewModels;
using MoonLight.Core.ROIs;
using System.Collections.Generic;
using System.Windows.Controls;

namespace MoonLight.App.Views
{
    /// <summary>
    /// TestView.xaml 的交互逻辑
    /// </summary>
    public partial class TestView : UserControl
    {
        public TestView()
        {
            InitializeComponent();
            this.DataContext = new TestViewModel();
        }

    }
}
