﻿using MoonLight.UI.Framework.Commands;

namespace MoonLight.Modules.FlowManager.Commands
{
    [CommandDefinition]
    public class ViewPropertyPanelCommandDefinition : CommandDefinition
    {
        public const string CommandName = "View.PropertyPanel";

        public override string Name
        {
            get { return CommandName; }
        }

        public override string Text
        {
            get { return "属性栏"; }
        }

        public override string ToolTip
        {
            get { return "属性栏"; }
        }
    }
}
