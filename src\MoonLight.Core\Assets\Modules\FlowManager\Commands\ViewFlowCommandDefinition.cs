﻿using MoonLight.UI.Framework.Commands;

namespace MoonLight.Modules.FlowManager.Commands
{
    [CommandDefinition]
    public class ViewFlowCommandDefinition : CommandDefinition
    {
        public const string CommandName = "View.Flow";

        public override string Name
        {
            get { return CommandName; }
        }

        public override string Text
        {
            get { return "流程"; }
        }

        public override string ToolTip
        {
            get { return "流程"; }
        }
    }
}
