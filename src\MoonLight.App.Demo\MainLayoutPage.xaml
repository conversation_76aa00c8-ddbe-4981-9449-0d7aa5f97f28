﻿<Page x:Class="MoonLight.App.Demo.MainLayoutPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MoonLight.App.Demo"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Title="MainLayoutPage">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="100"/>
            <!-- 左侧边栏宽度 -->
            <ColumnDefinition Width="*"/>
            <!-- 右侧内容区域 -->
        </Grid.ColumnDefinitions>

        <!-- 左侧边栏 -->
        <Border Grid.Column="0" Background="#FFE0E0E0" BorderBrush="Gray" BorderThickness="0,0,1,0">
            <StackPanel Margin="10">
                <TextBlock Text="导航菜单" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                <Button Content="页面 1" Click="NavigateToPage1_Click" Margin="0,5"/>
                <Button Content="页面 2" Click="NavigateToPage2_Click" Margin="0,5"/>
                <Button Content="页面 3" Click="NavigateToPage3_Click" Margin="0,5"/>
                <!-- 可以使用 ListBox 或 ItemsControl 来动态生成导航项 -->
            </StackPanel>
        </Border>

        <!-- 右侧内容框架 -->
        <Frame Grid.Column="1" x:Name="ContentFrame" NavigationUIVisibility="Hidden"/>
        <!-- NavigationUIVisibility="Hidden" 避免 Frame 自身显示导航按钮 -->
    </Grid>
</Page>
