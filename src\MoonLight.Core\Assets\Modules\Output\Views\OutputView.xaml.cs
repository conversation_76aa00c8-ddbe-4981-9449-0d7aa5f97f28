﻿using MoonLight.Modules.Output.Models;
using System.Windows;
using System.Windows.Controls;

namespace MoonLight.Modules.Output.Views
{
    /// <summary>
    /// OutputView.xaml 的交互逻辑
    /// </summary>
    public partial class OutputView : UserControl, IOutputView
    {
        public OutputView()
        {
            InitializeComponent();
        }

        public void ScrollToEnd()
        {
            //outputText.ScrollToEnd();
        }

        public void Clear()
        {
            //outputText.Clear();
        }

        public void AppendText(string text)
        {
            // outputText.AppendText(text);
            // ScrollToEnd();
        }

        public void SetText(string text)
        {
            //outputText.Text = text;
            // ScrollToEnd();
        }

        private void btnScrollIntoTop_Click(object sender, RoutedEventArgs e)
        {

        }

        private void btnScrollIntoEnd_Click(object sender, RoutedEventArgs e)
        {

        }
    }
}
