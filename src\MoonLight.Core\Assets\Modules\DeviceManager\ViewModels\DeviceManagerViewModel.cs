﻿using MoonLight.UI.Framework;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.Composition;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using MoonLight.Core.Devices;
using System.Windows;
using MoonLight.Core.Devices.Camera;
using System.Linq;
using MoonLight.Core.Enums;
using MoonLight.Core.Models;
using MoonLight.Core.Services;
using EventMgrLib;
using MoonLight.Core.Events;
using System.Windows.Media.Media3D;
using Newtonsoft.Json;
using System.Runtime.CompilerServices;
using MoonLight.Core.Motion;
using MoonLight.Core.Devices.Communication;


namespace MoonLight.Core.Modules
{
    [DisplayName("设备管理器")]
    [Export]
    [PartCreationPolicy(CreationPolicy.Shared)]
    public class DeviceManagerViewModel : WindowBase
    {

        private DeviceManager _deviceManager => DeviceManager.Instance;

        public List<DeviceCategoryGroup> DeviceGroups => _deviceManager.DeviceGroups;

        // 分组工具箱
        public ObservableCollection<DeviceToolboxGroup> ToolboxGroups { get; } = new ObservableCollection<DeviceToolboxGroup>();


        private string _editingDeviceName;
        public string EditingDeviceName
        {
            get => _editingDeviceName;
            set
            {
                if (_editingDeviceName != value)
                {
                    _editingDeviceName = value;
                    NotifyOfPropertyChange();
                }
            }
        }


        private IDevice _selectedDevice;
        public IDevice SelectedDevice
        {
            get => _selectedDevice;
            set
            {
                if (_selectedDevice != value)
                {
                    _selectedDevice = value;
                    EditingDeviceName = _selectedDevice?.Name;
                    NotifyOfPropertyChange();
                    _deviceManager.CurrentConfigurationUI = _selectedDevice?.GetConfigurationView();
                    NotifyOfPropertyChange(nameof(CurrentConfigurationUI));
                }
            }
        }
        public string Error => null;
        public string this[string columnName]
        {
            get
            {
                if (columnName == nameof(EditingDeviceName))
                {
                    if (string.IsNullOrWhiteSpace(EditingDeviceName))
                        return "设备名称不能为空";

                    if (_deviceManager.DeviceGroups
                        .SelectMany(g => g.Devices)
                        .Any(d => d.Name == EditingDeviceName && d != SelectedDevice))
                        return "设备名称已存在";
                }
                return null;
            }
        }

        // 添加重命名状态属性
        private bool _isRenaming;
        public bool IsRenaming
        {
            get => _isRenaming;
            set => Set(ref _isRenaming, value);
        }



        // 当前拖拽的设备模板
        private DeviceTemplate _draggedItem;
        public DeviceTemplate DraggedItem
        {
            get => _draggedItem;
            set => Set(ref _draggedItem, value);
        }

        public FrameworkElement CurrentConfigurationUI => _deviceManager.CurrentConfigurationUI;

        public ICommand AddCameraCommand { get; }
        public ICommand AddBarcodeReaderCommand { get; }
        public ICommand AddNetworkDeviceCommand { get; }
        public ICommand RemoveDeviceCommand { get; }
        public ICommand SaveConfigurationCommand { get; }
        public ICommand LoadConfigurationCommand { get; }
        public ICommand StartDragCommand { get; private set; }
        public ICommand DropCommand { get; private set; }




        public DeviceManagerViewModel( )
        {
          
            _deviceManager.ConfigurationUIChanged += (s, e) => NotifyOfPropertyChange(nameof(CurrentConfigurationUI));

            AddCameraCommand = new RelayCommand(AddCamera);
            AddBarcodeReaderCommand = new RelayCommand(AddBarcodeReader);
            AddNetworkDeviceCommand = new RelayCommand(AddNetworkDevice);

            SaveConfigurationCommand = new RelayCommand(SaveConfiguration);
            LoadConfigurationCommand = new RelayCommand(LoadConfiguration);
            StartDragCommand = new RelayCommand<DeviceTemplate>(StartDrag);
            DropCommand = new RelayCommand<DeviceCategory>(Drop);
            RemoveDeviceCommand = new RelayCommand(RemoveDevice);

            // 初始化工具箱
            InitializeToolbox();
        }





        private void InitializeToolbox()
        {
            // 视觉设备组
            var visionGroup = new DeviceToolboxGroup("工业相机");
            visionGroup.Items.Add(new DeviceTemplate("海康相机", DeviceCategory.Camera, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            visionGroup.Items.Add(new DeviceTemplate("大恒相机", DeviceCategory.Camera, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            visionGroup.Items.Add(new DeviceTemplate("Basler相机", DeviceCategory.Camera, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            ToolboxGroups.Add(visionGroup);

            // 识别设备组
            var recognitionGroup = new DeviceToolboxGroup("识别设备");
            recognitionGroup.Items.Add(new DeviceTemplate("条码阅读器", DeviceCategory.BarcodeReader, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            recognitionGroup.Items.Add(new DeviceTemplate("OCR阅读器", DeviceCategory.BarcodeReader, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            ToolboxGroups.Add(recognitionGroup);

            // 通信设备组
            var commGroup = new DeviceToolboxGroup("通信设备");
            commGroup.Items.Add(new DeviceTemplate("TCP客户端", DeviceCategory.NetworkDevice, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            commGroup.Items.Add(new DeviceTemplate("TCP服务器", DeviceCategory.NetworkDevice, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            commGroup.Items.Add(new DeviceTemplate("UDP通讯", DeviceCategory.NetworkDevice, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            commGroup.Items.Add(new DeviceTemplate("串口通讯", DeviceCategory.SerialDevice, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            commGroup.Items.Add(new DeviceTemplate("以太网设备", DeviceCategory.NetworkDevice, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            commGroup.Items.Add(new DeviceTemplate("Profinet设备", DeviceCategory.NetworkDevice, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            ToolboxGroups.Add(commGroup);

            // 控制设备组
            var controlGroup = new DeviceToolboxGroup("控制设备");
            controlGroup.Items.Add(new DeviceTemplate("Pmac运动控制卡", DeviceCategory.MotionController, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            controlGroup.Items.Add(new DeviceTemplate("固高运动控制卡", DeviceCategory.MotionController, "/MoonLight.Core;component/Assets/Images/Solution/AddSol.png"));
            ToolboxGroups.Add(controlGroup);
        }

        private void StartDrag(DeviceTemplate template)
        {
            DraggedItem = template;
        }

        private void Drop(DeviceCategory category)
        {
            try
            {
                if (DraggedItem == null) return;

                IDevice newDevice = null;
                PluginsInfo m_PluginsInfo = null;
                switch (DraggedItem.Category)
                {
                    case DeviceCategory.Camera:
                         m_PluginsInfo = PluginService.PluginDic_Device[DraggedItem.Name];
                        newDevice = (CameraBase)Activator.CreateInstance(m_PluginsInfo.ToolType);
                        break;

                    case DeviceCategory.SerialDevice:

                         m_PluginsInfo = PluginService.PluginDic_Communication[DraggedItem.Name];
                        newDevice = (CommunicationBase)Activator.CreateInstance(m_PluginsInfo.ToolType);

                        break;
                    case DeviceCategory.NetworkDevice:

                         m_PluginsInfo = PluginService.PluginDic_Communication[DraggedItem.Name];
                        newDevice = (CommunicationBase)Activator.CreateInstance(m_PluginsInfo.ToolType);

                        break;

                    case DeviceCategory.MotionController:
                        m_PluginsInfo = PluginService.PluginDic_Device[DraggedItem.Name];
                        newDevice = (ICard)Activator.CreateInstance(m_PluginsInfo.ToolType);

                        break;
                }

                if (newDevice != null)
                {
                    _deviceManager.AddDevice(newDevice);
                }

                DraggedItem = null;
            }
            catch (Exception ex)
            {
            }
        }

        private string GetUniqueDeviceName(string baseName)
        {
            int counter = 1;
            string newName = baseName;

            while (_deviceManager.DeviceGroups
                   .SelectMany(g => g.Devices)
                   .Any(d => d.Name == newName))
            {
                newName = $"{baseName} ({counter++})";
            }

            return newName;
        }

        private void AddCamera(object o)
        {
            _deviceManager.AddDevice(new CameraBase());
        }

        private void AddBarcodeReader(object o)
        {
            _deviceManager.AddDevice(new CameraBase());
        }

        private void AddNetworkDevice(object o)
        {
            _deviceManager.AddDevice(new CameraBase());
        }

        private bool CanRemoveDevice(object o)
        {
            return SelectedDevice != null;
        }

        private void RemoveDevice(object o)
        {
            if (SelectedDevice != null)
            {
                
                var result = MessageBox.Show($"确定要删除设备 '{SelectedDevice.Name}' 吗?", "确认删除",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    if (SelectedDevice.IsOpen())
                    {
                        SelectedDevice.Close();
                    }
                    _deviceManager.RemoveDevice(SelectedDevice.Id);
                }
            }
        }

        private void SaveConfiguration(object o)
        {
            try
            {
                _deviceManager.SaveDeviceConfiguration();
                MessageBox.Show("配置保存成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadConfiguration(object o)
        {
            try
            {
                _deviceManager.LoadDeviceConfiguration();
                MessageBox.Show("配置加载成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        protected override Task OnDeactivateAsync(bool close, CancellationToken cancellationToken)
        {
            _deviceManager.SaveDeviceConfiguration();
            return Task.FromResult(true);
        }

    }
}
