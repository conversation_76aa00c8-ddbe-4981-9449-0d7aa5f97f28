﻿<UserControl x:Class="MoonLight.Modules.FlowManager.Views.PropertyPanelView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

        <!--<ContentControl Content="{Binding PropertyControl}" IsEnabled="{Binding IsEnabled}"/>-->
        <!--<Frame Content="{Binding PropertyControl}" NavigationUIVisibility="Hidden"/>Content="{Binding PropertyControl}"-->
    <!--<ContentPresenter x:Name="fe" Content="{Binding PropertyControl}"/>-->

    <Grid Name="Grid">
        
    </Grid>

</UserControl>
