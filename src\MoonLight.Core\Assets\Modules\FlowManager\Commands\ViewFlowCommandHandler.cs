﻿using MoonLight.Modules.FlowManager.ViewModels;
using MoonLight.UI.Framework.Commands;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework.Threading;
using System.ComponentModel.Composition;
using System.Threading.Tasks;

namespace MoonLight.Modules.FlowManager.Commands
{
    [CommandHandler]
    public class ViewFlowCommandHandler : CommandHandlerBase<ViewFlowCommandDefinition>
    {
        private readonly IShell _shell;

        [ImportingConstructor]
        public ViewFlowCommandHandler(IShell shell)
        {
            _shell = shell;
        }

        public override Task Run(Command command)
        {
            _shell.ShowTool(IoC.Get<FlowTreeViewModel>());
            return TaskUtility.Completed;
        }
    }
}
