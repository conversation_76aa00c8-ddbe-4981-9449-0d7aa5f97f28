﻿using System;
using System.Globalization;
using System.Linq;
using System.Windows.Data;
using System.Windows;

namespace MoonLight.Core.Assets.Converter
{
    public class EnumArrayToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || parameter == null)
                return Visibility.Collapsed;

            // 获取枚举值  
            var enumType = value.GetType();
            if (!enumType.IsEnum)
                return Visibility.Collapsed;

            // 检查传入的参数是否为逗号分隔的枚举值  
            var validValues = parameter.ToString().Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                             .Select(v => Enum.Parse(enumType, v.Trim()))
                                             .ToArray();

            // 检查当前枚举值是否在有效值列表中  
            if (validValues.Contains(value))
            {
                return Visibility.Visible;
            }

            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
