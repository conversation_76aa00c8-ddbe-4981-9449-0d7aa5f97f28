﻿using MoonLight.Modules.Output.Models;
using MoonLight.UI.Framework.Commands;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework.Threading;
using System.ComponentModel.Composition;
using System.Threading.Tasks;

namespace MoonLight.Modules.Output.Commands
{
    [CommandHandler]
    public class ViewOutputCommandHandler : CommandHandlerBase<ViewOutputCommandDefinition>
    {
        private readonly IShell _shell;

        [ImportingConstructor]
        public ViewOutputCommandHandler(IShell shell)
        {
            _shell = shell;
        }

        public override Task Run(Command command)
        {
            _shell.ShowTool<IOutput>();
            return TaskUtility.Completed;
        }
    }
}
