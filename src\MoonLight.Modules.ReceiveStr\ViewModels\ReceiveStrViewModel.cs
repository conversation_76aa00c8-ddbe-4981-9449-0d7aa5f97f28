﻿using System.ComponentModel;
using System;
using MoonLight.Core.Attributes;
using MoonLight.Core.Models;
using MoonLight.Core.Enums;
using MoonLight.Core.Common.Log;
using System.Collections.Generic;
using MoonLight.Modules.ReceiveStr.Views;
using MoonLight.Core.Interfaces;
using System.Windows.Controls;
using System.Linq;
using MoonLight.Core.Devices;
using MoonLight.Core.Devices.Communication;
using System.Windows.Input;
using MoonLight.UI.Framework;
using System.Runtime.Serialization;
using MoonLight.Core.Modules;

namespace MoonLight.Modules.ReceiveStr.ViewModels
{
    [Category("文件通讯")]
    [DisplayName("接收文本")]
    [ToolImageName("ReceiveStr")]
    [Serializable]
    public class ReceiveStrViewModel : ToolUnit
    {
        [field:NonSerialized]
        public CommunicationBase SelectedComm { get; set; }

        private string RecStr = "";

        // 启用超时
        private bool _IsEnableTimeOut = false;
        public bool IsEnableTimeOut
        {
            get { return _IsEnableTimeOut; }
            set { Set(ref _IsEnableTimeOut, value); }
        }

        // 当前Name
        private string _CurName = "";
        public string CurName
        {
            get { return _CurName; }
            set
            {
                Set(ref _CurName, value, new System.Action(() => 
                {
                    SelectedComm = null;
                    IDeviceManager manager = IoC.Get<IDeviceManager>();// DeviceManager.Instance;
                    if(manager != null)
                    {
                        SelectedComm = (CommunicationBase)manager.GetDeviceByName(CurName);
                        CommRemarks = SelectedComm?.Remarks;
                    }
                }));
            }
        }

        // 备注
        private string _CommRemarks;
        public string CommRemarks
        {
            get { return _CommRemarks; }
            set { Set(ref _CommRemarks, value); }
        }

        private List<string> _ComNames = null;
        public List<string> ComNames
        {
            get
            {
                if(_ComNames == null)
                {
                    _ComNames = new List<string>();
                }
                return _ComNames;
            }
            set { Set(ref _ComNames, value);}
        }

        // 16进制接收
        private bool _ReceiveAsHex = false;
        public bool ReceiveAsHex
        {
            get { return _ReceiveAsHex; }
            set { _ReceiveAsHex = value; }
        }

        [field: NonSerialized]
        public ICommand ConfirmCommand {  get; private set; }
        [field: NonSerialized]
        public ICommand CancelCommand { get; private set; }
        [field: NonSerialized]
        public ICommand ExecuteCommand { get; private set; }

        public ReceiveStrViewModel()
        {
            ConfirmCommand = new RelayCommand(Confirm);
            CancelCommand = new RelayCommand(Cancel);
            ExecuteCommand = new RelayCommand(Execute);
        }

        private void Confirm(object o)
        {
            var view = this.ToolView as ReceiveStrView;
            if (view != null)
            {
                view.Close();
            }
        }

        private void Cancel(object o)
        {
            var view = this.ToolView as ReceiveStrView;
            if (view != null)
            {
                view.Close();
            }
        }

        private void Execute(object o)
        {
            InternalRun();
        }

        public override bool Run()
        {
            Stopwatch.Restart();
            try
            {
                if (SelectedComm == null)
                {
                    ChangeToolRunStatus(RunStatus.NG);
                    return false;
                }

                SelectedComm.IsReceivedByHex = ReceiveAsHex;
                SelectedComm.GetStr(out RecStr);
                ChangeToolRunStatus(RunStatus.OK);
                return true;
            }
            catch (Exception ex)
            {
                ChangeToolRunStatus(RunStatus.NG);
                Logger.AddLog(ex.Message, MsgType.Error);
                return false;
            }
        }

        public override void AddOutputParams()
        {
            base.AddOutputParams();
            AddOutputParam("接收文本", "string", RecStr);
        }

       
        [NonSerialized]
        ReceiveStrPropertyView propertyView = null;
        public override UserControl GetUserControl()
        {
            if (propertyView == null)
            {
                propertyView = new ReceiveStrPropertyView();
                propertyView.DataContext = this;
            }
            return propertyView;
        }

        public override void Loaded(IRenderView renderView, UserControl propertyView = null)
        {
            var str = CurName;
            ComNames = new List<string>();
            IDeviceManager manager = IoC.Get<IDeviceManager>();
            if (manager != null)
            {
                var networkAndSerialDevices = manager.DeviceGroups
                    .Where(g => g.Category == DeviceCategory.NetworkDevice ||
                                g.Category == DeviceCategory.SerialDevice)
                    .SelectMany(g => g.Devices)
                    .Select(d => d.Name);
                ComNames.AddRange(networkAndSerialDevices);
                if(str != null && str != string.Empty)
                {
                    CurName = str;
                }
            }

            var view = ToolView as ReceiveStrView;
            if (view != null && !view.IsClosed)
            {
                ClosedView = true; 
            }
            else
            {
                ClearWindow(renderView);
            }
        }

        [OnDeserialized()] //反序列化之后
        internal void OnDeserializedMethod(StreamingContext context)
        {
            ConfirmCommand = new RelayCommand(Confirm);
            CancelCommand = new RelayCommand(Cancel);
            ExecuteCommand = new RelayCommand(Execute);
        }
    }
}
