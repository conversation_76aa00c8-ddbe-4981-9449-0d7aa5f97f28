using MoonLight.UI.Framework.Commands;

namespace MoonLight.Modules.DeviceManager.Commands
{
    [CommandDefinition]
    public class OpenDeviceManagerCommandDefinition : CommandDefinition
    {
        public const string CommandName = "Tools.DeviceManager";

        public override string Name
        {
            get { return CommandName; }
        }

        public override string Text
        {
            get { return "设备管理器"; }
        }

        public override string ToolTip
        {
            get { return "设备管理器"; }
        }
    }
}
