<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CommunityToolkit.Mvvm</name>
    </assembly>
    <members>
        <member name="T:CommunityToolkit.Mvvm.Collections.Internals.ObservableGroupHelper">
            <summary>
            A helper type for the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> type.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Collections.Internals.ObservableGroupHelper.KeyChangedEventArgs">
            <summary>
            The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> for <see cref="P:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup.Key"/>
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup">
            <summary>
            An interface for a grouped collection of items.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup.Key">
            <summary>
            Gets the key for the current collection.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup.Count">
            <summary>
            Gets the number of items currently in the grouped collection.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup.Item(System.Int32)">
            <summary>
            Gets the element at the specified index in the current collection.
            </summary>
            <param name="index">The zero-based index of the element to get.</param>
            <returns>The element at the specified index in the read-only list.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown if the index is out of range.</exception>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup`2">
            <summary>
            An interface for a grouped collection of items.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of elements in the group.</typeparam>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup`2.Item(System.Int32)">
            <summary>
            Gets the element at the specified index in the current collection.
            </summary>
            <param name="index">The zero-based index of the element to get.</param>
            <returns>The element at the specified index in the read-only list.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown if the index is out of range.</exception>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup`1">
            <summary>
            An interface for a grouped collection of items.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.IReadOnlyObservableGroup`1.Key">
            <summary>
            Gets the key for the current collection.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions">
            <summary>
            The extensions methods to simplify the usage of <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.FirstGroupByKey``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0)">
            <summary>
            Returns the first group with <paramref name="key"/> key.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group to query.</param>
            <returns>The first group matching <paramref name="key"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
            <exception cref="T:System.InvalidOperationException">The target group does not exist.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.FirstGroupByKeyOrDefault``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0)">
            <summary>
            Returns the first group with <paramref name="key"/> key or <see langword="null"/> if not found.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group to query.</param>
            <returns>The first group matching <paramref name="key"/> or <see langword="null"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.AddGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0)">
            <summary>
            Adds a key-value <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group to add.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.AddGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},System.Linq.IGrouping{``0,``1})">
            <summary>
            Adds a key-collection <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="grouping">The group of items to add.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="grouping"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.AddGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,System.Collections.Generic.IEnumerable{``1})">
            <summary>
            Adds a key-collection <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group where <paramref name="collection"/> will be added.</param>
            <param name="collection">The collection to add.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/>, <paramref name="key"/> or <paramref name="collection"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0)">
            <summary>
            Adds a key-value <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group to add.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},System.Linq.IGrouping{``0,``1})">
            <summary>
            Adds a key-value <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="grouping">The group of items to add.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="grouping"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,System.Collections.Generic.IEnumerable{``1})">
            <summary>
            Adds a key-value <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group where <paramref name="collection"/> will be added.</param>
            <param name="collection">The collection to add.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/>, <paramref name="key"/> or <paramref name="collection"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,System.Collections.Generic.IComparer{``0})">
            <summary>
            Adds a key-value <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group to add.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1"/> instance to insert <typeparamref name="TKey"/> at the right position.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/>, <paramref name="key"/> or <paramref name="comparer"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},System.Linq.IGrouping{``0,``1},System.Collections.Generic.IComparer{``0})">
            <summary>
            Adds a key-value <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="grouping">The group of items to add.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1"/> instance to insert <typeparamref name="TKey"/> at the right position.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/>, <paramref name="grouping"/> or <paramref name="comparer"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,System.Collections.Generic.IComparer{``0},System.Collections.Generic.IEnumerable{``1})">
            <summary>
            Adds a key-value <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> item into a target <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group where <paramref name="collection"/> will be added.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1"/> instance to insert <typeparamref name="TKey"/> at the right position.</param>
            <param name="collection">The collection to add.</param>
            <returns>The added <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/>.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/>, <paramref name="key"/>, <paramref name="comparer"/> or <paramref name="collection"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.AddItem``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,``1)">
            <summary>
            Add <paramref name="item"/> into the first group with <paramref name="key"/> key.
            If the group does not exist, it will be added.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group where the <paramref name="item"/> should be added.</param>
            <param name="item">The item to add.</param>
            <returns>The instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> which will receive the value. It will either be an existing group or a new group.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertItem``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,``1)">
            <summary>
            Insert <paramref name="item"/> into the first group with <paramref name="key"/> key.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group where to insert <paramref name="item"/>.</param>
            <param name="item">The item to add.</param>
            <returns>The instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> which will receive the value.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.InsertItem``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,System.Collections.Generic.IComparer{``0},``1,System.Collections.Generic.IComparer{``1})">
            <summary>
            Insert <paramref name="item"/> into the first group with <paramref name="key"/> key.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group where to insert <paramref name="item"/>.</param>
            <param name="keyComparer">The <see cref="T:System.Collections.Generic.IComparer`1"/> instance to compare keys.</param>
            <param name="item">The item to add.</param>
            <param name="itemComparer">The <see cref="T:System.Collections.Generic.IComparer`1"/> instance to compare elements.</param>
            <returns>The instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> which will receive the value.</returns>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/>, <paramref name="key"/>, <paramref name="keyComparer"/> or <paramref name="itemComparer"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.RemoveGroup``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0)">
            <summary>
            Remove the first occurrence of the group with <paramref name="key"/> from the <paramref name="source"/> grouped collection.
            It will not do anything if the group does not exist.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TValue">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group to remove.</param>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollectionExtensions.RemoveItem``2(CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection{``0,``1},``0,``1,System.Boolean)">
            <summary>
            Remove the first <paramref name="item"/> from the first group with <paramref name="key"/> from the <paramref name="source"/> grouped collection.
            It will not do anything if the group or the item does not exist.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TValue">The type of the items in the collection.</typeparam>
            <param name="source">The source <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> instance.</param>
            <param name="key">The key of the group where the <paramref name="item"/> should be removed.</param>
            <param name="item">The item to remove.</param>
            <param name="removeGroupIfEmpty">If true (default value), the group will be removed once it becomes empty.</param>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="source"/> or <paramref name="key"/> are <see langword="null"/>.</exception>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2">
            <summary>
            An observable list of observable groups.
            </summary>
            <typeparam name="TKey">The type of the group keys.</typeparam>
            <typeparam name="TElement">The type of elements in the collection.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> class.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2.#ctor(System.Collections.Generic.IEnumerable{System.Linq.IGrouping{`0,`1}})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2"/> class.
            </summary>
            <param name="collection">The initial data to add in the grouped collection.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="collection"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2.System#Linq#ILookup{TKey,TElement}#Item(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2.TryGetList(System.Collections.Generic.List{CommunityToolkit.Mvvm.Collections.ObservableGroup{`0,`1}}@)">
            <summary>
            Tries to get the underlying <see cref="T:System.Collections.Generic.List`1"/> instance, if present.
            </summary>
            <param name="list">The resulting <see cref="T:System.Collections.Generic.List`1"/>, if one was in use.</param>
            <returns>Whether or not a <see cref="T:System.Collections.Generic.List`1"/> instance has been found.</returns>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2.System#Linq#ILookup{TKey,TElement}#Contains(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroupedCollection`2.System#Collections#Generic#IEnumerable{System#Linq#IGrouping{TKey,TElement}}#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2">
            <summary>
            An observable group.
            It associates a <see cref="P:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.Key"/> to an <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of elements in the group.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.#ctor(`0)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> class.
            </summary>
            <param name="key">The key for the group.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="key"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.#ctor(System.Linq.IGrouping{`0,`1})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> class.
            </summary>
            <param name="grouping">The grouping to fill the group.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="grouping"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.#ctor(`0,System.Collections.Generic.IEnumerable{`1})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> class.
            </summary>
            <param name="key">The key for the group.</param>
            <param name="collection">The initial collection of data to add to the group.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="key"/> or <paramref name="collection"/> are <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.Key">
            <summary>
            Gets or sets the key of the group.
            </summary>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="value"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.TryGetList(System.Collections.Generic.List{`1}@)">
            <summary>
            Tries to get the underlying <see cref="T:System.Collections.Generic.List`1"/> instance, if present.
            </summary>
            <param name="list">The resulting <see cref="T:System.Collections.Generic.List`1"/>, if one was in use.</param>
            <returns>Whether or not a <see cref="T:System.Collections.Generic.List`1"/> instance has been found.</returns>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.CommunityToolkit#Mvvm#Collections#IReadOnlyObservableGroup#Key">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ObservableGroup`2.CommunityToolkit#Mvvm#Collections#IReadOnlyObservableGroup#Item(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2">
            <summary>
            A read-only list of groups.
            </summary>
            <typeparam name="TKey">The type of the group keys.</typeparam>
            <typeparam name="TElement">The type of elements in the collection.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2.#ctor(System.Collections.ObjectModel.ObservableCollection{CommunityToolkit.Mvvm.Collections.ObservableGroup{`0,`1}})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2"/> class.
            </summary>
            <param name="collection">The source collection to wrap.</param>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="collection"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2.#ctor(System.Collections.ObjectModel.ObservableCollection{CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup{`0,`1}})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2"/> class.
            </summary>
            <param name="collection">The source collection to wrap.</param>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown if <paramref name="collection"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2.System#Linq#ILookup{TKey,TElement}#Item(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2.System#Linq#ILookup{TKey,TElement}#Contains(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2.System#Collections#Generic#IEnumerable{System#Linq#IGrouping{TKey,TElement}}#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2.OnSourceCollectionChanged(System.Object,System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <summary>
            Forwards the <see cref="E:System.Collections.Specialized.INotifyCollectionChanged.CollectionChanged"/> event whenever it is raised by the wrapped collection.
            </summary>
            <param name="sender">The wrapped collection (an <see cref="T:System.Collections.ObjectModel.ObservableCollection`1"/> of <see cref="T:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2"/> instance).</param>
            <param name="e">The <see cref="T:System.Collections.Specialized.NotifyCollectionChangedEventArgs"/> arguments.</param>
            <exception cref="T:System.NotSupportedException">Thrown if a range operation is requested.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroupedCollection`2.FirstGroupByKeyOrDefault(`0)">
            <summary>
            Returns the first group with <paramref name="key"/> key or <see langword="null"/> if not found.
            </summary>
            <param name="key">The key of the group to query (assumed not to be <see langword="null"/>).</param>
            <returns>The first group matching <paramref name="key"/>.</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2">
            <summary>
            A read-only observable group. It associates a <see cref="P:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2.Key"/> to a <see cref="T:System.Collections.ObjectModel.ReadOnlyObservableCollection`1"/>.
            </summary>
            <typeparam name="TKey">The type of the group key.</typeparam>
            <typeparam name="TElement">The type of elements in the group.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2.#ctor(`0,System.Collections.ObjectModel.ObservableCollection{`1})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2"/> class.
            </summary>
            <param name="key">The key of the group.</param>
            <param name="collection">The collection of items to add in the group.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="key"/> or <paramref name="collection"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2.#ctor(CommunityToolkit.Mvvm.Collections.ObservableGroup{`0,`1})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2"/> class.
            </summary>
            <param name="group">The <see cref="T:CommunityToolkit.Mvvm.Collections.ObservableGroup`2"/> to wrap.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="group"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2.Key">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2.CommunityToolkit#Mvvm#Collections#IReadOnlyObservableGroup#Key">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Collections.ReadOnlyObservableGroup`2.CommunityToolkit#Mvvm#Collections#IReadOnlyObservableGroup#Item(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.INotifyPropertyChangedAttribute">
            <summary>
            An attribute that indicates that a given type should implement the <see cref="T:System.ComponentModel.INotifyPropertyChanged"/> interface and
            have minimal built-in functionality to support it. This includes exposing the necessary event and having two methods
            to raise it that mirror <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)"/> and
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanged(System.String)"/>. For more extensive support, use <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObjectAttribute"/>.
            <para>
            This attribute can be used as follows:
            <code>
            [INotifyPropertyChanged]
            partial class MyViewModel : SomeOtherClass
            {
                // Other members here...
            }
            </code>
            </para>
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.INotifyPropertyChangedAttribute.IncludeAdditionalHelperMethods">
            <summary>
            Gets or sets a value indicating whether or not to also generate all the additional helper methods that are found
            in <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/> as well (eg. <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>.
            If set to <see langword="false"/>, only the <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/> event and
            the two <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)"/> overloads will be generated.
            The default value is <see langword="true"/>.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.NotifyCanExecuteChangedForAttribute">
             <summary>
             An attribute that can be used to support <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> properties in generated properties. When this attribute is
             used, the generated property setter will also call <see cref="M:CommunityToolkit.Mvvm.Input.IRelayCommand.NotifyCanExecuteChanged"/> for the properties specified
             in the attribute data, causing the validation logic for the command to be executed again. This can be useful to keep the code compact
             when there are one or more dependent commands that should also be notified when a property is updated. If this attribute is used in
             a field without <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/>, it is ignored (just like <see cref="T:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedForAttribute"/>).
             <para>
             In order to use this attribute, the target property has to implement the <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> interface.
             </para>
             <para>
             This attribute can be used as follows:
             <code>
             partial class MyViewModel : ObservableObject
             {
                 [ObservableProperty]
                 [NotifyCanExecuteChangedFor(nameof(GreetUserCommand))]
                 private string name;
            
                 public IRelayCommand GreetUserCommand { get; }
             }
             </code>
             </para>
             And with this, code analogous to this will be generated:
             <code>
             partial class MyViewModel
             {
                 public string Name
                 {
                     get => name;
                     set
                     {
                         if (SetProperty(ref name, value))
                         {
                             GreetUserCommand.NotifyCanExecuteChanged();
                         }
                     }
                 }
             }
             </code>
             </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.NotifyCanExecuteChangedForAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.NotifyCanExecuteChangedForAttribute"/> class.
            </summary>
            <param name="commandName">The name of the command to also notify when the annotated property changes.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.NotifyCanExecuteChangedForAttribute.#ctor(System.String,System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.NotifyCanExecuteChangedForAttribute"/> class.
            </summary>
            <param name="commandName">The name of the property to also notify when the annotated property changes.</param>
            <param name="otherCommandNames">
            The other command names to also notify when the annotated property changes. This parameter can optionally
            be used to indicate a series of dependent commands from the same attribute, to keep the code more compact.
            </param>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.NotifyCanExecuteChangedForAttribute.CommandNames">
            <summary>
            Gets the command names to also notify when the annotated property changes.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.NotifyDataErrorInfoAttribute">
            <summary>
            An attribute that can be used to support <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/> in generated properties, when applied to
            fields contained in a type that is inheriting from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> and using any validation attributes.
            When this attribute is used, the generated property setter will also call <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ValidateProperty(System.Object,System.String)"/>.
            This allows generated properties to opt-in into validation behavior without having to fallback into a full explicit observable property.
            <para>
            This attribute can be used as follows:
            <code>
            partial class MyViewModel : ObservableValidator
            {
                [ObservableProperty]
                [NotifyDataErrorInfo]
                [Required]
                [MinLength(2)]
                private string username;
            }
            </code>
            </para>
            And with this, code analogous to this will be generated:
            <code>
            partial class MyViewModel
            {
                [Required]
                [MinLength(2)]
                public string Username
                {
                    get => username;
                    set => SetProperty(ref username, value, validate: true);
                }
            }
            </code>
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedForAttribute">
             <summary>
             An attribute that can be used to support <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/> in generated properties. When this attribute is
             used, the generated property setter will also call <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanged(System.String)"/> (or the equivalent
             method in the target class) for the properties specified in the attribute data. This can be useful to keep the code compact when
             there are one or more dependent properties that should also be reported as updated when the value of the annotated observable
             property is changed. If this attribute is used in a field without <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/>, it is ignored.
             <para>
             In order to use this attribute, the containing type has to implement the <see cref="T:System.ComponentModel.INotifyPropertyChanged"/> interface
             and expose a method with the same signature as <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanged(System.String)"/>. If the containing
             type also implements the <see cref="T:System.ComponentModel.INotifyPropertyChanging"/> interface and exposes a method with the same signature as
             <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanging(System.String)"/>, then this method will be invoked as well by the property setter.
             </para>
             <para>
             This attribute can be used as follows:
             <code>
             partial class MyViewModel : ObservableObject
             {
                 [ObservableProperty]
                 [NotifyPropertyChangedFor(nameof(FullName))]
                 private string name;
            
                 [ObservableProperty]
                 [NotifyPropertyChangedFor(nameof(FullName))]
                 private string surname;
            
                 public string FullName => $"{Name} {Surname}";
             }
             </code>
             </para>
             And with this, code analogous to this will be generated:
             <code>
             partial class MyViewModel
             {
                 public string Name
                 {
                     get => name;
                     set
                     {
                         if (SetProperty(ref name, value))
                         {
                             OnPropertyChanged(nameof(FullName));
                         }
                     }
                 }
            
                 public string Surname
                 {
                     get => surname;
                     set
                     {
                         if (SetProperty(ref surname, value))
                         {
                             OnPropertyChanged(nameof(FullName));
                         }
                     }
                 }
             }
             </code>
             </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedForAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedForAttribute"/> class.
            </summary>
            <param name="propertyName">The name of the property to also notify when the annotated property changes.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedForAttribute.#ctor(System.String,System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedForAttribute"/> class.
            </summary>
            <param name="propertyName">The name of the property to also notify when the annotated property changes.</param>
            <param name="otherPropertyNames">
            The other property names to also notify when the annotated property changes. This parameter can optionally
            be used to indicate a series of dependent properties from the same attribute, to keep the code more compact.
            </param>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedForAttribute.PropertyNames">
            <summary>
            Gets the property names to also notify when the annotated property changes.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.NotifyPropertyChangedRecipientsAttribute">
            <summary>
            An attribute that can be used to support <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute"/> in generated properties, when applied to fields
            contained in a type that is either inheriting from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/>, or annotated with <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipientAttribute"/>.
            When this attribute is used, the generated property setter will also call <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)"/>.
            This allows generated properties to opt-in into broadcasting behavior without having to fallback into a full explicit observable property.
            <para>
            This attribute can be used as follows:
            <code>
            partial class MyViewModel : ObservableRecipient
            {
                [ObservableProperty]
                [NotifyPropertyChangedRecipients]
                private string username;
            }
            </code>
            </para>
            <para>
            And with this, code analogous to this will be generated:
            <code>
            partial class MyViewModel
            {
                public string Username
                {
                    get => username;
                    set => SetProperty(ref username, value, broadcast: true);
                }
            }
            </code>
            </para>
            <para>
            This attribute can also be added to a class, and if so it will affect all generated properties in that type and inherited types.
            </para>
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObjectAttribute">
            <summary>
            An attribute that indicates that a given type should have all the members from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/>
            generated into it, as well as the <see cref="T:System.ComponentModel.INotifyPropertyChanged"/> and <see cref="T:System.ComponentModel.INotifyPropertyChanging"/>
            interfaces. This can be useful when you want the same functionality from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/> into a class
            that already inherits from another one (since C# doesn't support multiple inheritance). This attribute will trigger
            the source generator to just create the same APIs directly into the decorated class.
            <para>
            This attribute can be used as follows:
            <code>
            [ObservableObject]
            partial class MyViewModel : SomeOtherClass
            {
                // Other members here...
            }
            </code>
            </para>
            And with this, the same APIs from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/> will be available on this type as well.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservablePropertyAttribute">
             <summary>
             An attribute that indicates that a given field should be wrapped by a generated observable property.
             In order to use this attribute, the containing type has to inherit from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/>, or it
             must be using <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObjectAttribute"/> or <see cref="T:CommunityToolkit.Mvvm.ComponentModel.INotifyPropertyChangedAttribute"/>.
             If the containing type also implements the <see cref="T:System.ComponentModel.INotifyPropertyChanging"/> (that is, if it either inherits from
             <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/> or is using <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObjectAttribute"/>), then the generated code will
             also invoke <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanging(System.ComponentModel.PropertyChangingEventArgs)"/> to signal that event.
             <para>
             This attribute can be used as follows:
             <code>
             partial class MyViewModel : ObservableObject
             {
                 [ObservableProperty]
                 private string name;
            
                 [ObservableProperty]
                 private bool isEnabled;
             }
             </code>
             </para>
             And with this, code analogous to this will be generated:
             <code>
             partial class MyViewModel
             {
                 public string Name
                 {
                     get => name;
                     set => SetProperty(ref name, value);
                 }
            
                 public bool IsEnabled
                 {
                     get => isEnabled;
                     set => SetProperty(ref isEnabled, value);
                 }
             }
             </code>
             </summary>
             <remarks>
             The generated properties will automatically use the <c>UpperCamelCase</c> format for their names,
             which will be derived from the field names. The generator can also recognize fields using either
             the <c>_lowerCamel</c> or <c>m_lowerCamel</c> naming scheme. Otherwise, the first character in the
             source field name will be converted to uppercase (eg. <c>isEnabled</c> to <c>IsEnabled</c>).
             </remarks>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipientAttribute">
            <summary>
            An attribute that indicates that a given type should have all the members from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/>
            generated into it. This can be useful when you want the same functionality from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/> into
            a class that already inherits from another one (since C# doesn't support multiple inheritance). This attribute will trigger
            the source generator to just create the same APIs directly into the decorated class. For instance, this attribute can be
            used to easily combine the functionality from both <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> and <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/>,
            by using <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> as the base class and adding this attribute to the declared type.
            <para>
            This attribute can be used as follows:
            <code>
            [ObservableRecipient]
            partial class MyViewModel : ObservableValidator
            {
                // Other members here...
            }
            </code>
            </para>
            And with this, the same APIs from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/> will be available on this type as well.
            <para>
            To avoid conflicts with other APIs in types where the new members are being generated, constructors are only generated when the annotated
            type doesn't have any explicit constructors being declared. If that is the case, the same constructors from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/>
            are emitted, with the accessibility adapted to that of the annotated type. Otherwise, they are skipped, so the type being annotated has the
            responsibility of properly initializing the <see cref="P:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Messenger"/> property. Additionally, if the annotated type inherits
            from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/>, the <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``1(``0@,``0,System.Boolean,System.String)"/> overloads will be skipped
            as well, as they would conflict with the <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``1(``0@,``0,System.Boolean,System.String)"/> methods.
            </para>
            </summary>
            <remarks>
            In order to work, <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipientAttribute"/> needs to be applied to a type that inherits from
            <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/> (either directly or indirectly), or to one decorated with <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObjectAttribute"/>.
            This is because the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/> methods rely on some of the inherited members to work.
            If this condition is not met, the code will fail to build.
            </remarks>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject">
            <summary>
            A base class for objects of which the properties must be observable.
            </summary>
        </member>
        <member name="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged">
            <inheritdoc cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/>
        </member>
        <member name="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging">
            <inheritdoc cref="E:System.ComponentModel.INotifyPropertyChanging.PropertyChanging"/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs)">
            <summary>
            Raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            </summary>
            <param name="e">The input <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> instance.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="e"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanging(System.ComponentModel.PropertyChangingEventArgs)">
            <summary>
            Raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event.
            </summary>
            <param name="e">The input <see cref="T:System.ComponentModel.PropertyChangingEventArgs"/> instance.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="e"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanged(System.String)">
            <summary>
            Raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            </summary>
            <param name="propertyName">(optional) The name of the property that changed.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.OnPropertyChanging(System.String)">
            <summary>
            Raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event.
            </summary>
            <param name="propertyName">(optional) The name of the property that changed.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with the new
            value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            The <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events are not raised
            if the current and new value for the target property are the same.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.Collections.Generic.IEqualityComparer{``0},System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with the new
            value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            See additional notes about this overload in <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Action{``0},System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with the new
            value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            This overload is much less efficient than <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/> and it
            should only be used when the former is not viable (eg. when the target property being
            updated does not directly expose a backing field that can be passed by reference).
            For performance reasons, it is recommended to use a stateful callback if possible through
            the <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)"/> whenever possible
            instead of this overload, as that will allow the C# compiler to cache the input callback and
            reduce the memory allocations. More info on that overload are available in the related XML
            docs. This overload is here for completeness and in cases where that is not applicable.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            The <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events are not raised
            if the current and new value for the target property are the same.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="callback"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Collections.Generic.IEqualityComparer{``0},System.Action{``0},System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with the new
            value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            See additional notes about this overload in <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Action{``0},System.String)"/>.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/> or <paramref name="callback"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)">
             <summary>
             Compares the current and new values for a given nested property. If the value has changed,
             raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property and then raises the
             <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. The behavior mirrors that of <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>,
             with the difference being that this method is used to relay properties from a wrapped model in the
             current instance. This type is useful when creating wrapping, bindable objects that operate over
             models that lack support for notification (eg. for CRUD operations).
             Suppose we have this model (eg. for a database row in a table):
             <code>
             public class Person
             {
                 public string Name { get; set; }
             }
             </code>
             We can then use a property to wrap instances of this type into our observable model (which supports
             notifications), injecting the notification to the properties of that model, like so:
             <code>
             public class BindablePerson : ObservableObject
             {
                 public Model { get; }
            
                 public BindablePerson(Person model)
                 {
                     Model = model;
                 }
            
                 public string Name
                 {
                     get => Model.Name;
                     set => Set(Model.Name, value, Model, (model, name) => model.Name = name);
                 }
             }
             </code>
             This way we can then use the wrapping object in our application, and all those "proxy" properties will
             also raise notifications when changed. Note that this method is not meant to be a replacement for
             <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>, and it should only be used when relaying properties to a model that
             doesn't support notifications, and only if you can't implement notifications to that model directly (eg. by having
             it inherit from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/>). The syntax relies on passing the target model and a stateless callback
             to allow the C# compiler to cache the function, which results in much better performance and no memory usage.
             </summary>
             <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
             <typeparam name="T">The type of property (or field) to set.</typeparam>
             <param name="oldValue">The current property value.</param>
             <param name="newValue">The property's value after the change occurred.</param>
             <param name="model">The model containing the property being updated.</param>
             <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
             <param name="propertyName">(optional) The name of the property that changed.</param>
             <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
             <remarks>
             The <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events are not
             raised if the current and new value for the target property are the same.
             </remarks>
             <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="model"/> or <paramref name="callback"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.String)">
            <summary>
            Compares the current and new values for a given nested property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property and then raises the
            <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. The behavior mirrors that of <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>,
            with the difference being that this method is used to relay properties from a wrapped model in the
            current instance. See additional notes about this overload in <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)"/>.
            </summary>
            <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
            <typeparam name="T">The type of property (or field) to set.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="model">The model containing the property being updated.</param>
            <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/>, <paramref name="model"/> or <paramref name="callback"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetPropertyAndNotifyOnCompletion(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier@,System.Threading.Tasks.Task,System.String)">
             <summary>
             Compares the current and new values for a given field (which should be the backing
             field for a property). If the value has changed, raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/>
             event, updates the field and then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
             The behavior mirrors that of <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>, with the difference being that
             this method will also monitor the new value of the property (a generic <see cref="T:System.Threading.Tasks.Task"/>) and will also
             raise the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> again for the target property when it completes.
             This can be used to update bindings observing that <see cref="T:System.Threading.Tasks.Task"/> or any of its properties.
             This method and its overload specifically rely on the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier"/> type, which needs
             to be used in the backing field for the target <see cref="T:System.Threading.Tasks.Task"/> property. The field doesn't need to be
             initialized, as this method will take care of doing that automatically. The <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier"/>
             type also includes an implicit operator, so it can be assigned to any <see cref="T:System.Threading.Tasks.Task"/> instance directly.
             Here is a sample property declaration using this method:
             <code>
             private TaskNotifier myTask;
            
             public Task MyTask
             {
                 get => myTask;
                 private set => SetAndNotifyOnCompletion(ref myTask, value);
             }
             </code>
             </summary>
             <param name="taskNotifier">The field notifier to modify.</param>
             <param name="newValue">The property's value after the change occurred.</param>
             <param name="propertyName">(optional) The name of the property that changed.</param>
             <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
             <remarks>
             The <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events are not raised if the current
             and new value for the target property are the same. The return value being <see langword="true"/> only
             indicates that the new value being assigned to <paramref name="taskNotifier"/> is different than the previous one,
             and it does not mean the new <see cref="T:System.Threading.Tasks.Task"/> instance passed as argument is in any particular state.
             </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetPropertyAndNotifyOnCompletion(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier@,System.Threading.Tasks.Task,System.Action{System.Threading.Tasks.Task},System.String)">
            <summary>
            Compares the current and new values for a given field (which should be the backing
            field for a property). If the value has changed, raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/>
            event, updates the field and then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            This method is just like <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetPropertyAndNotifyOnCompletion(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier@,System.Threading.Tasks.Task,System.String)"/>,
            with the difference being an extra <see cref="T:System.Action`1"/> parameter with a callback being invoked
            either immediately, if the new task has already completed or is <see langword="null"/>, or upon completion.
            </summary>
            <param name="taskNotifier">The field notifier to modify.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            The <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events are not raised
            if the current and new value for the target property are the same.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="callback"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetPropertyAndNotifyOnCompletion``1(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier{``0}@,System.Threading.Tasks.Task{``0},System.String)">
             <summary>
             Compares the current and new values for a given field (which should be the backing
             field for a property). If the value has changed, raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/>
             event, updates the field and then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
             The behavior mirrors that of <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>, with the difference being that
             this method will also monitor the new value of the property (a generic <see cref="T:System.Threading.Tasks.Task"/>) and will also
             raise the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> again for the target property when it completes.
             This can be used to update bindings observing that <see cref="T:System.Threading.Tasks.Task"/> or any of its properties.
             This method and its overload specifically rely on the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1"/> type, which needs
             to be used in the backing field for the target <see cref="T:System.Threading.Tasks.Task"/> property. The field doesn't need to be
             initialized, as this method will take care of doing that automatically. The <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1"/>
             type also includes an implicit operator, so it can be assigned to any <see cref="T:System.Threading.Tasks.Task"/> instance directly.
             Here is a sample property declaration using this method:
             <code>
             private TaskNotifier&lt;int&gt; myTask;
            
             public Task&lt;int&gt; MyTask
             {
                 get => myTask;
                 private set => SetAndNotifyOnCompletion(ref myTask, value);
             }
             </code>
             </summary>
             <typeparam name="T">The type of result for the <see cref="T:System.Threading.Tasks.Task`1"/> to set and monitor.</typeparam>
             <param name="taskNotifier">The field notifier to modify.</param>
             <param name="newValue">The property's value after the change occurred.</param>
             <param name="propertyName">(optional) The name of the property that changed.</param>
             <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
             <remarks>
             The <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events are not raised if the current
             and new value for the target property are the same. The return value being <see langword="true"/> only
             indicates that the new value being assigned to <paramref name="taskNotifier"/> is different than the previous one,
             and it does not mean the new <see cref="T:System.Threading.Tasks.Task`1"/> instance passed as argument is in any particular state.
             </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetPropertyAndNotifyOnCompletion``1(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier{``0}@,System.Threading.Tasks.Task{``0},System.Action{System.Threading.Tasks.Task{``0}},System.String)">
            <summary>
            Compares the current and new values for a given field (which should be the backing
            field for a property). If the value has changed, raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/>
            event, updates the field and then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            This method is just like <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetPropertyAndNotifyOnCompletion``1(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier{``0}@,System.Threading.Tasks.Task{``0},System.String)"/>,
            with the difference being an extra <see cref="T:System.Action`1"/> parameter with a callback being invoked
            either immediately, if the new task has already completed or is <see langword="null"/>, or upon completion.
            </summary>
            <typeparam name="T">The type of result for the <see cref="T:System.Threading.Tasks.Task`1"/> to set and monitor.</typeparam>
            <param name="taskNotifier">The field notifier to modify.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            The <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events are not raised
            if the current and new value for the target property are the same.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="callback"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetPropertyAndNotifyOnCompletion``1(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.ITaskNotifier{``0},``0,System.Action{``0},System.String)">
            <summary>
            Implements the notification logic for the related methods.
            </summary>
            <typeparam name="TTask">The type of <see cref="T:System.Threading.Tasks.Task"/> to set and monitor.</typeparam>
            <param name="taskNotifier">The field notifier.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="callback">(optional) A callback to invoke to update the property value.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.ITaskNotifier`1">
            <summary>
            An interface for task notifiers of a specified type.
            </summary>
            <typeparam name="TTask">The type of value to store.</typeparam>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.ITaskNotifier`1.Task">
            <summary>
            Gets or sets the wrapped <typeparamref name="TTask"/> value.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier">
            <summary>
            A wrapping class that can hold a <see cref="T:System.Threading.Tasks.Task"/> value.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier"/> class.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier.CommunityToolkit#Mvvm#ComponentModel#ObservableObject#ITaskNotifier{System#Threading#Tasks#Task}#Task">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier.op_Implicit(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier)~System.Threading.Tasks.Task">
            <summary>
            Unwraps the <see cref="T:System.Threading.Tasks.Task"/> value stored in the current instance.
            </summary>
            <param name="notifier">The input <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1"/> instance.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1">
            <summary>
            A wrapping class that can hold a <see cref="T:System.Threading.Tasks.Task`1"/> value.
            </summary>
            <typeparam name="T">The type of value for the wrapped <see cref="T:System.Threading.Tasks.Task`1"/> instance.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1"/> class.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1.CommunityToolkit#Mvvm#ComponentModel#ObservableObject#ITaskNotifier{System#Threading#Tasks#Task{T}}#Task">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1.op_Implicit(CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier{`0})~System.Threading.Tasks.Task{`0}">
            <summary>
            Unwraps the <see cref="T:System.Threading.Tasks.Task`1"/> value stored in the current instance.
            </summary>
            <param name="notifier">The input <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.TaskNotifier`1"/> instance.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient">
            <summary>
            A base class for observable objects that also acts as recipients for messages. This class is an extension of
            <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/> which also provides built-in support to use the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> type.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/> class.
            </summary>
            <remarks>
            This constructor will produce an instance that will use the <see cref="P:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Default"/> instance
            to perform requested operations. It will also be available locally through the <see cref="P:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Messenger"/> property.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.#ctor(CommunityToolkit.Mvvm.Messaging.IMessenger)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient"/> class.
            </summary>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to send messages.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Messenger">
            <summary>
            Gets the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance in use.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.IsActive">
            <summary>
            Gets or sets a value indicating whether the current view model is currently active.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.OnActivated">
            <summary>
            Invoked whenever the <see cref="P:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.IsActive"/> property is set to <see langword="true"/>.
            Use this method to register to messages and do other initialization for this instance.
            </summary>
            <remarks>
            The base implementation registers all messages for this recipients that have been declared
            explicitly through the <see cref="T:CommunityToolkit.Mvvm.Messaging.IRecipient`1"/> interface, using the default channel.
            For more details on how this works, see the <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.RegisterAll(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object)"/> method.
            If you need more fine tuned control, want to register messages individually or just prefer
            the lambda-style syntax for message registration, override this method and register manually.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.OnDeactivated">
            <summary>
            Invoked whenever the <see cref="P:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.IsActive"/> property is set to <see langword="false"/>.
            Use this method to unregister from messages and do general cleanup for this instance.
            </summary>
            <remarks>
            The base implementation unregisters all messages for this recipient. It does so by
            invoking <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessenger.UnregisterAll(System.Object)"/>, which removes all registered
            handlers for a given subscriber, regardless of what token was used to register them.
            That is, all registered handlers across all subscription channels will be removed.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)">
            <summary>
            Broadcasts a <see cref="T:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1"/> with the specified
            parameters, without using any particular token (so using the default channel).
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The value of the property before it changed.</param>
            <param name="newValue">The value of the property after it changed.</param>
            <param name="propertyName">The name of the property that changed.</param>
            <remarks>
            You should override this method if you wish to customize the channel being
            used to send the message (eg. if you need to use a specific token for the channel).
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``1(``0@,``0,System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="broadcast">If <see langword="true"/>, <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)"/> will also be invoked.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            This method is just like <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>, just with the addition
            of the <paramref name="broadcast"/> parameter. As such, following the behavior of the base method,
            the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events
            are not raised if the current and new value for the target property are the same.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``1(``0@,``0,System.Collections.Generic.IEqualityComparer{``0},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            See additional notes about this overload in <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``1(``0@,``0,System.Boolean,System.String)"/>.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="broadcast">If <see langword="true"/>, <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)"/> will also be invoked.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``1(``0,``0,System.Action{``0},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. Similarly to
            the <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Action{``0},System.String)"/> method, this overload should only be
            used when <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/> can't be used directly.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="broadcast">If <see langword="true"/>, <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)"/> will also be invoked.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            This method is just like <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Action{``0},System.String)"/>, just with the addition
            of the <paramref name="broadcast"/> parameter. As such, following the behavior of the base method,
            the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events
            are not raised if the current and new value for the target property are the same.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="callback"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``1(``0,``0,System.Collections.Generic.IEqualityComparer{``0},System.Action{``0},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            See additional notes about this overload in <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``1(``0,``0,System.Action{``0},System.Boolean,System.String)"/>.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="broadcast">If <see langword="true"/>, <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)"/> will also be invoked.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/> or <paramref name="callback"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given nested property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property and then raises the
            <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. The behavior mirrors that of
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)"/>, with the difference being that this
            method is used to relay properties from a wrapped model in the current instance. For more info, see the docs for
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)"/>.
            </summary>
            <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
            <typeparam name="T">The type of property (or field) to set.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="model">The model </param>
            <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
            <param name="broadcast">If <see langword="true"/>, <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)"/> will also be invoked.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="model"/> or <paramref name="callback"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given nested property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property and then raises the
            <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. The behavior mirrors that of
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.String)"/>,
            with the difference being that this method is used to relay properties from a wrapped model in the
            current instance. For more info, see the docs for
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.String)"/>.
            </summary>
            <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
            <typeparam name="T">The type of property (or field) to set.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="model">The model </param>
            <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
            <param name="broadcast">If <see langword="true"/>, <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableRecipient.Broadcast``1(``0,``0,System.String)"/> will also be invoked.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/>, <paramref name="model"/> or <paramref name="callback"/> are <see langword="null"/>.</exception>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator">
            <summary>
            A base class for objects implementing the <see cref="T:System.ComponentModel.INotifyDataErrorInfo"/> interface. This class
            also inherits from <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/>, so it can be used for observable items too.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.EntityValidatorMap">
            <summary>
            The <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable`2"/> instance used to track compiled delegates to validate entities.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.DisplayNamesMap">
            <summary>
            The <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable`2"/> instance used to track display names for properties to validate.
            </summary>
            <remarks>
            This is necessary because we want to reuse the same <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext"/> instance for all validations, but
            with the same behavior with respect to formatted names that new instances would have provided. The issue is that the
            <see cref="P:System.ComponentModel.DataAnnotations.ValidationContext.DisplayName"/> property is not refreshed when we set <see cref="P:System.ComponentModel.DataAnnotations.ValidationContext.MemberName"/>,
            so we need to replicate the same logic to retrieve the right display name for properties to validate and update that
            property manually right before passing the context to <see cref="T:System.ComponentModel.DataAnnotations.Validator"/> and proceed with the normal functionality.
            </remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.HasErrorsChangedEventArgs">
            <summary>
            The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> for <see cref="P:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.HasErrors"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.validationContext">
            <summary>
            The <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext"/> instance currently in use.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.errors">
            <summary>
            The <see cref="T:System.Collections.Generic.Dictionary`2"/> instance used to store previous validation results.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.totalErrors">
            <summary>
            Indicates the total number of properties with errors (not total errors).
            This is used to allow <see cref="P:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.HasErrors"/> to operate in O(1) time, as it can just
            check whether this value is not 0 instead of having to traverse <see cref="F:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.errors"/>.
            </summary>
        </member>
        <member name="E:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ErrorsChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> class.
            This constructor will create a new <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext"/> that will
            be used to validate all properties, which will reference the current instance
            and no additional services or validation properties and settings.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.#ctor(System.Collections.Generic.IDictionary{System.Object,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> class.
            This constructor will create a new <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext"/> that will
            be used to validate all properties, which will reference the current instance.
            </summary>
            <param name="items">A set of key/value pairs to make available to consumers.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.#ctor(System.IServiceProvider,System.Collections.Generic.IDictionary{System.Object,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> class.
            This constructor will create a new <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext"/> that will
            be used to validate all properties, which will reference the current instance.
            </summary>
            <param name="serviceProvider">An <see cref="T:System.IServiceProvider"/> instance to make available during validation.</param>
            <param name="items">A set of key/value pairs to make available to consumers.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.#ctor(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> class.
            This constructor will store the input <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext"/> instance,
            and it will use it to validate all properties for the current viewmodel.
            </summary>
            <param name="validationContext">
            The <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext"/> instance to use to validate properties.
            <para>
            This instance will be passed to all <see cref="M:System.ComponentModel.DataAnnotations.Validator.TryValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult})"/>
            calls executed by the current viewmodel, and its <see cref="P:System.ComponentModel.DataAnnotations.ValidationContext.MemberName"/> property will be updated every time
            before the call is made to set the name of the property being validated. The property name will not be reset after that, so the
            value of <see cref="P:System.ComponentModel.DataAnnotations.ValidationContext.MemberName"/> will always indicate the name of the last property that was validated, if any.
            </para>
            </param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="validationContext"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.HasErrors">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``1(``0@,``0,System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="validate">If <see langword="true"/>, <paramref name="newValue"/> will also be validated.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            This method is just like <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/>, just with the addition
            of the <paramref name="validate"/> parameter. If that is set to <see langword="true"/>, the new value will be
            validated and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ErrorsChanged"/> will be raised if needed. Following the behavior of the base method,
            the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events
            are not raised if the current and new value for the target property are the same.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="propertyName"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``1(``0@,``0,System.Collections.Generic.IEqualityComparer{``0},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            See additional notes about this overload in <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``1(``0@,``0,System.Boolean,System.String)"/>.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="validate">If <see langword="true"/>, <paramref name="newValue"/> will also be validated.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``1(``0,``0,System.Action{``0},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. Similarly to
            the <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Action{``0},System.String)"/> method, this overload should only be
            used when <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/> can't be used directly.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="validate">If <see langword="true"/>, <paramref name="newValue"/> will also be validated.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <remarks>
            This method is just like <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Action{``0},System.String)"/>, just with the addition
            of the <paramref name="validate"/> parameter. As such, following the behavior of the base method,
            the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> and <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> events
            are not raised if the current and new value for the target property are the same.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``1(``0,``0,System.Collections.Generic.IEqualityComparer{``0},System.Action{``0},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property with
            the new value, then raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event.
            See additional notes about this overload in <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``1(``0,``0,System.Action{``0},System.Boolean,System.String)"/>.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="validate">If <see langword="true"/>, <paramref name="newValue"/> will also be validated.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/>, <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given nested property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property and then raises the
            <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. The behavior mirrors that of
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)"/>, with the difference being that this
            method is used to relay properties from a wrapped model in the current instance. For more info, see the docs for
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)"/>.
            </summary>
            <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
            <typeparam name="T">The type of property (or field) to set.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="model">The model </param>
            <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
            <param name="validate">If <see langword="true"/>, <paramref name="newValue"/> will also be validated.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="model"/>, <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.Boolean,System.String)">
            <summary>
            Compares the current and new values for a given nested property. If the value has changed,
            raises the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanging"/> event, updates the property and then raises the
            <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.PropertyChanged"/> event. The behavior mirrors that of
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.String)"/>,
            with the difference being that this method is used to relay properties from a wrapped model in the
            current instance. For more info, see the docs for
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.String)"/>.
            </summary>
            <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
            <typeparam name="T">The type of property (or field) to set.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="model">The model </param>
            <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
            <param name="validate">If <see langword="true"/>, <paramref name="newValue"/> will also be validated.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns><see langword="true"/> if the property was changed, <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/>, <paramref name="model"/>, <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.TrySetProperty``1(``0@,``0,System.Collections.Generic.IReadOnlyCollection{System.ComponentModel.DataAnnotations.ValidationResult}@,System.String)">
            <summary>
            Tries to validate a new value for a specified property. If the validation is successful,
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.String)"/> is called, otherwise no state change is performed.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="errors">The resulting validation errors, if any.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns>Whether the validation was successful and the property value changed as well.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="propertyName"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.TrySetProperty``1(``0@,``0,System.Collections.Generic.IEqualityComparer{``0},System.Collections.Generic.IReadOnlyCollection{System.ComponentModel.DataAnnotations.ValidationResult}@,System.String)">
            <summary>
            Tries to validate a new value for a specified property. If the validation is successful,
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0@,``0,System.Collections.Generic.IEqualityComparer{``0},System.String)"/> is called, otherwise no state change is performed.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="field">The field storing the property's value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="errors">The resulting validation errors, if any.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns>Whether the validation was successful and the property value changed as well.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.TrySetProperty``1(``0,``0,System.Action{``0},System.Collections.Generic.IReadOnlyCollection{System.ComponentModel.DataAnnotations.ValidationResult}@,System.String)">
            <summary>
            Tries to validate a new value for a specified property. If the validation is successful,
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Action{``0},System.String)"/> is called, otherwise no state change is performed.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="errors">The resulting validation errors, if any.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns>Whether the validation was successful and the property value changed as well.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.TrySetProperty``1(``0,``0,System.Collections.Generic.IEqualityComparer{``0},System.Action{``0},System.Collections.Generic.IReadOnlyCollection{System.ComponentModel.DataAnnotations.ValidationResult}@,System.String)">
            <summary>
            Tries to validate a new value for a specified property. If the validation is successful,
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``1(``0,``0,System.Collections.Generic.IEqualityComparer{``0},System.Action{``0},System.String)"/> is called, otherwise no state change is performed.
            </summary>
            <typeparam name="T">The type of the property that changed.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="callback">A callback to invoke to update the property value.</param>
            <param name="errors">The resulting validation errors, if any.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns>Whether the validation was successful and the property value changed as well.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/>, <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.TrySetProperty``2(``1,``1,``0,System.Action{``0,``1},System.Collections.Generic.IReadOnlyCollection{System.ComponentModel.DataAnnotations.ValidationResult}@,System.String)">
            <summary>
            Tries to validate a new value for a specified property. If the validation is successful,
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,``0,System.Action{``0,``1},System.String)"/> is called, otherwise no state change is performed.
            </summary>
            <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
            <typeparam name="T">The type of property (or field) to set.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="model">The model </param>
            <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
            <param name="errors">The resulting validation errors, if any.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns>Whether the validation was successful and the property value changed as well.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="model"/>, <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.TrySetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.Collections.Generic.IReadOnlyCollection{System.ComponentModel.DataAnnotations.ValidationResult}@,System.String)">
            <summary>
            Tries to validate a new value for a specified property. If the validation is successful,
            <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableObject.SetProperty``2(``1,``1,System.Collections.Generic.IEqualityComparer{``1},``0,System.Action{``0,``1},System.String)"/> is called, otherwise no state change is performed.
            </summary>
            <typeparam name="TModel">The type of model whose property (or field) to set.</typeparam>
            <typeparam name="T">The type of property (or field) to set.</typeparam>
            <param name="oldValue">The current property value.</param>
            <param name="newValue">The property's value after the change occurred.</param>
            <param name="comparer">The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> instance to use to compare the input values.</param>
            <param name="model">The model </param>
            <param name="callback">The callback to invoke to set the target property value, if a change has occurred.</param>
            <param name="errors">The resulting validation errors, if any.</param>
            <param name="propertyName">(optional) The name of the property that changed.</param>
            <returns>Whether the validation was successful and the property value changed as well.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="comparer"/>, <paramref name="model"/>, <paramref name="callback"/> or <paramref name="propertyName"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ClearErrors(System.String)">
            <summary>
            Clears the validation errors for a specified property or for the entire entity.
            </summary>
            <param name="propertyName">
            The name of the property to clear validation errors for.
            If a <see langword="null"/> or empty name is used, all entity-level errors will be cleared.
            </param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.GetErrors(System.String)">
            <inheritdoc cref="M:System.ComponentModel.INotifyDataErrorInfo.GetErrors(System.String)"/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.System#ComponentModel#INotifyDataErrorInfo#GetErrors(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ValidateAllProperties">
            <summary>
            Validates all the properties in the current instance and updates all the tracked errors.
            If any changes are detected, the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ErrorsChanged"/> event will be raised.
            </summary>
            <remarks>
            Only public instance properties (excluding custom indexers) that have at least one
            <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute"/> applied to them will be validated. All other
            members in the current instance will be ignored. None of the processed properties
            will be modified - they will only be used to retrieve their values and validate them.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ValidateProperty(System.Object,System.String)">
            <summary>
            Validates a property with a specified name and a given input value.
            If any changes are detected, the <see cref="E:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ErrorsChanged"/> event will be raised.
            </summary>
            <param name="value">The value to test for the specified property.</param>
            <param name="propertyName">The name of the property to validate.</param>
            <exception cref="T:CommunityToolkit.Mvvm.ArgumentNullException">Thrown when <paramref name="propertyName"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.TryValidateProperty(System.Object,System.String,System.Collections.Generic.IReadOnlyCollection{System.ComponentModel.DataAnnotations.ValidationResult}@)">
            <summary>
            Tries to validate a property with a specified name and a given input value, and returns
            the computed errors, if any. If the property is valid, it is assumed that its value is
            about to be set in the current object. Otherwise, no observable local state is modified.
            </summary>
            <param name="value">The value to test for the specified property.</param>
            <param name="propertyName">The name of the property to validate.</param>
            <param name="errors">The resulting validation errors, if any.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ClearAllErrors">
            <summary>
            Clears all the current errors for the entire entity.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ClearErrorsForProperty(System.String)">
            <summary>
            Clears all the current errors for a target property.
            </summary>
            <param name="propertyName">The name of the property to clear errors for.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.GetDisplayNameForProperty(System.String)">
            <summary>
            Gets the display name for a given property. It could be a custom name or just the property name.
            </summary>
            <param name="propertyName">The target property name being validated.</param>
            <returns>The display name for the property.</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__ObservableValidatorHelper">
            <summary>
            An internal helper to support the source generator APIs related to <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/>.
            This type is not intended to be used directly by user code.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__ObservableValidatorHelper.ValidateProperty(CommunityToolkit.Mvvm.ComponentModel.ObservableValidator,System.Object,System.String)">
            <summary>
            Invokes <see cref="M:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator.ValidateProperty(System.Object,System.String)"/> externally on a target instance.
            </summary>
            <param name="instance">The target <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableValidator"/> instance.</param>
            <param name="value">The value to test for the specified property.</param>
            <param name="propertyName">The name of the property to validate.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions">
            <summary>
            An internal helper used to support <see cref="T:CommunityToolkit.Mvvm.ComponentModel.ObservableObject"/> and generated code from its template.
            This type is not intended to be used directly by user code.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.GetAwaitableWithoutEndValidation(System.Threading.Tasks.Task)">
            <summary>
            Gets an awaitable object that skips end validation.
            </summary>
            <param name="task">The input <see cref="T:System.Threading.Tasks.Task"/> to get the awaitable for.</param>
            <returns>A <see cref="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation"/> object wrapping <paramref name="task"/>.</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation">
            <summary>
            A custom task awaitable object that skips end validation.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.task">
            <summary>
            The wrapped <see cref="T:System.Threading.Tasks.Task"/> instance to create an awaiter for.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.#ctor(System.Threading.Tasks.Task)">
            <summary>
            Creates a new <see cref="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation"/> instance with the specified parameters.
            </summary>
            <param name="task">The wrapped <see cref="T:System.Threading.Tasks.Task"/> instance to create an awaiter for.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.GetAwaiter">
            <summary>
            Gets an <see cref="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter"/> instance for the current underlying task.
            </summary>
            <returns>An <see cref="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter"/> instance for the current underlying task.</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter">
            <summary>
            An awaiter object for <see cref="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter.taskAwaiter">
            <summary>
            The underlying <see cref="T:System.Runtime.CompilerServices.TaskAwaiter"/> instance.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter.#ctor(System.Threading.Tasks.Task)">
            <summary>
            Creates a new <see cref="T:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter"/> instance with the specified parameters.
            </summary>
            <param name="task">The wrapped <see cref="T:System.Threading.Tasks.Task"/> instance to create an awaiter for.</param>
        </member>
        <member name="P:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter.IsCompleted">
            <summary>
            Gets whether the operation has completed or not.
            </summary>
            <remarks>This property is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter.GetResult">
            <summary>
            Ends the await operation.
            </summary>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter.OnCompleted(System.Action)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ComponentModel.__Internals.__TaskExtensions.TaskAwaitableWithoutEndValidation.Awaiter.UnsafeOnCompleted(System.Action)">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.DependencyInjection.Ioc">
            <summary>
            A type that facilitates the use of the <see cref="T:System.IServiceProvider"/> type.
            The <see cref="T:CommunityToolkit.Mvvm.DependencyInjection.Ioc"/> provides the ability to configure services in a singleton, thread-safe
            service provider instance, which can then be used to resolve service instances.
            The first step to use this feature is to declare some services, for instance:
            <code>
            public interface ILogger
            {
                void Log(string text);
            }
            </code>
            <code>
            public class ConsoleLogger : ILogger
            {
                void Log(string text) => Console.WriteLine(text);
            }
            </code>
            Then the services configuration should then be done at startup, by calling the <see cref="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.ConfigureServices(System.IServiceProvider)"/>
            method and passing an <see cref="T:System.IServiceProvider"/> instance with the services to use. That instance can
            be from any library offering dependency injection functionality, such as Microsoft.Extensions.DependencyInjection.
            For instance, using that library, <see cref="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.ConfigureServices(System.IServiceProvider)"/> can be used as follows in this example:
            <code>
            Ioc.Default.ConfigureServices(
                new ServiceCollection()
                .AddSingleton&lt;ILogger, Logger&gt;()
                .BuildServiceProvider());
            </code>
            Finally, you can use the <see cref="T:CommunityToolkit.Mvvm.DependencyInjection.Ioc"/> instance (which implements <see cref="T:System.IServiceProvider"/>)
            to retrieve the service instances from anywhere in your application, by doing as follows:
            <code>
            Ioc.Default.GetService&lt;ILogger&gt;().Log("Hello world!");
            </code>
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.DependencyInjection.Ioc.Default">
            <summary>
            Gets the default <see cref="T:CommunityToolkit.Mvvm.DependencyInjection.Ioc"/> instance.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.DependencyInjection.Ioc.serviceProvider">
            <summary>
            The <see cref="T:System.IServiceProvider"/> instance to use, if initialized.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.GetService(System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.GetService``1">
            <summary>
            Tries to resolve an instance of a specified service type.
            </summary>
            <typeparam name="T">The type of service to resolve.</typeparam>
            <returns>An instance of the specified service, or <see langword="null"/>.</returns>
            <exception cref="T:System.InvalidOperationException">Thrown if the current <see cref="T:CommunityToolkit.Mvvm.DependencyInjection.Ioc"/> instance has not been initialized.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.GetRequiredService``1">
            <summary>
            Resolves an instance of a specified service type.
            </summary>
            <typeparam name="T">The type of service to resolve.</typeparam>
            <returns>An instance of the specified service, or <see langword="null"/>.</returns>
            <exception cref="T:System.InvalidOperationException">
            Thrown if the current <see cref="T:CommunityToolkit.Mvvm.DependencyInjection.Ioc"/> instance has not been initialized, or if the
            requested service type was not registered in the service provider currently in use.
            </exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.ConfigureServices(System.IServiceProvider)">
            <summary>
            Initializes the shared <see cref="T:System.IServiceProvider"/> instance.
            </summary>
            <param name="serviceProvider">The input <see cref="T:System.IServiceProvider"/> instance to use.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="serviceProvider"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.ThrowInvalidOperationExceptionForMissingInitialization">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when the <see cref="T:System.IServiceProvider"/> property is used before initialization.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.ThrowInvalidOperationExceptionForUnregisteredType">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when the <see cref="T:System.IServiceProvider"/> property is missing a type registration.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.DependencyInjection.Ioc.ThrowInvalidOperationExceptionForRepeatedConfiguration">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when a configuration is attempted more than once.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand">
            <summary>
            A command that mirrors the functionality of <see cref="T:CommunityToolkit.Mvvm.Input.RelayCommand"/>, with the addition of
            accepting a <see cref="T:System.Func`1"/> returning a <see cref="T:System.Threading.Tasks.Task"/> as the execute
            action, and providing an <see cref="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecutionTask"/> property that notifies changes when
            <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecuteAsync(System.Object)"/> is invoked and when the returned <see cref="T:System.Threading.Tasks.Task"/> completes.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecutionTaskChangedEventArgs">
            <summary>
            The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> for <see cref="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecutionTask"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.CanBeCanceledChangedEventArgs">
            <summary>
            The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> for <see cref="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.CanBeCanceled"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.IsCancellationRequestedChangedEventArgs">
            <summary>
            The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> for <see cref="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.IsCancellationRequested"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.IsRunningChangedEventArgs">
            <summary>
            The cached <see cref="T:System.ComponentModel.PropertyChangedEventArgs"/> for <see cref="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.IsRunning"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.execute">
            <summary>
            The <see cref="T:System.Func`1"/> to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.Execute(System.Object)"/> is used.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.cancelableExecute">
            <summary>
            The cancelable <see cref="T:System.Func`2"/> to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.Execute(System.Object)"/> is used.
            </summary>
            <remarks>Only one between this and <see cref="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.execute"/> is not <see langword="null"/>.</remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.canExecute">
            <summary>
            The optional action to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.CanExecute(System.Object)"/> is used.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.options">
            <summary>
            The options being set for the current command.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.cancellationTokenSource">
            <summary>
            The <see cref="T:System.Threading.CancellationTokenSource"/> instance to use to cancel <see cref="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.cancelableExecute"/>.
            </summary>
            <remarks>This is only used when <see cref="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.cancelableExecute"/> is not <see langword="null"/>.</remarks>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.PropertyChanged">
            <inheritdoc/>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.CanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.Tasks.Task},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.Tasks.Task},System.Func{System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.Tasks.Task},System.Func{System.Boolean},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Func{System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Func{System.Boolean},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecutionTask">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.CanBeCanceled">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.IsCancellationRequested">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.IsRunning">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.CommunityToolkit#Mvvm#Input#Internals#ICancellationAwareCommand#IsCancellationSupported">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.NotifyCanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.CanExecute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.Execute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecuteAsync(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.Cancel">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.AwaitAndThrowIfFailed(System.Threading.Tasks.Task)">
            <summary>
            Awaits an input <see cref="T:System.Threading.Tasks.Task"/> and throws an exception on the calling context, if the task fails.
            </summary>
            <param name="executionTask">The input <see cref="T:System.Threading.Tasks.Task"/> instance to await.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions">
            <summary>
            Options to customize the behavior of <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> and <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> instances.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions.None">
            <summary>
            No option is specified. The <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> and <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> types will use their default behavior:
            <list type="bullet">
                <item>Concurrent execution is disallowed: a command is disabled if there is a pending asynchronous execution running.</item>
                <item>
                    <para>
                        Exceptions are thrown on the calling context: calling <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.Execute(System.Object)"/> will await the
                        returned <see cref="T:System.Threading.Tasks.Task"/> for the operation, and propagate the exception on the calling context.        
                    </para>
                    <para>This behavior is consistent with synchronous commands, where exceptions in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand.Execute(System.Object)"/> behave the same.</para>
                </item>
            </list>
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions.AllowConcurrentExecutions">
            <summary>
            <para>Concurrent executions are allowed. This option makes it so that the same command can be invoked concurrently multiple times.</para>
            <para>
            Note that additional considerations should be taken into account in this case:
            <list type="bullet">
                <item>If the command supports cancellation, previous invocations will automatically be canceled if a new one is started.</item>
                <item>The <see cref="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecutionTask"/> property will always represent the operation that was started last.</item>
            </list>
            </para>
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions.FlowExceptionsToTaskScheduler">
            <summary>
            <para>Exceptions are not thrown on the calling context, and are propagated to <see cref="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException"/> instead.</para>
            <para>
            This affects how calls to <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.Execute(System.Object)"/> behave. When this option is used, if an operation fails, that exception will not
            be rethrown on the calling context (as it is not awaited there). Instead, it will flow to <see cref="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException"/>.
            </para>
            <para>
            This option enables more advanced scenarios, where the <see cref="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.ExecutionTask"/> property can be used to inspect the state of an operation
            that was queued. That is, even if the operation failed or was canceled, the details of that can be retrieved at a later time by accessing this property.
            </para>
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1">
            <summary>
            A generic command that provides a more specific version of <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/>.
            </summary>
            <typeparam name="T">The type of parameter being passed as input to the callbacks.</typeparam>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.execute">
            <summary>
            The <see cref="T:System.Func`1"/> to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.Execute(`0)"/> is used.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.cancelableExecute">
            <summary>
            The cancelable <see cref="T:System.Func`3"/> to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.Execute(System.Object)"/> is used.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.canExecute">
            <summary>
            The optional action to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.CanExecute(`0)"/> is used.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.options">
            <summary>
            The options being set for the current command.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.cancellationTokenSource">
            <summary>
            The <see cref="T:System.Threading.CancellationTokenSource"/> instance to use to cancel <see cref="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.cancelableExecute"/>.
            </summary>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.PropertyChanged">
            <inheritdoc/>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.CanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.Tasks.Task})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.Tasks.Task},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.CancellationToken,System.Threading.Tasks.Task})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.CancellationToken,System.Threading.Tasks.Task},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.Tasks.Task},System.Predicate{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.Tasks.Task},System.Predicate{`0},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Predicate{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.#ctor(System.Func{`0,System.Threading.CancellationToken,System.Threading.Tasks.Task},System.Predicate{`0},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> class.
            </summary>
            <param name="cancelableExecute">The cancelable execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <param name="options">The options to use to configure the async command.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="cancelableExecute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.ExecutionTask">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.CanBeCanceled">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.IsCancellationRequested">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.IsRunning">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.CommunityToolkit#Mvvm#Input#Internals#ICancellationAwareCommand#IsCancellationSupported">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.NotifyCanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.CanExecute(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.CanExecute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.Execute(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.Execute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.ExecuteAsync(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.ExecuteAsync(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1.Cancel">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.RelayCommandAttribute">
             <summary>
             An attribute that can be used to automatically generate <see cref="T:System.Windows.Input.ICommand"/> properties from declared methods. When this attribute
             is used to decorate a method, a generator will create a command property with the corresponding <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> interface
             depending on the signature of the method. If an invalid method signature is used, the generator will report an error.
             <para>
             In order to use this attribute, the containing type doesn't need to implement any interfaces. The generated properties will be lazily
             assigned but their value will never change, so there is no need to support property change notifications or other additional functionality.
             </para>
             <para>
             This attribute can be used as follows:
             <code>
             partial class MyViewModel
             {
                 [RelayCommand]
                 private void GreetUser(User? user)
                 {
                     Console.WriteLine($"Hello {user.Name}!");
                 }
             }
             </code>
             And with this, code analogous to this will be generated:
             <code>
             partial class MyViewModel
             {
                 private RelayCommand? greetUserCommand;
            
                 public IRelayCommand GreetUserCommand => greetUserCommand ??= new RelayCommand(GreetUser);
             }
             </code>
             </para>
             <para>
             The following signatures are supported for annotated methods:
             <code>
             void Method();
             </code>
             Will generate an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> property (using a <see cref="T:CommunityToolkit.Mvvm.Input.RelayCommand"/> instance).
             <code>
             void Method(T?);
             </code>
             Will generate an <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand`1"/> property (using a <see cref="T:CommunityToolkit.Mvvm.Input.RelayCommand`1"/> instance).
             <code>
             Task Method();
             Task Method(CancellationToken);
             Task&lt;T&gt; Method();
             Task&lt;T&gt; Method(CancellationToken);
             </code>
             Will both generate an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> property (using an <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> instance).
             <code>
             Task Method(T?);
             Task Method(T?, CancellationToken);
             Task&lt;T&gt; Method(T?);
             Task&lt;T&gt; Method(T?, CancellationToken);
             </code>
             Will both generate an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1"/> property (using an <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> instance).
             </para>
             </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.RelayCommandAttribute.CanExecute">
            <summary>
            Gets or sets the name of the property or method that will be invoked to check whether the
            generated command can be executed at any given time. The referenced member needs to return
            a <see cref="T:System.Boolean"/> value, and has to have a signature compatible with the target command.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.RelayCommandAttribute.AllowConcurrentExecutions">
            <summary>
            Gets or sets a value indicating whether or not to allow concurrent executions for an asynchronous command.
            <para>
            When set for an attribute used on a method that would result in an <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> or an
            <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> property to be generated, this will modify the behavior of these commands
            when an execution is invoked while a previous one is still running. It is the same as creating an instance of
            these command types with a constructor such as <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.Tasks.Task},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)"/>
            and using the <see cref="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions.AllowConcurrentExecutions"/> value.
            </para>
            </summary>
            <remarks>Using this property is not valid if the target command doesn't map to an asynchronous command.</remarks>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.RelayCommandAttribute.FlowExceptionsToTaskScheduler">
            <summary>
            Gets or sets a value indicating whether or not to exceptions should be propagated to <see cref="E:System.Threading.Tasks.TaskScheduler.UnobservedTaskException"/>.
            <para>
            When set for an attribute used on a method that would result in an <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand"/> or an
            <see cref="T:CommunityToolkit.Mvvm.Input.AsyncRelayCommand`1"/> property to be generated, this will modify the behavior of these commands
            in case an exception is thrown by the underlying operation. It is the same as creating an instance of
            these command types with a constructor such as <see cref="M:CommunityToolkit.Mvvm.Input.AsyncRelayCommand.#ctor(System.Func{System.Threading.Tasks.Task},CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions)"/>
            and using the <see cref="F:CommunityToolkit.Mvvm.Input.AsyncRelayCommandOptions.FlowExceptionsToTaskScheduler"/> value.
            </para>
            </summary>
            <remarks>Using this property is not valid if the target command doesn't map to an asynchronous command.</remarks>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.RelayCommandAttribute.IncludeCancelCommand">
             <summary>
             Gets or sets a value indicating whether a cancel command should also be generated for an asynchronous command.
             <para>
             When set to <see langword="true"/>, this additional code will be generated:
             <code>
             partial class MyViewModel
             {
                 private ICommand? loginUserCancelCommand;
            
                 public ICommand LoginUserCancelCommand => loginUserCancelCommand ??= LoginUserCommand.CreateCancelCommand();
             }
             </code>
             Where <c>LoginUserCommand</c> is an <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> defined in the class (or generated by this attribute as well).
             </para>
             </summary>
             <remarks>Using this property is not valid if the target command doesn't map to a cancellable asynchronous command.</remarks>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommandExtensions">
            <summary>
            Extensions for the <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> type.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.IAsyncRelayCommandExtensions.CreateCancelCommand(CommunityToolkit.Mvvm.Input.IAsyncRelayCommand)">
            <summary>
            Creates an <see cref="T:System.Windows.Input.ICommand"/> instance that can be used to cancel execution on the input command.
            The returned command will also notify when it can be executed based on the state of the wrapped command.
            </summary>
            <param name="command">The input <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance to create a cancellation command for.</param>
            <returns>An <see cref="T:System.Windows.Input.ICommand"/> instance that can be used to monitor and signal cancellation for <paramref name="command"/>.</returns>
            <remarks>The returned instance is not guaranteed to be unique across multiple invocations with the same arguments.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="command"/> is <see langword="null"/>.</exception>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand">
            <summary>
            An interface expanding <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/> to support asynchronous operations.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.ExecutionTask">
            <summary>
            Gets the last scheduled <see cref="T:System.Threading.Tasks.Task"/>, if available.
            This property notifies a change when the <see cref="T:System.Threading.Tasks.Task"/> completes.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.CanBeCanceled">
            <summary>
            Gets a value indicating whether a running operation for this command can currently be canceled.
            </summary>
            <remarks>
            The exact sequence of events that types implementing this interface should raise is as follows:
            <list type="bullet">
            <item>
            The command is initially not running: <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsRunning"/>, <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.CanBeCanceled"/>
            and <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsCancellationRequested"/> are <see langword="false"/>.
            </item>
            <item>
            The command starts running: <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsRunning"/> and <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.CanBeCanceled"/> switch to
            <see langword="true"/>. <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsCancellationRequested"/> is set to <see langword="false"/>.
            </item>
            <item>
            If the operation is canceled: <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.CanBeCanceled"/> switches to <see langword="false"/>
            and <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsCancellationRequested"/> switches to <see langword="true"/>.
            </item>
            <item>
            The operation completes: <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsRunning"/> and <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.CanBeCanceled"/> switch
            to <see langword="false"/>. The state of <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsCancellationRequested"/> is undefined.
            </item>
            </list>
            This only applies if the underlying logic for the command actually supports cancelation. If that is
            not the case, then <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.CanBeCanceled"/> and <see cref="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsCancellationRequested"/> will always remain
            <see langword="false"/> regardless of the current state of the command.
            </remarks>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsCancellationRequested">
            <summary>
            Gets a value indicating whether a cancelation request has been issued for the current operation.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.IsRunning">
            <summary>
            Gets a value indicating whether the command currently has a pending operation being executed.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.ExecuteAsync(System.Object)">
            <summary>
            Provides a more specific version of <see cref="M:System.Windows.Input.ICommand.Execute(System.Object)"/>,
            also returning the <see cref="T:System.Threading.Tasks.Task"/> representing the async operation being executed.
            </summary>
            <param name="parameter">The input parameter.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> representing the async operation being executed.</returns>
            <exception cref="T:System.ArgumentException">Thrown if <paramref name="parameter"/> is incompatible with the underlying command implementation.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.Cancel">
            <summary>
            Communicates a request for cancelation.
            </summary>
            <remarks>
            If the underlying command is not running, or if it does not support cancelation, this method will perform no action.
            Note that even with a successful cancelation, the completion of the current operation might not be immediate.
            </remarks>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1">
            <summary>
            A generic interface representing a more specific version of <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/>.
            </summary>
            <typeparam name="T">The type used as argument for the interface methods.</typeparam>
            <remarks>This interface is needed to solve the diamond problem with base classes.</remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand`1.ExecuteAsync(`0)">
            <summary>
            Provides a strongly-typed variant of <see cref="M:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand.ExecuteAsync(System.Object)"/>.
            </summary>
            <param name="parameter">The input parameter.</param>
            <returns>The <see cref="T:System.Threading.Tasks.Task"/> representing the async operation being executed.</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.IRelayCommand">
            <summary>
            An interface expanding <see cref="T:System.Windows.Input.ICommand"/> with the ability to raise
            the <see cref="E:System.Windows.Input.ICommand.CanExecuteChanged"/> event externally.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.IRelayCommand.NotifyCanExecuteChanged">
            <summary>
            Notifies that the <see cref="M:System.Windows.Input.ICommand.CanExecute(System.Object)"/> property has changed.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.IRelayCommand`1">
            <summary>
            A generic interface representing a more specific version of <see cref="T:CommunityToolkit.Mvvm.Input.IRelayCommand"/>.
            </summary>
            <typeparam name="T">The type used as argument for the interface methods.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.IRelayCommand`1.CanExecute(`0)">
            <summary>
            Provides a strongly-typed variant of <see cref="M:System.Windows.Input.ICommand.CanExecute(System.Object)"/>.
            </summary>
            <param name="parameter">The input parameter.</param>
            <returns>Whether or not the current command can be executed.</returns>
            <remarks>Use this overload to avoid boxing, if <typeparamref name="T"/> is a value type.</remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.IRelayCommand`1.Execute(`0)">
            <summary>
            Provides a strongly-typed variant of <see cref="M:System.Windows.Input.ICommand.Execute(System.Object)"/>.
            </summary>
            <param name="parameter">The input parameter.</param>
            <remarks>Use this overload to avoid boxing, if <typeparamref name="T"/> is a value type.</remarks>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.Internals.CancelCommand">
            <summary>
            A <see cref="T:System.Windows.Input.ICommand"/> implementation wrapping <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> to support cancellation.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.Internals.CancelCommand.command">
            <summary>
            The wrapped <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.Internals.CancelCommand.#ctor(CommunityToolkit.Mvvm.Input.IAsyncRelayCommand)">
            <summary>
            Creates a new <see cref="T:CommunityToolkit.Mvvm.Input.Internals.CancelCommand"/> instance.
            </summary>
            <param name="command">The <see cref="T:CommunityToolkit.Mvvm.Input.IAsyncRelayCommand"/> instance to wrap.</param>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.Internals.CancelCommand.CanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.Internals.CancelCommand.CanExecute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.Internals.CancelCommand.Execute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.Internals.CancelCommand.OnPropertyChanged(System.Object,System.ComponentModel.PropertyChangedEventArgs)">
            <inheritdoc cref="T:System.ComponentModel.PropertyChangedEventHandler"/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.Internals.DisabledCommand">
            <summary>
            A reusable <see cref="T:System.Windows.Input.ICommand"/> instance that is always disabled.
            </summary>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.Internals.DisabledCommand.CanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.Internals.DisabledCommand.Instance">
            <summary>
            Gets a shared, reusable <see cref="T:CommunityToolkit.Mvvm.Input.Internals.DisabledCommand"/> instance.
            </summary>
            <remarks>
            This instance can safely be used across multiple objects without having
            to worry about this static keeping others alive, as the event uses a
            custom accessor that just discards items (as the event is known to never
            be raised). As such, this instance will never act as root for other objects.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.Internals.DisabledCommand.CanExecute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.Internals.DisabledCommand.Execute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.Internals.ICancellationAwareCommand">
            <summary>
            An interface for commands that know whether they support cancellation or not.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Input.Internals.ICancellationAwareCommand.IsCancellationSupported">
            <summary>
            Gets whether or not the current command supports cancellation.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.RelayCommand">
            <summary>
            A command whose sole purpose is to relay its functionality to other
            objects by invoking delegates. The default return value for the <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand.CanExecute(System.Object)"/>
            method is <see langword="true"/>. This type does not allow you to accept command parameters
            in the <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand.Execute(System.Object)"/> and <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand.CanExecute(System.Object)"/> callback methods.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.RelayCommand.execute">
            <summary>
            The <see cref="T:System.Action"/> to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand.Execute(System.Object)"/> is used.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.RelayCommand.canExecute">
            <summary>
            The optional action to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand.CanExecute(System.Object)"/> is used.
            </summary>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.RelayCommand.CanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand.#ctor(System.Action)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.RelayCommand"/> class that can always execute.
            </summary>
            <param name="execute">The execution logic.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand.#ctor(System.Action,System.Func{System.Boolean})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.RelayCommand"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand.NotifyCanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand.CanExecute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand.Execute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Input.RelayCommand`1">
            <summary>
            A generic command whose sole purpose is to relay its functionality to other
            objects by invoking delegates. The default return value for the CanExecute
            method is <see langword="true"/>. This class allows you to accept command parameters
            in the <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.Execute(`0)"/> and <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.CanExecute(`0)"/> callback methods.
            </summary>
            <typeparam name="T">The type of parameter being passed as input to the callbacks.</typeparam>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.RelayCommand`1.execute">
            <summary>
            The <see cref="T:System.Action"/> to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.Execute(`0)"/> is used.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Input.RelayCommand`1.canExecute">
            <summary>
            The optional action to invoke when <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.CanExecute(`0)"/> is used.
            </summary>
        </member>
        <member name="E:CommunityToolkit.Mvvm.Input.RelayCommand`1.CanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.RelayCommand`1"/> class that can always execute.
            </summary>
            <param name="execute">The execution logic.</param>
            <remarks>
            Due to the fact that the <see cref="T:System.Windows.Input.ICommand"/> interface exposes methods that accept a
            nullable <see cref="T:System.Object"/> parameter, it is recommended that if <typeparamref name="T"/> is a reference type,
            you should always declare it as nullable, and to always perform checks within <paramref name="execute"/>.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0},System.Predicate{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Input.RelayCommand`1"/> class.
            </summary>
            <param name="execute">The execution logic.</param>
            <param name="canExecute">The execution status logic.</param>
            <remarks>See notes in <see cref="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.#ctor(System.Action{`0})"/>.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="execute"/> or <paramref name="canExecute"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.NotifyCanExecuteChanged">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.CanExecute(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.CanExecute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.Execute(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.Execute(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.TryGetCommandArgument(System.Object,`0@)">
            <summary>
            Tries to get a command argument of compatible type <typeparamref name="T"/> from an input <see cref="T:System.Object"/>.
            </summary>
            <param name="parameter">The input parameter.</param>
            <param name="result">The resulting <typeparamref name="T"/> value, if any.</param>
            <returns>Whether or not a compatible command argument could be retrieved.</returns>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Input.RelayCommand`1.ThrowArgumentExceptionForInvalidCommandArgument(System.Object)">
            <summary>
            Throws an <see cref="T:System.ArgumentException"/> if an invalid command argument is used.
            </summary>
            <param name="parameter">The input parameter.</param>
            <exception cref="T:System.ArgumentException">Thrown with an error message to give info on the invalid parameter.</exception>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessenger">
            <summary>
            An interface for a type providing the ability to exchange messages between different objects.
            This can be useful to decouple different modules of an application without having to keep strong
            references to types being referenced. It is also possible to send messages to specific channels, uniquely
            identified by a token, and to have different messengers in different sections of an applications.
            In order to use the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> functionalities, first define a message type, like so:
            <code>
            public sealed class LoginCompletedMessage { }
            </code>
            Then, register your a recipient for this message:
            <code>
            Messenger.Default.Register&lt;MyRecipientType, LoginCompletedMessage&gt;(this, (r, m) =>
            {
                // Handle the message here...
            });
            </code>
            The message handler here is a lambda expression taking two parameters: the recipient and the message.
            This is done to avoid the allocations for the closures that would've been generated if the expression
            had captured the current instance. The recipient type parameter is used so that the recipient can be
            directly accessed within the handler without the need to manually perform type casts. This allows the
            code to be less verbose and more reliable, as all the checks are done just at build time. If the handler
            is defined within the same type as the recipient, it is also possible to directly access private members.
            This allows the message handler to be a static method, which enables the C# compiler to perform a number
            of additional memory optimizations (such as caching the delegate, avoiding unnecessary memory allocations).
            Finally, send a message when needed, like so:
            <code>
            Messenger.Default.Send&lt;LoginCompletedMessage&gt;();
            </code>
            Additionally, the method group syntax can also be used to specify the message handler
            to invoke when receiving a message, if a method with the right signature is available
            in the current scope. This is helpful to keep the registration and handling logic separate.
            Following up from the previous example, consider a class having this method:
            <code>
            private static void Receive(MyRecipientType recipient, LoginCompletedMessage message)
            {
                // Handle the message there
            }
            </code>
            The registration can then be performed in a single line like so:
            <code>
            Messenger.Default.Register(this, Receive);
            </code>
            The C# compiler will automatically convert that expression to a <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> instance
            compatible with <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``2(CommunityToolkit.Mvvm.Messaging.IMessenger,``0,CommunityToolkit.Mvvm.Messaging.MessageHandler{``0,``1})"/>.
            This will also work if multiple overloads of that method are available, each handling a different
            message type: the C# compiler will automatically pick the right one for the current message type.
            It is also possible to register message handlers explicitly using the <see cref="T:CommunityToolkit.Mvvm.Messaging.IRecipient`1"/> interface.
            To do so, the recipient just needs to implement the interface and then call the
            <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.RegisterAll(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object)"/> extension, which will automatically register
            all the handlers that are declared by the recipient type. Registration for individual handlers is supported as well.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.IsRegistered``2(System.Object,``1)">
            <summary>
            Checks whether or not a given recipient has already been registered for a message.
            </summary>
            <typeparam name="TMessage">The type of message to check for the given recipient.</typeparam>
            <typeparam name="TToken">The type of token to check the channel for.</typeparam>
            <param name="recipient">The target recipient to check the registration for.</param>
            <param name="token">The token used to identify the target channel to check.</param>
            <returns>Whether or not <paramref name="recipient"/> has already been registered for the specified message.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="recipient"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.Register``3(``0,``2,CommunityToolkit.Mvvm.Messaging.MessageHandler{``0,``1})">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TRecipient">The type of recipient for the message.</typeparam>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <typeparam name="TToken">The type of token to use to pick the messages to receive.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
            <param name="handler">The <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> to invoke when a message is received.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="recipient"/>, <paramref name="token"/> or <paramref name="handler"/> are <see langword="null"/>.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.UnregisterAll(System.Object)">
            <summary>
            Unregisters a recipient from all registered messages.
            </summary>
            <param name="recipient">The recipient to unregister.</param>
            <remarks>
            This method will unregister the target recipient across all channels.
            Use this method as an easy way to lose all references to a target recipient.
            If the recipient has no registered handler, this method does nothing.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="recipient"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.UnregisterAll``1(System.Object,``0)">
            <summary>
            Unregisters a recipient from all messages on a specific channel.
            </summary>
            <typeparam name="TToken">The type of token to identify what channel to unregister from.</typeparam>
            <param name="recipient">The recipient to unregister.</param>
            <param name="token">The token to use to identify which handlers to unregister.</param>
            <remarks>If the recipient has no registered handler, this method does nothing.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="recipient"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.Unregister``2(System.Object,``1)">
            <summary>
            Unregisters a recipient from messages of a given type.
            </summary>
            <typeparam name="TMessage">The type of message to stop receiving.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to unregister from.</typeparam>
            <param name="recipient">The recipient to unregister.</param>
            <param name="token">The token to use to identify which handlers to unregister.</param>
            <remarks>If the recipient has no registered handler, this method does nothing.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="recipient"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.Send``2(``0,``1)">
            <summary>
            Sends a message of the specified type to all registered recipients.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to use to send the message.</typeparam>
            <param name="message">The message to send.</param>
            <param name="token">The token indicating what channel to use.</param>
            <returns>The message that was sent (ie. <paramref name="message"/>).</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="message"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.Cleanup">
            <summary>
            Performs a cleanup on the current messenger.
            Invoking this method does not unregister any of the currently registered
            recipient, and it can be used to perform cleanup operations such as
            trimming the internal data structures of a messenger implementation.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessenger.Reset">
            <summary>
            Resets the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance and unregisters all the existing recipients.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions">
            <summary>
            Extensions for the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> type.
            </summary>
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.MethodInfos">
            <summary>
            A class that acts as a container to load the <see cref="T:System.Reflection.MethodInfo"/> instance linked to
            the <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``2(CommunityToolkit.Mvvm.Messaging.IMessenger,CommunityToolkit.Mvvm.Messaging.IRecipient{``0},``1)"/> method.
            This class is needed to avoid forcing the initialization code in the static constructor to run as soon as
            the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions"/> type is referenced, even if that is done just to use methods
            that do not actually require this <see cref="T:System.Reflection.MethodInfo"/> instance to be available.
            We're effectively using this type to leverage the lazy loading of static constructors done by the runtime.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.MethodInfos.RegisterIRecipient">
            <summary>
            The <see cref="T:System.Reflection.MethodInfo"/> instance associated with <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``2(CommunityToolkit.Mvvm.Messaging.IMessenger,CommunityToolkit.Mvvm.Messaging.IRecipient{``0},``1)"/>.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.DiscoveredRecipients">
            <summary>
            A non-generic version of <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.DiscoveredRecipients`1"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.DiscoveredRecipients.RegistrationMethods">
            <summary>
            The <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable`2"/> instance used to track the preloaded registration action for each recipient.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.DiscoveredRecipients`1">
            <summary>
            A class that acts as a static container to associate a <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable`2"/> instance to each
            <typeparamref name="TToken"/> type in use. This is done because we can only use a single type as key, but we need to track
            associations of each recipient type also across different communication channels, each identified by a token.
            Since the token is actually a compile-time parameter, we can use a wrapping class to let the runtime handle a different
            instance for each generic type instantiation. This lets us only worry about the recipient type being inspected.
            </summary>
            <typeparam name="TToken">The token indicating what channel to use.</typeparam>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.DiscoveredRecipients`1.RegistrationMethods">
            <summary>
            The <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable`2"/> instance used to track the preloaded registration action for each recipient.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.IsRegistered``1(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object)">
            <summary>
            Checks whether or not a given recipient has already been registered for a message.
            </summary>
            <typeparam name="TMessage">The type of message to check for the given recipient.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to check the registration.</param>
            <param name="recipient">The target recipient to check the registration for.</param>
            <returns>Whether or not <paramref name="recipient"/> has already been registered for the specified message.</returns>
            <remarks>This method will use the default channel to check for the requested registration.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> or <paramref name="recipient"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.RegisterAll(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object)">
            <summary>
            Registers all declared message handlers for a given recipient, using the default channel.
            </summary>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="recipient">The recipient that will receive the messages.</param>
            <remarks>See notes for <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.RegisterAll``1(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object,``0)"/> for more info.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> or <paramref name="recipient"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.RegisterAll``1(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object,``0)">
            <summary>
            Registers all declared message handlers for a given recipient.
            </summary>
            <typeparam name="TToken">The type of token to identify what channel to use to receive messages.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">The token indicating what channel to use.</param>
            <remarks>
            This method will register all messages corresponding to the <see cref="T:CommunityToolkit.Mvvm.Messaging.IRecipient`1"/> interfaces
            being implemented by <paramref name="recipient"/>. If none are present, this method will do nothing.
            Note that unlike all other extensions, this method will use reflection to find the handlers to register.
            Once the registration is complete though, the performance will be exactly the same as with handlers
            registered directly through any of the other generic extensions for the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> interface.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/>, <paramref name="recipient"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``1(CommunityToolkit.Mvvm.Messaging.IMessenger,CommunityToolkit.Mvvm.Messaging.IRecipient{``0})">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="recipient">The recipient that will receive the messages.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
            <remarks>This method will use the default channel to perform the requested registration.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> or <paramref name="recipient"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``2(CommunityToolkit.Mvvm.Messaging.IMessenger,CommunityToolkit.Mvvm.Messaging.IRecipient{``0},``1)">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to use to receive messages.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">The token indicating what channel to use.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
            <remarks>This method will use the default channel to perform the requested registration.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/>, <paramref name="recipient"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``1(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object,CommunityToolkit.Mvvm.Messaging.MessageHandler{System.Object,``0})">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="handler">The <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> to invoke when a message is received.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
            <remarks>This method will use the default channel to perform the requested registration.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/>, <paramref name="recipient"/> or <paramref name="handler"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``2(CommunityToolkit.Mvvm.Messaging.IMessenger,``0,CommunityToolkit.Mvvm.Messaging.MessageHandler{``0,``1})">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TRecipient">The type of recipient for the message.</typeparam>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="handler">The <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> to invoke when a message is received.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
            <remarks>This method will use the default channel to perform the requested registration.</remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/>, <paramref name="recipient"/> or <paramref name="handler"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Register``2(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object,``1,CommunityToolkit.Mvvm.Messaging.MessageHandler{System.Object,``0})">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <typeparam name="TToken">The type of token to use to pick the messages to receive.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
            <param name="handler">The <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> to invoke when a message is received.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/>, <paramref name="recipient"/> or <paramref name="handler"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Unregister``1(CommunityToolkit.Mvvm.Messaging.IMessenger,System.Object)">
            <summary>
            Unregisters a recipient from messages of a given type.
            </summary>
            <typeparam name="TMessage">The type of message to stop receiving.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to unregister the recipient.</param>
            <param name="recipient">The recipient to unregister.</param>
            <remarks>
            This method will unregister the target recipient only from the default channel.
            If the recipient has no registered handler, this method does nothing.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> or <paramref name="recipient"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Send``1(CommunityToolkit.Mvvm.Messaging.IMessenger)">
            <summary>
            Sends a message of the specified type to all registered recipients.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to send the message.</param>
            <returns>The message that has been sent.</returns>
            <remarks>
            This method is a shorthand for <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Send``1(CommunityToolkit.Mvvm.Messaging.IMessenger,``0)"/> when the
            message type exposes a parameterless constructor: it will automatically create
            a new <typeparamref name="TMessage"/> instance and send that to its recipients.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Send``1(CommunityToolkit.Mvvm.Messaging.IMessenger,``0)">
            <summary>
            Sends a message of the specified type to all registered recipients.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to send the message.</param>
            <param name="message">The message to send.</param>
            <returns>The message that was sent (ie. <paramref name="message"/>).</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> or <paramref name="message"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Send``2(CommunityToolkit.Mvvm.Messaging.IMessenger,``1)">
            <summary>
            Sends a message of the specified type to all registered recipients.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to use to send the message.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to send the message.</param>
            <param name="token">The token indicating what channel to use.</param>
            <returns>The message that has been sen.</returns>
            <remarks>
            This method will automatically create a new <typeparamref name="TMessage"/> instance
            just like <see cref="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Send``1(CommunityToolkit.Mvvm.Messaging.IMessenger)"/>, and then send it to the right recipients.
            </remarks>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.CreateObservable``1(CommunityToolkit.Mvvm.Messaging.IMessenger)">
            <summary>
            Creates an <see cref="T:System.IObservable`1"/> instance that can be used to be notified whenever a message of a given type is broadcast by a messenger.
            </summary>
            <typeparam name="TMessage">The type of message to use to receive notification for through the resulting <see cref="T:System.IObservable`1"/> instance.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <returns>An <see cref="T:System.IObservable`1"/> instance to receive notifications for <typeparamref name="TMessage"/> messages being broadcast.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.CreateObservable``2(CommunityToolkit.Mvvm.Messaging.IMessenger,``1)">
            <summary>
            Creates an <see cref="T:System.IObservable`1"/> instance that can be used to be notified whenever a message of a given type is broadcast by a messenger.
            </summary>
            <typeparam name="TMessage">The type of message to use to receive notification for through the resulting <see cref="T:System.IObservable`1"/> instance.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to use to receive messages.</typeparam>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
            <returns>An <see cref="T:System.IObservable`1"/> instance to receive notifications for <typeparamref name="TMessage"/> messages being broadcast.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="messenger"/> or <paramref name="token"/> are <see langword="null"/>.</exception>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1">
            <summary>
            An <see cref="T:System.IObservable`1"/> implementations for a given message type.
            </summary>
            <typeparam name="TMessage">The type of messages to listen to.</typeparam>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.messenger">
            <summary>
            The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.#ctor(CommunityToolkit.Mvvm.Messaging.IMessenger)">
            <summary>
            Creates a new <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1"/> instance with the given parameters.
            </summary>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Subscribe(System.IObserver{`0})">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Recipient">
            <summary>
            An <see cref="T:CommunityToolkit.Mvvm.Messaging.IRecipient`1"/> implementation for <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Recipient.messenger">
            <summary>
            The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Recipient.observer">
            <summary>
            The target <see cref="T:System.IObserver`1"/> instance currently in use.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Recipient.#ctor(CommunityToolkit.Mvvm.Messaging.IMessenger,System.IObserver{`0})">
            <summary>
            Creates a new <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Recipient"/> instance with the specified parameters.
            </summary>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="observer">The <see cref="T:System.IObserver`1"/> instance to use to create the recipient for.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Recipient.Receive(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`1.Recipient.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2">
            <summary>
            An <see cref="T:System.IObservable`1"/> implementations for a given pair of message and token types.
            </summary>
            <typeparam name="TMessage">The type of messages to listen to.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to use to receive messages.</typeparam>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.messenger">
            <summary>
            The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.token">
            <summary>
            The token used to determine the receiving channel to use.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.#ctor(CommunityToolkit.Mvvm.Messaging.IMessenger,`1)">
            <summary>
            Creates a new <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2"/> instance with the given parameters.
            </summary>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Subscribe(System.IObserver{`0})">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient">
            <summary>
            An <see cref="T:CommunityToolkit.Mvvm.Messaging.IRecipient`1"/> implementation for <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2"/>.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient.messenger">
            <summary>
            The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient.observer">
            <summary>
            The target <see cref="T:System.IObserver`1"/> instance currently in use.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient.token">
            <summary>
            The token used to determine the receiving channel to use.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient.#ctor(CommunityToolkit.Mvvm.Messaging.IMessenger,System.IObserver{`0},`1)">
            <summary>
            Creates a new <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient"/> instance with the specified parameters.
            </summary>
            <param name="messenger">The <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> instance to use to register the recipient.</param>
            <param name="observer">The <see cref="T:System.IObserver`1"/> instance to use to create the recipient for.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient.Receive(`0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IMessengerExtensions.Observable`2.Recipient.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1">
            <summary>
            A simple buffer writer implementation using pooled arrays.
            </summary>
            <typeparam name="T">The type of items to store in the list.</typeparam>
            <remarks>
            This type is a <see langword="ref"/> <see langword="struct"/> to avoid the object allocation and to
            enable the pattern-based <see cref="T:System.IDisposable"/> support. We aren't worried with consumers not
            using this type correctly since it's private and only accessible within the parent type.
            </remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.DefaultInitialBufferSize">
            <summary>
            The default buffer size to use to expand empty arrays.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.array">
            <summary>
            The underlying <typeparamref name="T"/> array.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.span">
            <summary>
            The span mapping to <see cref="F:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.array"/>.
            </summary>
            <remarks>All writes are done through this to avoid covariance checks.</remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.index">
            <summary>
            The starting offset within <see cref="F:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.array"/>.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.Create">
            <summary>
            Creates a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1"/> struct.
            </summary>
            <returns>A new <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1"/> instance.</returns>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.Span">
            <summary>
            Gets a <see cref="T:System.ReadOnlySpan`1"/> with the current items.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.Add(`0)">
            <summary>
            Adds a new item to the current collection.
            </summary>
            <param name="item">The item to add.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.Reset">
            <summary>
            Resets the underlying array and the stored items.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.ResizeBufferAndAdd(`0)">
            <summary>
            Resizes <see cref="F:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.array"/> when there is no space left for new items, then adds one
            </summary>
            <param name="item">The item to add.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.ArrayPoolBufferWriter`1.Dispose">
            <inheritdoc cref="M:System.IDisposable.Dispose"/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher">
            <summary>
            A dispatcher type that invokes a given <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> callback.
            </summary>
            <remarks>
            This type is used to avoid type aliasing with <see cref="M:System.Runtime.CompilerServices.Unsafe.As``1(System.Object)"/> when the generic
            arguments are not known. Additionally, this is an abstract class and not an interface so that when
            <see cref="M:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher.Invoke(System.Object,System.Object)"/> is called, virtual dispatch will be used instead of interface
            stub dispatch, which is much slower and with more indirections.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher.Invoke(System.Object,System.Object)">
            <summary>
            Invokes the current callback on a target recipient, with a specified message.
            </summary>
            <param name="recipient">The target recipient for the message.</param>
            <param name="message">The message being broadcast.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher.For`2">
            <summary>
            A generic version of <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher"/>.
            </summary>
            <typeparam name="TRecipient">The type of recipient for the message.</typeparam>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher.For`2.handler">
            <summary>
            The underlying <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> callback to invoke.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher.For`2.#ctor(CommunityToolkit.Mvvm.Messaging.MessageHandler{`0,`1})">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher.For`2"/> class.
            </summary>
            <param name="handler">The input <see cref="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2"/> instance.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher.For`2.Invoke(System.Object,System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Internals.Type2">
            <summary>
            A simple type representing an immutable pair of types.
            </summary>
            <remarks>
            This type replaces a simple <see cref="T:System.ValueTuple`2"/> as it's faster in its
            <see cref="M:CommunityToolkit.Mvvm.Messaging.Internals.Type2.GetHashCode"/> and <see cref="M:System.IEquatable`1.Equals(`0)"/> methods, and because
            unlike a value tuple it exposes its fields as immutable. Additionally, the
            <see cref="F:CommunityToolkit.Mvvm.Messaging.Internals.Type2.TMessage"/> and <see cref="F:CommunityToolkit.Mvvm.Messaging.Internals.Type2.TToken"/> fields provide additional clarity reading
            the code compared to <see cref="F:System.ValueTuple`2.Item1"/> and <see cref="F:System.ValueTuple`2.Item2"/>.
            </remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Internals.Type2.TMessage">
            <summary>
            The type of registered message.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Internals.Type2.TToken">
            <summary>
            The type of registration token.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.Type2.#ctor(System.Type,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.Type2"/> struct.
            </summary>
            <param name="tMessage">The type of registered message.</param>
            <param name="tToken">The type of registration token.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.Type2.Equals(CommunityToolkit.Mvvm.Messaging.Internals.Type2)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.Type2.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.Type2.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Internals.Unit">
            <summary>
            An empty type representing a generic token with no specific value.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.Unit.Equals(CommunityToolkit.Mvvm.Messaging.Internals.Unit)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.Unit.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Internals.Unit.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.IRecipient`1">
            <summary>
            An interface for a recipient that declares a registration for a specific message type.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.IRecipient`1.Receive(`0)">
            <summary>
            Receives a given <typeparamref name="TMessage"/> message instance.
            </summary>
            <param name="message">The message being received.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.MessageHandler`2">
            <summary>
            A <see langword="delegate"/> used to represent actions to invoke when a message is received.
            The recipient is given as an input argument to allow message registrations to avoid creating
            closures: if an instance method on a recipient needs to be invoked it is possible to just
            cast the recipient to the right type and then access the local method from that instance.
            </summary>
            <typeparam name="TRecipient">The type of recipient for the message.</typeparam>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <param name="recipient">The recipient that is receiving the message.</param>
            <param name="message">The message being received.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1">
            <summary>
            A <see langword="class"/> for request messages that can receive multiple replies, which can either be used directly or through derived classes.
            </summary>
            <typeparam name="T">The type of request to make.</typeparam>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.responses">
            <summary>
            The collection of received replies. We accept both <see cref="T:System.Threading.Tasks.Task`1"/> instance, representing already running
            operations that can be executed in parallel, or <see cref="T:System.Func`2"/> instances, which can be used so that multiple
            asynchronous operations are only started sequentially from <see cref="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.GetAsyncEnumerator(System.Threading.CancellationToken)"/> and do not overlap in time.
            </summary>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.cancellationTokenSource">
            <summary>
            The <see cref="T:System.Threading.CancellationTokenSource"/> instance used to link the token passed to
            <see cref="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.GetAsyncEnumerator(System.Threading.CancellationToken)"/> and the one passed to all subscribers to the message.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.CancellationToken">
            <summary>
            Gets the <see cref="T:System.Threading.CancellationToken"/> instance that will be linked to the
            one used to asynchronously enumerate the received responses. This can be used to cancel asynchronous
            replies that are still being processed, if no new items are needed from this request message.
            Consider the following example, where we define a message to retrieve the currently opened documents:
            <code>
            public class OpenDocumentsRequestMessage : AsyncCollectionRequestMessage&lt;XmlDocument&gt; { }
            </code>
            We can then request and enumerate the results like so:
            <code>
            await foreach (var document in Messenger.Default.Send&lt;OpenDocumentsRequestMessage&gt;())
            {
                // Process each document here...
            }
            </code>
            If we also want to control the cancellation of the token passed to each subscriber to the message,
            we can do so by passing a token we control to the returned message before starting the enumeration
            (<see cref="M:System.Threading.Tasks.TaskAsyncEnumerableExtensions.WithCancellation``1(System.Collections.Generic.IAsyncEnumerable{``0},System.Threading.CancellationToken)"/>).
            The previous snippet with this additional change looks as follows:
            <code>
            await foreach (var document in Messenger.Default.Send&lt;OpenDocumentsRequestMessage&gt;().WithCancellation(cts.Token))
            {
                // Process each document here...
            }
            </code>
            When no more new items are needed (or for any other reason depending on the situation), the token
            passed to the enumerator can be canceled (by calling <see cref="M:System.Threading.CancellationTokenSource.Cancel"/>),
            and that will also notify the remaining tasks in the request message. The token exposed by the message
            itself will automatically be linked and canceled with the one passed to the enumerator.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.Reply(`0)">
            <summary>
            Replies to the current request message.
            </summary>
            <param name="response">The response to use to reply to the request message.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.Reply(System.Threading.Tasks.Task{`0})">
            <summary>
            Replies to the current request message.
            </summary>
            <param name="response">The response to use to reply to the request message.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="response"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.Reply(System.Func{System.Threading.CancellationToken,System.Threading.Tasks.Task{`0}})">
            <summary>
            Replies to the current request message.
            </summary>
            <param name="response">The response to use to reply to the request message.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="response"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.GetResponsesAsync(System.Threading.CancellationToken)">
            <summary>
            Gets the collection of received response items.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> value to stop the operation.</param>
            <returns>The collection of received response items.</returns>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncCollectionRequestMessage`1.GetAsyncEnumerator(System.Threading.CancellationToken)">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1">
            <summary>
            A <see langword="class"/> for async request messages, which can either be used directly or through derived classes.
            </summary>
            <typeparam name="T">The type of request to make.</typeparam>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.Response">
            <summary>
            Gets the message response.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown when <see cref="P:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.HasReceivedResponse"/> is <see langword="false"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.HasReceivedResponse">
            <summary>
            Gets a value indicating whether a response has already been assigned to this instance.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.Reply(`0)">
            <summary>
            Replies to the current request message.
            </summary>
            <param name="response">The response to use to reply to the request message.</param>
            <exception cref="T:System.InvalidOperationException">Thrown if <see cref="P:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.Response"/> has already been set.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.Reply(System.Threading.Tasks.Task{`0})">
            <summary>
            Replies to the current request message.
            </summary>
            <param name="response">The response to use to reply to the request message.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="response"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown if <see cref="P:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.Response"/> has already been set.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.GetAwaiter">
            <inheritdoc cref="M:System.Threading.Tasks.Task`1.GetAwaiter"/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.ThrowInvalidOperationExceptionForNoResponseReceived">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when a response is not available.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.ThrowInvalidOperationExceptionForDuplicateReply">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when <see cref="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.Reply(`0)"/> or <see cref="M:CommunityToolkit.Mvvm.Messaging.Messages.AsyncRequestMessage`1.Reply(System.Threading.Tasks.Task{`0})"/> are called twice.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Messages.CollectionRequestMessage`1">
            <summary>
            A <see langword="class"/> for request messages that can receive multiple replies, which can either be used directly or through derived classes.
            </summary>
            <typeparam name="T">The type of request to make.</typeparam>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.CollectionRequestMessage`1.Responses">
            <summary>
            Gets the message responses.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.CollectionRequestMessage`1.Reply(`0)">
            <summary>
            Replies to the current request message.
            </summary>
            <param name="response">The response to use to reply to the request message.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.CollectionRequestMessage`1.GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.CollectionRequestMessage`1.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1">
            <summary>
            A message used to broadcast property changes in observable objects.
            </summary>
            <typeparam name="T">The type of the property to broadcast the change for.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1.#ctor(System.Object,System.String,`0,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1"/> class.
            </summary>
            <param name="sender">The original sender of the broadcast message.</param>
            <param name="propertyName">The name of the property that changed.</param>
            <param name="oldValue">The value that the property had before the change.</param>
            <param name="newValue">The value that the property has after the change.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="sender"/> is <see langword="null"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1.Sender">
            <summary>
            Gets the original sender of the broadcast message.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1.PropertyName">
            <summary>
            Gets the name of the property that changed.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1.OldValue">
            <summary>
            Gets the value that the property had before the change.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.PropertyChangedMessage`1.NewValue">
            <summary>
            Gets the value that the property has after the change.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1">
            <summary>
            A <see langword="class"/> for request messages, which can either be used directly or through derived classes.
            </summary>
            <typeparam name="T">The type of request to make.</typeparam>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.Response">
            <summary>
            Gets the message response.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown when <see cref="P:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.HasReceivedResponse"/> is <see langword="false"/>.</exception>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.HasReceivedResponse">
            <summary>
            Gets a value indicating whether a response has already been assigned to this instance.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.Reply(`0)">
            <summary>
            Replies to the current request message.
            </summary>
            <param name="response">The response to use to reply to the request message.</param>
            <exception cref="T:System.InvalidOperationException">Thrown if <see cref="P:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.Response"/> has already been set.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.op_Implicit(CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage{`0})~`0">
            <summary>
            Implicitly gets the response from a given <see cref="T:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1"/> instance.
            </summary>
            <param name="message">The input <see cref="T:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1"/> instance.</param>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="message"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.InvalidOperationException">Thrown when <see cref="P:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.HasReceivedResponse"/> is <see langword="false"/>.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.ThrowInvalidOperationExceptionForNoResponseReceived">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when a response is not available.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.ThrowInvalidOperationExceptionForDuplicateReply">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when <see cref="M:CommunityToolkit.Mvvm.Messaging.Messages.RequestMessage`1.Reply(`0)"/> is called twice.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.Messages.ValueChangedMessage`1">
            <summary>
            A base message that signals whenever a specific value has changed.
            </summary>
            <typeparam name="T">The type of value that has changed.</typeparam>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.Messages.ValueChangedMessage`1.#ctor(`0)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.Messages.ValueChangedMessage`1"/> class.
            </summary>
            <param name="value">The value that has changed.</param>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.Messages.ValueChangedMessage`1.Value">
            <summary>
            Gets the value that has changed.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger">
            <summary>
            A class providing a reference implementation for the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> interface.
            </summary>
            <remarks>
            This <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> implementation uses strong references to track the registered
            recipients, so it is necessary to manually unregister them when they're no longer needed.
            </remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.recipientsMap">
            <summary>
            The collection of currently registered recipients, with a link to their linked message receivers.
            </summary>
            <remarks>
            This collection is used to allow reflection-free access to all the existing
            registered recipients from <see cref="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.UnregisterAll(System.Object)"/> and other methods in this type,
            so that all the existing handlers can be removed without having to dynamically create
            the generic types for the containers of the various dictionaries mapping the handlers.
            </remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.typesMap">
            <summary>
            The <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> and <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> instance for types combination.
            </summary>
            <remarks>
            The values are just of type <see cref="T:System.Collections.Generic.IDictionary2`1"/> as we don't know the type parameters in advance.
            Each method relies on <see cref="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.GetOrAddMapping``2"/> to get the type-safe instance of the
            <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> or <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> class for each pair of generic arguments in use.
            </remarks>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Default">
            <summary>
            Gets the default <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger"/> instance.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.IsRegistered``2(System.Object,``1)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Register``3(``0,``2,CommunityToolkit.Mvvm.Messaging.MessageHandler{``0,``1})">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Register``2(CommunityToolkit.Mvvm.Messaging.IRecipient{``0},``1)">
            <inheritdoc cref="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Register``2(CommunityToolkit.Mvvm.Messaging.IRecipient{``0},``1)"/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Register``2(System.Object,``1,CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher)">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <typeparam name="TToken">The type of token to use to pick the messages to receive.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
            <param name="dispatcher">The input <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher"/> instance to register, or null.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.UnregisterAll(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.UnregisterAll``1(System.Object,``0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Unregister``2(System.Object,``1)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Send``2(``0,``1)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.CommunityToolkit#Mvvm#Messaging#IMessenger#Cleanup">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Reset">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.TryGetMapping``1(CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping@)">
            <summary>
            Tries to get the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> instance of currently
            registered recipients for the input <typeparamref name="TMessage"/> type.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <param name="mapping">The resulting <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> instance, if found.</param>
            <returns>Whether or not the required <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> instance was found.</returns>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.TryGetMapping``2(CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping{``1}@)">
            <summary>
            Tries to get the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> instance of currently registered recipients
            for the combination of types <typeparamref name="TMessage"/> and <typeparamref name="TToken"/>.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to use to send the message.</typeparam>
            <param name="mapping">The resulting <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> instance, if found.</param>
            <returns>Whether or not the required <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> instance was found.</returns>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.GetOrAddMapping``1">
            <summary>
            Gets the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> instance of currently
            registered recipients for the input <typeparamref name="TMessage"/> type.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <returns>A <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> instance with the requested type arguments.</returns>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.GetOrAddMapping``2">
            <summary>
            Gets the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> instance of currently registered recipients
            for the combination of types <typeparamref name="TMessage"/> and <typeparamref name="TToken"/>.
            </summary>
            <typeparam name="TMessage">The type of message to send.</typeparam>
            <typeparam name="TToken">The type of token to identify what channel to use to send the message.</typeparam>
            <returns>A <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> instance with the requested type arguments.</returns>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping">
            <summary>
            A mapping type representing a link to recipients and their view of handlers per communication channel.
            </summary>
            <remarks>
            This type is a specialization of <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> for <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.Unit"/> tokens.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> class.
            </summary>
            <param name="messageType">The message type being used.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping.Create``1">
            <summary>
            Creates a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> class.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <returns>A new <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> instance.</returns>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping.TypeArguments">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1">
            <summary>
            A mapping type representing a link to recipients and their view of handlers per communication channel.
            </summary>
            <typeparam name="TToken">The type of token to use to pick the messages to receive.</typeparam>
            <remarks>
            This type is defined for simplicity and as a workaround for the lack of support for using type aliases
            over open generic types in C# (using type aliases can only be used for concrete, closed types).
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> class.
            </summary>
            <param name="messageType">The message type being used.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1.Create``1">
            <summary>
            Creates a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> class.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <returns>A new <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> instance.</returns>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1.TypeArguments">
            <inheritdoc/>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.IMapping">
            <summary>
            An interface for the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping"/> and <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Mapping`1"/> types which allows to retrieve
            the type arguments from a given generic instance without having any prior knowledge about those arguments.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.IMapping.TypeArguments">
            <summary>
            Gets the <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.Type2"/> instance representing the current type arguments.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient">
            <summary>
            A simple type representing a recipient.
            </summary>
            <remarks>
            This type is used to enable fast indexing in each mapping dictionary,
            since it acts as an external override for the <see cref="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient.GetHashCode"/> and
            <see cref="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient.Equals(System.Object)"/> methods for arbitrary objects, removing both
            the virtual call and preventing instances overriding those methods in this context.
            Using this type guarantees that all the equality operations are always only done
            based on reference equality for each registered recipient, regardless of its type.
            </remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient.Target">
            <summary>
            The registered recipient.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient"/> struct.
            </summary>
            <param name="target">The target recipient instance.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient.Equals(CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.Recipient.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.StrongReferenceMessenger.ThrowInvalidOperationExceptionForDuplicateRegistration">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when trying to add a duplicate handler.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger">
            <summary>
            A class providing a reference implementation for the <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> interface.
            </summary>
            <remarks>
            <para>
            This <see cref="T:CommunityToolkit.Mvvm.Messaging.IMessenger"/> implementation uses weak references to track the registered
            recipients, so it is not necessary to manually unregister them when they're no longer needed.
            </para>
            <para>
            The <see cref="T:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger"/> type will automatically perform internal trimming when
            full GC collections are invoked, so calling <see cref="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Cleanup"/> manually is not necessary to
            ensure that on average the internal data structures are as trimmed and compact as possible.
            </para>
            </remarks>
        </member>
        <member name="F:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.recipientsMap">
            <summary>
            The map of currently registered recipients for all message types.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger"/> class.
            </summary>
        </member>
        <member name="P:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Default">
            <summary>
            Gets the default <see cref="T:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger"/> instance.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.IsRegistered``2(System.Object,``1)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Register``3(``0,``2,CommunityToolkit.Mvvm.Messaging.MessageHandler{``0,``1})">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Register``2(CommunityToolkit.Mvvm.Messaging.IRecipient{``0},``1)">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <typeparam name="TToken">The type of token to use to pick the messages to receive.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
            <remarks>
            This method is a variation of <see cref="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Register``3(``0,``2,CommunityToolkit.Mvvm.Messaging.MessageHandler{``0,``1})"/>
            that is specialized for recipients implementing <see cref="T:CommunityToolkit.Mvvm.Messaging.IRecipient`1"/>. See more comments at the top of this type, as well as
            within <see cref="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Send``2(``0,``1)"/> and in the <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher"/> types.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Register``2(System.Object,``1,CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher)">
            <summary>
            Registers a recipient for a given type of message.
            </summary>
            <typeparam name="TMessage">The type of message to receive.</typeparam>
            <typeparam name="TToken">The type of token to use to pick the messages to receive.</typeparam>
            <param name="recipient">The recipient that will receive the messages.</param>
            <param name="token">A token used to determine the receiving channel to use.</param>
            <param name="dispatcher">The input <see cref="T:CommunityToolkit.Mvvm.Messaging.Internals.MessageHandlerDispatcher"/> instance to register, or null.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when trying to register the same message twice.</exception>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.UnregisterAll(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.UnregisterAll``1(System.Object,``0)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Unregister``2(System.Object,``1)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Send``2(``0,``1)">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.SendAll``1(System.ReadOnlySpan{System.Object},System.Int32,``0)">
            <summary>
            Implements the broadcasting logic for <see cref="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Send``2(``0,``1)"/>.
            </summary>
            <typeparam name="TMessage"></typeparam>
            <param name="pairs"></param>
            <param name="i"></param>
            <param name="message"></param>
            <remarks>
            This method is not a local function to avoid triggering multiple compilations due to <c>TToken</c>
            potentially being a value type, which results in specialized code due to reified generics. This is
            necessary to work around a Roslyn limitation that causes unnecessary type parameters in local
            functions not to be discarded in the synthesized methods. Additionally, keeping this loop outside
            of the EH block (the <see langword="try"/> block) can help result in slightly better codegen.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Cleanup">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.Reset">
            <inheritdoc/>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.CleanupWithNonBlockingLock">
            <summary>
            Executes a cleanup without locking the current instance. This method has to be
            invoked when a lock on <see cref="F:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.recipientsMap"/> has already been acquired.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.CleanupWithoutLock">
            <summary>
            Executes a cleanup without locking the current instance. This method has to be
            invoked when a lock on <see cref="F:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.recipientsMap"/> has already been acquired.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger.ThrowInvalidOperationExceptionForDuplicateRegistration">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> when trying to add a duplicate handler.
            </summary>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ArgumentNullException">
            <summary>
            Internal polyfill for <see cref="T:System.ArgumentNullException"/>.
            </summary>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ArgumentNullException.ThrowIfNull(System.Object,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is <see langword="null"/>.
            </summary>
            <param name="argument">The reference type argument to validate as non-<see langword="null"/>.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="T:CommunityToolkit.Mvvm.ArgumentNullException.For`1">
            <summary>
            A specialized version for generic values.
            </summary>
            <typeparam name="T">The type of values to check.</typeparam>
            <remarks>
            This type is needed because if there had been a generic overload with a generic parameter, all calls
            would have just been bound by that by the compiler instead of the <see cref="T:System.Object"/> overload.
            </remarks>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ArgumentNullException.For`1.ThrowIfNull(`0,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is <see langword="null"/>.
            </summary>
            <param name="argument">The reference type argument to validate as non-<see langword="null"/>.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:CommunityToolkit.Mvvm.ArgumentNullException.Throw(System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentNullException"/>.
            </summary>
            <param name="paramName">The name of the parameter that failed validation.</param>
        </member>
        <member name="T:System.Collections.Generic.Dictionary2`2">
            <summary>
            A specialized <see cref="T:System.Collections.Generic.Dictionary`2"/> implementation to be used with messenger types.
            </summary>
            <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
            <typeparam name="TValue">The type of values in the dictionary.</typeparam>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.StartOfFreeList">
            <summary>
            The index indicating the start of a free linked list.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.buckets">
            <summary>
            The array of 1-based indices for the <see cref="T:System.Collections.Generic.Dictionary2`2.Entry"/> items stored in <see cref="F:System.Collections.Generic.Dictionary2`2.entries"/>.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.entries">
            <summary>
            The array of currently stored key-value pairs (ie. the lists for each hash group).
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.fastModMultiplier">
            <summary>
            A coefficient used to speed up retrieving the target bucket when doing lookups.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.count">
            <summary>
            The current number of items stored in the map.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.freeList">
            <summary>
            The 1-based index for the start of the free list within <see cref="F:System.Collections.Generic.Dictionary2`2.entries"/>.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.freeCount">
            <summary>
            The total number of empty items.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Collections.Generic.Dictionary2`2"/> class.
            </summary>
        </member>
        <member name="P:System.Collections.Generic.Dictionary2`2.Count">
            <inheritdoc/>
        </member>
        <member name="P:System.Collections.Generic.Dictionary2`2.Item(`0)">
            <inheritdoc/>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.Clear">
            <inheritdoc/>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.ContainsKey(`0)">
            <summary>
            Checks whether or not the dictionary contains a pair with a specified key.
            </summary>
            <param name="key">The key to look for.</param>
            <returns>Whether or not the key was present in the dictionary.</returns>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.TryGetValue(`0,`1@)">
            <summary>
            Gets the value if present for the specified key.
            </summary>
            <param name="key">The key to look for.</param>
            <param name="value">The value found, otherwise <see langword="default"/>.</param>
            <returns>Whether or not the key was present.</returns>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.TryRemove(`0)">
            <inheritdoc/>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.GetOrAddValueRef(`0)">
            <summary>
            Gets the value for the specified key, or, if the key is not present,
            adds an entry and returns the value by ref. This makes it possible to
            add or update a value in a single look up operation.
            </summary>
            <param name="key">Key to look for.</param>
            <returns>Reference to the new or existing value.</returns>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.GetEnumerator">
            <inheritdoc cref="M:System.Collections.Generic.IEnumerable`1.GetEnumerator"/>
        </member>
        <member name="T:System.Collections.Generic.Dictionary2`2.Enumerator">
            <summary>
            Enumerator for <see cref="T:System.Collections.Generic.Dictionary2`2"/>.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.Enumerator.entries">
            <summary>
            The entries being enumerated.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.Enumerator.index">
            <summary>
            The current enumeration index.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.Enumerator.count">
            <summary>
            The current dictionary count.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.Enumerator.#ctor(System.Collections.Generic.Dictionary2{`0,`1})">
            <summary>
            Creates a new <see cref="T:System.Collections.Generic.Dictionary2`2.Enumerator"/> instance.
            </summary>
            <param name="dictionary">The input dictionary to enumerate.</param>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.Enumerator.MoveNext">
            <inheritdoc cref="M:System.Collections.IEnumerator.MoveNext"/>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.Enumerator.GetKey">
            <summary>
            Gets the current key.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.Enumerator.GetValue">
            <summary>
            Gets the current value.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.FindValue(`0)">
            <summary>
            Gets the value for the specified key, or.
            </summary>
            <param name="key">Key to look for.</param>
            <returns>Reference to the existing value.</returns>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.Initialize(System.Int32)">
            <summary>
            Initializes the current instance.
            </summary>
            <param name="capacity">The target capacity.</param>
            <returns></returns>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.Resize">
            <summary>
            Resizes the current dictionary to reduce the number of collisions
            </summary>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.GetBucket(System.UInt32)">
            <summary>
            Gets a reference to a target bucket from an input hashcode.
            </summary>
            <param name="hashCode">The input hashcode.</param>
            <returns>A reference to the target bucket.</returns>
        </member>
        <member name="T:System.Collections.Generic.Dictionary2`2.Entry">
            <summary>
            A type representing a map entry, ie. a node in a given list.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.Entry.HashCode">
            <summary>
            The cached hashcode for <see cref="F:System.Collections.Generic.Dictionary2`2.Entry.Key"/>;
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.Entry.Next">
            <summary>
            0-based index of next entry in chain: -1 means end of chain
            also encodes whether this entry this.itself_ is part of the free list by changing sign and subtracting 3,
            so -2 means end of free list, -3 means index 0 but on free list, -4 means index 1 but on free list, etc.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.Entry.Key">
            <summary>
            The key for the value in the current node.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.Dictionary2`2.Entry.Value">
            <summary>
            The value in the current node, if present.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.Dictionary2`2.ThrowArgumentExceptionForKeyNotFound(`0)">
            <summary>
            Throws an <see cref="T:System.ArgumentException"/> when trying to load an element with a missing key.
            </summary>
        </member>
        <member name="T:System.Collections.Generic.HashHelpers">
            <summary>
            A helper class for <see cref="T:System.Collections.Generic.Dictionary2`2"/>.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.HashHelpers.MaxPrimeArrayLength">
            <summary>
            Maximum prime smaller than the maximum array length.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.HashHelpers.HashPrime">
            <summary>
            An arbitrary prime factor used in <see cref="M:System.Collections.Generic.HashHelpers.GetPrime(System.Int32)"/>.
            </summary>
        </member>
        <member name="F:System.Collections.Generic.HashHelpers.primes">
            <summary>
            Table of prime numbers to use as hash table sizes.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.HashHelpers.IsPrime(System.Int32)">
            <summary>
            Checks whether a value is a prime.
            </summary>
            <param name="candidate">The value to check.</param>
            <returns>Whether or not <paramref name="candidate"/> is a prime.</returns>
        </member>
        <member name="M:System.Collections.Generic.HashHelpers.GetPrime(System.Int32)">
            <summary>
            Gets the smallest prime bigger than a specified value.
            </summary>
            <param name="min">The target minimum value.</param>
            <returns>The new prime that was found.</returns>
        </member>
        <member name="M:System.Collections.Generic.HashHelpers.ExpandPrime(System.Int32)">
            <summary>
            Returns size of hashtable to grow to.
            </summary>
            <param name="oldSize">The previous table size.</param>
            <returns>The expanded table size.</returns>
        </member>
        <member name="M:System.Collections.Generic.HashHelpers.GetFastModMultiplier(System.UInt32)">
            <summary>
            Returns approximate reciprocal of the divisor: ceil(2**64 / divisor).
            </summary>
            <remarks>This should only be used on 64-bit.</remarks>
        </member>
        <member name="M:System.Collections.Generic.HashHelpers.FastMod(System.UInt32,System.UInt32,System.UInt64)">
            <summary>
            Performs a mod operation using the multiplier pre-computed with <see cref="M:System.Collections.Generic.HashHelpers.GetFastModMultiplier(System.UInt32)"/>.
            </summary>
            <remarks>This should only be used on 64-bit.</remarks>
        </member>
        <member name="T:System.Collections.Generic.IDictionary2">
            <summary>
            A base interface masking <see cref="T:System.Collections.Generic.Dictionary2`2"/> instances and exposing non-generic functionalities.
            </summary>
        </member>
        <member name="P:System.Collections.Generic.IDictionary2.Count">
            <summary>
            Gets the count of entries in the dictionary.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.IDictionary2.Clear">
            <summary>
            Clears the current dictionary.
            </summary>
        </member>
        <member name="T:System.Collections.Generic.IDictionary2`2">
            <summary>
            An interface providing key type contravariant and value type covariant access
            to a <see cref="T:System.Collections.Generic.Dictionary2`2"/> instance.
            </summary>
            <typeparam name="TKey">The contravariant type of keys in the dictionary.</typeparam>
            <typeparam name="TValue">The covariant type of values in the dictionary.</typeparam>
        </member>
        <member name="P:System.Collections.Generic.IDictionary2`2.Item(`0)">
            <summary>
            Gets the value with the specified key.
            </summary>
            <param name="key">The key to look for.</param>
            <returns>The returned value.</returns>
            <exception cref="T:System.ArgumentException">Thrown if the key wasn't present.</exception>
        </member>
        <member name="T:System.Collections.Generic.IDictionary2`1">
            <summary>
            An interface providing key type contravariant access to a <see cref="T:System.Collections.Generic.Dictionary2`2"/> instance.
            </summary>
            <typeparam name="TKey">The contravariant type of keys in the dictionary.</typeparam>
        </member>
        <member name="M:System.Collections.Generic.IDictionary2`1.TryRemove(`0)">
            <summary>
            Tries to remove a value with a specified key, if present.
            </summary>
            <param name="key">The key of the value to remove.</param>
            <returns>Whether or not the key was present.</returns>
        </member>
        <member name="T:System.Gen2GcCallback">
            <summary>
            Schedules a callback roughly every gen 2 GC (you may see a Gen 0 an Gen 1 but only once).
            Ported from https://github.com/dotnet/runtime/blob/main/src/libraries/System.Private.CoreLib/src/System/Gen2GcCallback.cs.
            </summary>
        </member>
        <member name="F:System.Gen2GcCallback.callback">
            <summary>
            The callback to invoke at each GC.
            </summary>
        </member>
        <member name="F:System.Gen2GcCallback.handle">
            <summary>
            A weak <see cref="T:System.Runtime.InteropServices.GCHandle"/> to the target object to pass to <see cref="F:System.Gen2GcCallback.callback"/>.
            </summary>
        </member>
        <member name="M:System.Gen2GcCallback.#ctor(System.Action{System.Object},System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Gen2GcCallback"/> class.
            </summary>
            <param name="callback">The callback to invoke at each GC.</param>
            <param name="target">The target object to pass as argument to <paramref name="callback"/>.</param>
        </member>
        <member name="M:System.Gen2GcCallback.Register(System.Action{System.Object},System.Object)">
            <summary>
            Schedules a callback to be called on each GC until the target is collected.
            </summary>
            <param name="callback">The callback to invoke at each GC.</param>
            <param name="target">The target object to pass as argument to <paramref name="callback"/>.</param>
        </member>
        <member name="M:System.Gen2GcCallback.Finalize">
            <summary>
            Finalizes an instance of the <see cref="T:System.Gen2GcCallback"/> class.
            This finalizer is re-registered with <see cref="M:System.GC.ReRegisterForFinalize(System.Object)"/> as long as
            the target object is alive, which means it will be executed again every time a generation 2
            collection is triggered (as the <see cref="T:System.Gen2GcCallback"/> instance itself would be moved to
            that generation after surviving the generation 0 and 1 collections the first time).
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2">
            <summary>
            A custom <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable`2"/> instance that is specifically optimized to be used
            by <see cref="T:CommunityToolkit.Mvvm.Messaging.WeakReferenceMessenger"/>. In particular, it offers zero-allocation enumeration of stored items.
            </summary>
            <typeparam name="TKey">Tke key of items to store in the table.</typeparam>
            <typeparam name="TValue">The values to store in the table.</typeparam>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.InitialCapacity">
            <summary>
            Initial length of the table. Must be a power of two.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.lockObject">
            <summary>
            This lock protects all mutation of data in the table. Readers do not take this lock.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.container">
            <summary>
            The actual storage for the table; swapped out as the table grows.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2"/> class.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.TryGetValue(`0,`1@)">
            <inheritdoc cref="M:System.Runtime.CompilerServices.ConditionalWeakTable`2.TryGetValue(`0,`1@)"/>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.TryAdd(`0,`1)">
            <summary>
            Tries to add a new pair to the table.
            </summary>
            <param name="key">The key to add.</param>
            <param name="value">The value to associate with key.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Remove(`0)">
            <inheritdoc cref="M:System.Runtime.CompilerServices.ConditionalWeakTable`2.Remove(`0)"/>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.GetValue(`0,System.Runtime.CompilerServices.ConditionalWeakTable{`0,`1}.CreateValueCallback)">
            <inheritdoc cref="M:System.Runtime.CompilerServices.ConditionalWeakTable`2.GetValue(`0,System.Runtime.CompilerServices.ConditionalWeakTable{`0,`1}.CreateValueCallback)"/>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.GetValueLocked(`0,System.Runtime.CompilerServices.ConditionalWeakTable{`0,`1}.CreateValueCallback)">
            <summary>
            Implements the functionality for <see cref="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.GetValue(`0,System.Runtime.CompilerServices.ConditionalWeakTable{`0,`1}.CreateValueCallback)"/> under a lock.
            </summary>
            <param name="key">The input key.</param>
            <param name="createValueCallback">The callback to use to create a new item.</param>
            <returns>The new <typeparamref name="TValue"/> item to store.</returns>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator">
            <summary>
            Provides an enumerator for the current <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2"/> instance.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.table">
            <summary>
            Parent table, set to null when disposed.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.maxIndexInclusive">
            <summary>
            Last index in the container that should be enumerated.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.currentIndex">
            <summary>
            The current index into the container.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.key">
            <summary>
            The current key, if available.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.value">
            <summary>
            The current value, if available.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.#ctor(System.Runtime.CompilerServices.ConditionalWeakTable2{`0,`1})">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator"/> class.
            </summary>
            <param name="table">The input <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2"/> instance being enumerated.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.Dispose">
            <inheritdoc cref="M:System.IDisposable.Dispose"/>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.MoveNext">
            <inheritdoc cref="M:System.Collections.IEnumerator.MoveNext"/>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.GetKey">
            <summary>
            Gets the current key.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Enumerator.GetValue">
            <summary>
            Gets the current value.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.CreateEntry(`0,`1)">
            <summary>
            Worker for adding a new key/value pair. Will resize the container if it is full.
            </summary>
            <param name="key">The key for the new entry.</param>
            <param name="value">The value for the new entry.</param>
        </member>
        <member name="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Entry">
            <summary>
            A single entry within a <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2"/> instance.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Entry.depHnd">
            <summary>
            Holds key and value using a weak reference for the key and a strong reference for the
            value that is traversed only if the key is reachable without going through the value.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Entry.HashCode">
            <summary>
            Cached copy of key's hashcode.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Entry.Next">
            <summary>
            Index of next entry, -1 if last.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container">
            <summary>
            Container holds the actual data for the table. A given instance of Container always has the same capacity. When we need
            more capacity, we create a new Container, copy the old one into the new one, and discard the old one. This helps enable
            lock-free reads from the table, as readers never need to deal with motion of entries due to rehashing.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.parent">
            <summary>
            The <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2"/> with which this container is associated.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.buckets">
            <summary>
            <c>buckets[hashcode &amp; (buckets.Length - 1)]</c> contains index of the first entry in bucket (-1 if empty).
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.entries">
            <summary>
            The table entries containing the stored dependency handles
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.firstFreeEntry">
            <summary>
            <c>firstFreeEntry &lt; entries.Length => table</c> has capacity, entries grow from the bottom of the table.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.invalid">
            <summary>
            Flag detects if OOM or other background exception threw us out of the lock.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.finalized">
            <summary>
            Set to true when initially finalized
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.oldKeepAlive">
            <summary>
            Used to ensure the next allocated container isn't finalized until this one is GC'd.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.#ctor(System.Runtime.CompilerServices.ConditionalWeakTable2{`0,`1})">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container"/> class.
            </summary>
            <param name="parent">The input <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2"/> object associated with the current instance.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.#ctor(System.Runtime.CompilerServices.ConditionalWeakTable2{`0,`1},System.Int32[],System.Runtime.CompilerServices.ConditionalWeakTable2{`0,`1}.Entry[],System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container"/> class.
            </summary>
            <param name="parent">The input <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2"/> object associated with the current instance.</param>
            <param name="buckets">The array of buckets.</param>
            <param name="entries">The array of entries.</param>
            <param name="firstFreeEntry">The index of the first free entry.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.HasCapacity">
            <summary>
            Gets the capacity of the current container.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.FirstFreeEntry">
            <summary>
            Gets the index of the first free entry.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.CreateEntryNoResize(`0,`1)">
            <summary>
            Worker for adding a new key/value pair. Container must NOT be full.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.TryGetValueWorker(`0,`1@)">
            <summary>
            Worker for finding a key/value pair. Must hold lock.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.FindEntry(`0,System.Object@)">
            <summary>
            Returns -1 if not found (if key expires during FindEntry, this can be treated as "not found.").
            Must hold lock, or be prepared to retry the search while holding lock.
            </summary>
            <remarks>This method requires <paramref name="value"/> to be on the stack to be properly tracked.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.TryGetEntry(System.Int32,`0@,`1@)">
            <summary>
            Gets the entry at the specified entry index.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.Remove(`0)">
            <summary>
            Removes the specified key from the table, if it exists.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.RemoveIndex(System.Int32)">
            <summary>
            Removes a given entry at a specified index.
            </summary>
            <param name="entryIndex">The index of the entry to remove.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.Resize">
            <summary>
            Resize, and scrub expired keys off bucket lists. Must hold <see cref="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.lockObject"/>.
            </summary>
            <remarks>
            <see cref="F:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.firstFreeEntry"/> is less than <c>entries.Length</c> on exit, that is, the table has at least one free entry.
            </remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.Resize(System.Int32)">
            <summary>
            Creates a new <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container"/> of a specified size with the current items.
            </summary>
            <param name="newSize">The new requested size.</param>
            <returns>The new <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container"/> instance with the requested size.</returns>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.VerifyIntegrity">
            <summary>
            Verifies that the current instance is valid.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown if the current instance is invalid.</exception>
        </member>
        <member name="M:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container.Finalize">
            <summary>
            Finalizes the current <see cref="T:System.Runtime.CompilerServices.ConditionalWeakTable2`2.Container"/> instance.
            </summary>
        </member>
        <member name="T:Configuration">
            <summary>
            A container for all shared <see cref="T:System.AppContext"/> configuration switches for the MVVM Toolkit.
            </summary>
        </member>
        <member name="F:Configuration.DisableINotifyPropertyChangingSupport">
            <summary>
            The configuration property name for <see cref="F:Configuration.IsINotifyPropertyChangingDisabled"/>.
            </summary>
        </member>
        <member name="F:Configuration.IsINotifyPropertyChangingDisabled">
            <summary>
            Indicates whether or not support for <see cref="T:System.ComponentModel.INotifyPropertyChanging"/> is disabled.
            </summary>
        </member>
        <member name="M:Configuration.GetConfigurationValue(System.String)">
            <summary>
            Gets a configuration value for a specified property.
            </summary>
            <param name="propertyName">The property name to retrieve the value for.</param>
            <returns>The value of the specified configuration setting.</returns>
        </member>
    </members>
</doc>
