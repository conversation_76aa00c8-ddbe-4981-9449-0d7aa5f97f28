﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9F9CE548-5F95-482F-B2CB-6DFB2B458DD9}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MoonLight</RootNamespace>
    <AssemblyName>MoonLight</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AvalonDock, Version=4.60.0.0, Culture=neutral, PublicKeyToken=3e4669d2f30244f4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\dlls\AvalonDock.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ControlzEx">
      <HintPath>..\..\dlls\ControlzEx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DotNetProjects.Wpf.Extended.Toolkit, Version=4.6.78.0, Culture=neutral, PublicKeyToken=3e4669d2f30244f4, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\dlls\DotNetProjects.Wpf.Extended.Toolkit.dll</HintPath>
    </Reference>
    <Reference Include="MahApps.Metro">
      <HintPath>..\..\dlls\UI\MahApps.Metro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Xaml.Behaviors">
      <HintPath>..\..\dlls\Common\Microsoft.Xaml.Behaviors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PresentationCore">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\PresentationCore.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\PresentationFramework.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework.Aero" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\System.Xaml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.2\WindowsBase.dll</HintPath>
    </Reference>
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Platforms\ActivateExtensions.cs" />
    <Compile Include="Platforms\ActivationEventArgs.cs" />
    <Compile Include="Platforms\ActivationProcessedEventArgs.cs" />
    <Compile Include="Platforms\AsyncEventHandler.cs" />
    <Compile Include="Platforms\AsyncEventHandlerExtensions.cs" />
    <Compile Include="Platforms\BindableCollection.cs" />
    <Compile Include="Platforms\CloseResult.cs" />
    <Compile Include="Platforms\Conductor.cs" />
    <Compile Include="Platforms\ConductorBase.cs" />
    <Compile Include="Platforms\ConductorBaseWithActiveItem.cs" />
    <Compile Include="Platforms\ConductorExtensions.cs" />
    <Compile Include="Platforms\ConductorWithCollectionAllActive.cs" />
    <Compile Include="Platforms\ConductorWithCollectionOneActive.cs" />
    <Compile Include="Platforms\ContainerExtensions.cs" />
    <Compile Include="Platforms\ContinueResultDecorator.cs" />
    <Compile Include="Platforms\Coroutine.cs" />
    <Compile Include="Platforms\CoroutineExecutionContext.cs" />
    <Compile Include="Platforms\DeactivateExtensions.cs" />
    <Compile Include="Platforms\DeactivationEventArgs.cs" />
    <Compile Include="Platforms\DebugLog.cs" />
    <Compile Include="Platforms\DefaultCloseStrategy.cs" />
    <Compile Include="Platforms\DefaultPlatformProvider.cs" />
    <Compile Include="Platforms\DelegateResult.cs" />
    <Compile Include="Platforms\EnumerableExtensions.cs" />
    <Compile Include="Platforms\EventAggregator.cs" />
    <Compile Include="Platforms\EventAggregatorExtensions.cs" />
    <Compile Include="Platforms\Execute.cs" />
    <Compile Include="Platforms\ExpressionExtensions.cs" />
    <Compile Include="Platforms\IActivate.cs" />
    <Compile Include="Platforms\IChild.cs" />
    <Compile Include="Platforms\IClose.cs" />
    <Compile Include="Platforms\ICloseResult.cs" />
    <Compile Include="Platforms\ICloseStrategy.cs" />
    <Compile Include="Platforms\IConductor.cs" />
    <Compile Include="Platforms\IDeactivate.cs" />
    <Compile Include="Platforms\IEventAggregator.cs" />
    <Compile Include="Platforms\IGuardClose.cs" />
    <Compile Include="Platforms\IHandle.cs" />
    <Compile Include="Platforms\IHaveActiveItem.cs" />
    <Compile Include="Platforms\IHaveDisplayName.cs" />
    <Compile Include="Platforms\ILog.cs" />
    <Compile Include="Platforms\INotifyPropertyChangedEx.cs" />
    <Compile Include="Platforms\IObservableCollection.cs" />
    <Compile Include="Platforms\IoC.cs" />
    <Compile Include="Platforms\IParent.cs" />
    <Compile Include="Platforms\IPlatformProvider.cs" />
    <Compile Include="Platforms\IResult.cs" />
    <Compile Include="Platforms\IScreen.cs" />
    <Compile Include="Platforms\IViewAware.cs" />
    <Compile Include="Platforms\LogManager.cs" />
    <Compile Include="Platforms\OverrideCancelResultDecorator.cs" />
    <Compile Include="Platforms\PlatformProvider.cs" />
    <Compile Include="Platforms\Action.cs" />
    <Compile Include="Platforms\ActionExecutionContext.cs" />
    <Compile Include="Platforms\ActionMessage.cs" />
    <Compile Include="Platforms\AssemblySource.cs" />
    <Compile Include="Platforms\AttachedCollection.cs" />
    <Compile Include="Platforms\Bind.cs" />
    <Compile Include="Platforms\BindingScope.cs" />
    <Compile Include="Platforms\BooleanToVisibilityConverter.cs" />
    <Compile Include="Platforms\Bootstrapper.cs" />
    <Compile Include="Platforms\ChildResolver.cs" />
    <Compile Include="Platforms\ConventionManager.cs" />
    <Compile Include="Platforms\DependencyPropertyHelper.cs" />
    <Compile Include="Platforms\ElementConvention.cs" />
    <Compile Include="Platforms\ExtensionMethods.cs" />
    <Compile Include="Platforms\FrameAdapter.cs" />
    <Compile Include="Platforms\IHaveParameters.cs" />
    <Compile Include="Platforms\INavigationService.cs" />
    <Compile Include="Platforms\Message.cs" />
    <Compile Include="Platforms\MessageBinder.cs" />
    <Compile Include="Platforms\NameTransformer.cs" />
    <Compile Include="Platforms\NavigationExtensions.cs" />
    <Compile Include="Platforms\NavigationHelper.cs" />
    <Compile Include="Platforms\Parameter.cs" />
    <Compile Include="Platforms\Parser.cs" />
    <Compile Include="Platforms\RegExHelper.cs" />
    <Compile Include="Platforms\StringSplitter.cs" />
    <Compile Include="Platforms\TypeMappingConfiguration.cs" />
    <Compile Include="Platforms\View.cs" />
    <Compile Include="Platforms\ViewLocator.cs" />
    <Compile Include="Platforms\ViewModelBinder.cs" />
    <Compile Include="Platforms\ViewModelLocator.cs" />
    <Compile Include="Platforms\WindowManager.cs" />
    <Compile Include="Platforms\XamlPlatformProvider.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Platforms\PropertyChangedBase.cs" />
    <Compile Include="Platforms\RescueResultDecorator.cs" />
    <Compile Include="Platforms\ResultCompletionEventArgs.cs" />
    <Compile Include="Platforms\ResultDecoratorBase.cs" />
    <Compile Include="Platforms\ResultExtensions.cs" />
    <Compile Include="Platforms\Screen.cs" />
    <Compile Include="Platforms\ScreenExtensions.cs" />
    <Compile Include="Platforms\SequentialResult.cs" />
    <Compile Include="Platforms\SimpleContainer.cs" />
    <Compile Include="Platforms\SimpleResult.cs" />
    <Compile Include="Platforms\TaskExtensions.cs" />
    <Compile Include="Platforms\TaskResult.cs" />
    <Compile Include="Platforms\ViewAttachedEventArgs.cs" />
    <Compile Include="Platforms\ViewAware.cs" />
    <Compile Include="Platforms\WeakValueDictionary.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources.zh-Hans.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.zh-Hans.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="UI\AppBootstrapper.cs" />
    <Compile Include="UI\Framework\AsyncCommand.cs" />
    <Compile Include="UI\Framework\Behaviors\BindableTreeViewSelectedItemBehavior.cs" />
    <Compile Include="UI\Framework\Behaviors\KeyboardFocusBehavior.cs" />
    <Compile Include="UI\Framework\Behaviors\WindowOptionsBehavior.cs" />
    <Compile Include="UI\Framework\Commands\Command.cs" />
    <Compile Include="UI\Framework\Commands\CommandDefinition.cs" />
    <Compile Include="UI\Framework\Commands\CommandDefinitionAttribute.cs" />
    <Compile Include="UI\Framework\Commands\CommandDefinitionBase.cs" />
    <Compile Include="UI\Framework\Commands\CommandHandlerAttribute.cs" />
    <Compile Include="UI\Framework\Commands\CommandHandlerWrapper.cs" />
    <Compile Include="UI\Framework\Commands\CommandKeyboardShortcut.cs" />
    <Compile Include="UI\Framework\Commands\CommandKeyGestureService.cs" />
    <Compile Include="UI\Framework\Commands\CommandListDefinition.cs" />
    <Compile Include="UI\Framework\Commands\CommandRouter.cs" />
    <Compile Include="UI\Framework\Commands\CommandService.cs" />
    <Compile Include="UI\Framework\Commands\ExcludeCommandKeyboardShortcut.cs" />
    <Compile Include="UI\Framework\Commands\ICommandHandler.cs" />
    <Compile Include="UI\Framework\Commands\ICommandKeyGestureService.cs" />
    <Compile Include="UI\Framework\Commands\ICommandRerouter.cs" />
    <Compile Include="UI\Framework\Commands\ICommandRouter.cs" />
    <Compile Include="UI\Framework\Commands\ICommandService.cs" />
    <Compile Include="UI\Framework\Commands\ICommandUiItem.cs" />
    <Compile Include="UI\Framework\Commands\TargetableCommand.cs" />
    <Compile Include="UI\Framework\Controls\ClippingHwndHost.cs" />
    <Compile Include="UI\Framework\Controls\DynamicStyle.cs" />
    <Compile Include="UI\Framework\Controls\ExpanderEx.cs" />
    <Compile Include="UI\Framework\Controls\HwndMouse.cs" />
    <Compile Include="UI\Framework\Controls\HwndMouseEventArgs.cs" />
    <Compile Include="UI\Framework\Controls\HwndMouseState.cs" />
    <Compile Include="UI\Framework\Controls\HwndWrapper.cs" />
    <Compile Include="UI\Framework\Controls\SliderEx.cs" />
    <Compile Include="UI\Framework\Document.cs" />
    <Compile Include="UI\Framework\IDocument.cs" />
    <Compile Include="UI\Framework\ILayoutItem.cs" />
    <Compile Include="UI\Framework\IModule.cs" />
    <Compile Include="UI\Framework\IPersistedDocument.cs" />
    <Compile Include="UI\Framework\ITool.cs" />
    <Compile Include="UI\Framework\IWindow.cs" />
    <Compile Include="UI\Framework\LayoutItemBase.cs" />
    <Compile Include="UI\Framework\Markup\TranslateExtension.cs" />
    <Compile Include="UI\Framework\Menus\CommandMenuItemDefinition.cs" />
    <Compile Include="UI\Framework\Menus\ExcludeMenuDefinition.cs" />
    <Compile Include="UI\Framework\Menus\ExcludeMenuItemDefinition.cs" />
    <Compile Include="UI\Framework\Menus\ExcludeMenuItemGroupDefinition.cs" />
    <Compile Include="UI\Framework\Menus\MenuBarDefinition.cs" />
    <Compile Include="UI\Framework\Menus\MenuDefinition.cs" />
    <Compile Include="UI\Framework\Menus\MenuDefinitionBase.cs" />
    <Compile Include="UI\Framework\Menus\MenuItemDefinition.cs" />
    <Compile Include="UI\Framework\Menus\MenuItemGroupDefinition.cs" />
    <Compile Include="UI\Framework\Menus\TextMenuItemDefinition.cs" />
    <Compile Include="UI\Framework\ModuleBase.cs" />
    <Compile Include="UI\Framework\PersistedDocument.cs" />
    <Compile Include="UI\Framework\RelayCommand.cs" />
    <Compile Include="UI\Framework\Results\IOpenResult.cs" />
    <Compile Include="UI\Framework\Results\LambdaResult.cs" />
    <Compile Include="UI\Framework\Results\OpenDocumentResult.cs" />
    <Compile Include="UI\Framework\Results\OpenResultBase.cs" />
    <Compile Include="UI\Framework\Results\Show.cs" />
    <Compile Include="UI\Framework\Results\ShowCommonDialogResult.cs" />
    <Compile Include="UI\Framework\Results\ShowDialogResult.cs" />
    <Compile Include="UI\Framework\Results\ShowToolResult.cs" />
    <Compile Include="UI\Framework\Results\ShowWindowResult.cs" />
    <Compile Include="UI\Framework\Services\EditorFileType.cs" />
    <Compile Include="UI\Framework\Services\ExtensionMethods.cs" />
    <Compile Include="UI\Framework\Services\IEditorProvider.cs" />
    <Compile Include="UI\Framework\Services\IInputManager.cs" />
    <Compile Include="UI\Framework\Services\IMainWindow.cs" />
    <Compile Include="UI\Framework\Services\InputBindingTrigger.cs" />
    <Compile Include="UI\Framework\Services\InputManager.cs" />
    <Compile Include="UI\Framework\Services\IResourceManager.cs" />
    <Compile Include="UI\Framework\Services\IShell.cs" />
    <Compile Include="UI\Framework\Services\PaneLocation.cs" />
    <Compile Include="UI\Framework\Services\ResourceManager.cs" />
    <Compile Include="UI\Framework\Services\ServiceProvider.cs" />
    <Compile Include="UI\Framework\Services\SettingsPropertyChangedEventManager.cs" />
    <Compile Include="UI\Framework\ShaderEffects\GrayscaleEffect.cs" />
    <Compile Include="UI\Framework\ShaderEffects\ShaderEffectBase.cs" />
    <Compile Include="UI\Framework\ShaderEffects\ShaderEffectUtility.cs" />
    <Compile Include="UI\Framework\Themes\BlueTheme.cs" />
    <Compile Include="UI\Framework\Themes\DarkTheme.cs" />
    <Compile Include="UI\Framework\Themes\ITheme.cs" />
    <Compile Include="UI\Framework\Themes\IThemeManager.cs" />
    <Compile Include="UI\Framework\Themes\LightTheme.cs" />
    <Compile Include="UI\Framework\Themes\ThemeManager.cs" />
    <Compile Include="UI\Framework\Threading\TaskUtility.cs" />
    <Compile Include="UI\Framework\Tool.cs" />
    <Compile Include="UI\Framework\ToolBars\CommandToolBarItemDefinition.cs" />
    <Compile Include="UI\Framework\ToolBars\ExcludeToolBarDefinition.cs" />
    <Compile Include="UI\Framework\ToolBars\ExcludeToolBarItemDefinition.cs" />
    <Compile Include="UI\Framework\ToolBars\ExcludeToolBarItemGroupDefinition.cs" />
    <Compile Include="UI\Framework\ToolBars\ToolBarDefinition.cs" />
    <Compile Include="UI\Framework\ToolBars\ToolBarItemDefinition.cs" />
    <Compile Include="UI\Framework\ToolBars\ToolBarItemGroupDefinition.cs" />
    <Compile Include="UI\Framework\Utils\ItemsControlUtility.cs" />
    <Compile Include="UI\Framework\VisualTreeUtility.cs" />
    <Compile Include="UI\Framework\Win32\NativeMethods.cs" />
    <Compile Include="UI\Framework\WindowBase.cs" />
    <Compile Include="UI\Modules\MainMenu\Behaviors\MenuBehavior.cs" />
    <Compile Include="UI\Modules\MainMenu\Controls\MenuEx.cs" />
    <Compile Include="UI\Modules\MainMenu\Controls\MenuItemEx.cs" />
    <Compile Include="UI\Modules\MainMenu\Converters\CultureInfoNameConverter.cs" />
    <Compile Include="UI\Modules\MainMenu\IMenu.cs" />
    <Compile Include="UI\Modules\MainMenu\IMenuBuilder.cs" />
    <Compile Include="UI\Modules\MainMenu\MenuBuilder.cs" />
    <Compile Include="UI\Modules\MainMenu\MenuDefinitions.cs" />
    <Compile Include="UI\Modules\MainMenu\Models\CommandMenuItem.cs" />
    <Compile Include="UI\Modules\MainMenu\Models\MenuItemBase.cs" />
    <Compile Include="UI\Modules\MainMenu\Models\MenuItemSeparator.cs" />
    <Compile Include="UI\Modules\MainMenu\Models\MenuModel.cs" />
    <Compile Include="UI\Modules\MainMenu\Models\StandardMenuItem.cs" />
    <Compile Include="UI\Modules\MainMenu\Models\TextMenuItem.cs" />
    <Compile Include="UI\Modules\MainMenu\ViewModels\MainMenuSettingsViewModel.cs" />
    <Compile Include="UI\Modules\MainMenu\ViewModels\MainMenuViewModel.cs" />
    <Compile Include="UI\Modules\MainMenu\Views\MainMenuSettingsView.xaml.cs">
      <DependentUpon>MainMenuSettingsView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\MainMenu\Views\MainMenuView.xaml.cs">
      <DependentUpon>MainMenuView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\MainWindow\ViewModels\MainWindowViewModel.cs" />
    <Compile Include="UI\Modules\MainWindow\Views\MainWindowView.xaml.cs">
      <DependentUpon>MainWindowView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\Settings\Commands\OpenSettingsCommandDefinition.cs" />
    <Compile Include="UI\Modules\Settings\Commands\OpenSettingsCommandHandler.cs" />
    <Compile Include="UI\Modules\Settings\ISettingsEditor.cs" />
    <Compile Include="UI\Modules\Settings\ISettingsEditorAsync.cs" />
    <Compile Include="UI\Modules\Settings\MenuDefinitions.cs" />
    <Compile Include="UI\Modules\Settings\ViewModels\SettingsEditorWrapper.cs" />
    <Compile Include="UI\Modules\Settings\ViewModels\SettingsPageViewModel.cs" />
    <Compile Include="UI\Modules\Settings\ViewModels\SettingsViewModel.cs" />
    <Compile Include="UI\Modules\Settings\Views\SettingsView.xaml.cs">
      <DependentUpon>SettingsView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\Shell\Commands\CloseFileCommandDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\CloseFileCommandHandler.cs" />
    <Compile Include="UI\Modules\Shell\Commands\ExitCommandDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\ExitCommandHandler.cs" />
    <Compile Include="UI\Modules\Shell\Commands\NewFileCommandHandler.cs" />
    <Compile Include="UI\Modules\Shell\Commands\NewFileCommandListDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\OpenFileCommandDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\OpenFileCommandHandler.cs" />
    <Compile Include="UI\Modules\Shell\Commands\SaveAllFilesCommandDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\SaveAllFilesCommandHandler.cs" />
    <Compile Include="UI\Modules\Shell\Commands\SaveFileAsCommandDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\SaveFileCommandDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\SwitchToDocumentCommandListDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\SwitchToDocumentListCommandHandler.cs" />
    <Compile Include="UI\Modules\Shell\Commands\ViewFullscreenCommandDefinition.cs" />
    <Compile Include="UI\Modules\Shell\Commands\ViewFullscreenCommandHandler.cs" />
    <Compile Include="UI\Modules\Shell\Controls\LayoutInitializer.cs" />
    <Compile Include="UI\Modules\Shell\Controls\PanesStyleSelector.cs" />
    <Compile Include="UI\Modules\Shell\Controls\PanesTemplateSelector.cs" />
    <Compile Include="UI\Modules\Shell\Converters\NullableValueConverter.cs" />
    <Compile Include="UI\Modules\Shell\Converters\TruncateMiddleConverter.cs" />
    <Compile Include="UI\Modules\Shell\Converters\UriToImageSourceConverter.cs" />
    <Compile Include="UI\Modules\Shell\MenuDefinitions.cs" />
    <Compile Include="UI\Modules\Shell\Services\ILayoutItemStatePersister.cs" />
    <Compile Include="UI\Modules\Shell\Services\LayoutItemStatePersister.cs" />
    <Compile Include="UI\Modules\Shell\ToolBarDefinitions.cs" />
    <Compile Include="UI\Modules\Shell\ViewModels\ShellViewModel.cs" />
    <Compile Include="UI\Modules\Shell\Views\IShellView.cs" />
    <Compile Include="UI\Modules\Shell\Views\LayoutUtility.cs" />
    <Compile Include="UI\Modules\Shell\Views\ShellView.xaml.cs">
      <DependentUpon>ShellView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\StatusBar\IStatusBar.cs" />
    <Compile Include="UI\Modules\StatusBar\IStatusBarView.cs" />
    <Compile Include="UI\Modules\StatusBar\ViewModels\StatusBarItemViewModel.cs" />
    <Compile Include="UI\Modules\StatusBar\ViewModels\StatusBarViewModel.cs" />
    <Compile Include="UI\Modules\StatusBar\Views\StatusBarView.xaml.cs">
      <DependentUpon>StatusBarView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\ToolBars\Controls\CustomToggleButton.cs" />
    <Compile Include="UI\Modules\ToolBars\Controls\MainToolBar.cs" />
    <Compile Include="UI\Modules\ToolBars\Controls\ToolBarBase.cs" />
    <Compile Include="UI\Modules\ToolBars\Controls\ToolBarTrayContainer.cs" />
    <Compile Include="UI\Modules\ToolBars\Controls\ToolPaneToolBar.cs" />
    <Compile Include="UI\Modules\ToolBars\IToolBar.cs" />
    <Compile Include="UI\Modules\ToolBars\IToolBarBuilder.cs" />
    <Compile Include="UI\Modules\ToolBars\IToolBars.cs" />
    <Compile Include="UI\Modules\ToolBars\Models\CommandToolBarItem.cs" />
    <Compile Include="UI\Modules\ToolBars\Models\ToolBarItemBase.cs" />
    <Compile Include="UI\Modules\ToolBars\Models\ToolBarItemSeparator.cs" />
    <Compile Include="UI\Modules\ToolBars\Models\ToolBarModel.cs" />
    <Compile Include="UI\Modules\ToolBars\Module.cs" />
    <Compile Include="UI\Modules\ToolBars\ToolBarBuilder.cs" />
    <Compile Include="UI\Modules\ToolBars\ToolBarDefinitions.cs" />
    <Compile Include="UI\Modules\ToolBars\ViewModels\ToolBarsViewModel.cs" />
    <Compile Include="UI\Modules\ToolBars\Views\IToolBarView.cs" />
    <Compile Include="UI\Modules\ToolBars\Views\ToolBarsView.xaml.cs">
      <DependentUpon>ToolBarsView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\Toolbox\Commands\ViewToolboxCommandDefinition.cs" />
    <Compile Include="UI\Modules\Toolbox\Commands\ViewToolboxCommandHandler.cs" />
    <Compile Include="UI\Modules\Toolbox\Design\DesignTimeToolboxViewModel.cs" />
    <Compile Include="UI\Modules\Toolbox\IToolbox.cs" />
    <Compile Include="UI\Modules\Toolbox\MenuDefinitions.cs" />
    <Compile Include="UI\Modules\Toolbox\Models\ToolboxItem.cs" />
    <Compile Include="UI\Modules\Toolbox\Services\IToolboxService.cs" />
    <Compile Include="UI\Modules\Toolbox\Services\ToolboxService.cs" />
    <Compile Include="UI\Modules\Toolbox\ToolboxDragDrop.cs" />
    <Compile Include="UI\Modules\Toolbox\ToolboxItemAttribute.cs" />
    <Compile Include="UI\Modules\Toolbox\ViewModels\ToolboxItemViewModel.cs" />
    <Compile Include="UI\Modules\Toolbox\ViewModels\ToolboxViewModel.cs" />
    <Compile Include="UI\Modules\Toolbox\Views\ToolboxView.xaml.cs">
      <DependentUpon>ToolboxView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Modules\UndoRedo\Commands\RedoCommandDefinition.cs" />
    <Compile Include="UI\Modules\UndoRedo\Commands\UndoCommandDefinition.cs" />
    <Compile Include="UI\Modules\UndoRedo\Commands\ViewHistoryCommandDefinition.cs" />
    <Compile Include="UI\Modules\UndoRedo\Commands\ViewHistoryCommandHandler.cs" />
    <Compile Include="UI\Modules\UndoRedo\Design\DesignTimeHistoryViewModel.cs" />
    <Compile Include="UI\Modules\UndoRedo\IHistoryTool.cs" />
    <Compile Include="UI\Modules\UndoRedo\IUndoableAction.cs" />
    <Compile Include="UI\Modules\UndoRedo\IUndoRedoManager.cs" />
    <Compile Include="UI\Modules\UndoRedo\MenuDefinitions.cs" />
    <Compile Include="UI\Modules\UndoRedo\Services\UndoRedoManager.cs" />
    <Compile Include="UI\Modules\UndoRedo\ToolBarDefinitions.cs" />
    <Compile Include="UI\Modules\UndoRedo\ViewModels\HistoryItemType.cs" />
    <Compile Include="UI\Modules\UndoRedo\ViewModels\HistoryItemViewModel.cs" />
    <Compile Include="UI\Modules\UndoRedo\ViewModels\HistoryViewModel.cs" />
    <Compile Include="UI\Modules\UndoRedo\Views\HistoryView.xaml.cs">
      <DependentUpon>HistoryView.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Themes\VS2013\Controls\Converters\TreeViewIndentConverter.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="UI\app.config" />
    <None Include="UI\Framework\ShaderEffects\GrayscaleEffect.ps" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="UI\Modules\MainMenu\Resources\Styles.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\MainMenu\Views\MainMenuSettingsView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\MainMenu\Views\MainMenuView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\MainWindow\Views\MainWindowView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\Settings\SampleData\SettingsViewModelSampleData.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\Settings\Views\SettingsView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\Shell\Views\ShellView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\StatusBar\Views\StatusBarView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\ToolBars\Resources\Styles.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\ToolBars\Views\ToolBarsView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\Toolbox\Views\ToolboxView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Modules\UndoRedo\Views\HistoryView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\Generic.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\BlueTheme.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Button.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\CheckBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\ColorCanvas.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\ComboBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Focus.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Label.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Menu.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Merged.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\ScrollBar.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Slider.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\TabControl.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\TextBox.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Toolbar.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Tooltip.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\TreeView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\Window.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\Controls\WindowCommands.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\DarkTheme.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="UI\Themes\VS2013\LightTheme.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\FullScreen.png" />
    <Resource Include="UI\Resources\Icons\Open.png" />
    <Resource Include="UI\Resources\Icons\Redo.png" />
    <Resource Include="UI\Resources\Icons\Save.png" />
    <Resource Include="UI\Resources\Icons\SaveAll.png" />
    <Resource Include="UI\Resources\Icons\Undo.png" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.zh-Hans.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.zh-Hans.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\AddSol.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\RunOnce.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\Stop.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\CameraSet.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\CommunicationSet.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\UIDesign.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\GlobalVar.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\UserLogin.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\RunCycle.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="UI\Resources\Icons\MCT.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>