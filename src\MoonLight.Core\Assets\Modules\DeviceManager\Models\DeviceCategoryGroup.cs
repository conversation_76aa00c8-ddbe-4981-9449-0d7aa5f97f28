﻿using MoonLight.Core.Common.Helper;
using MoonLight.Core.Devices;
using Newtonsoft.Json;
using System.Collections.ObjectModel;

namespace MoonLight.Core.Modules
{
    public class DeviceCategoryGroup : NotifyPropertyBase
    {
        public DeviceCategory Category { get; }
        public string CategoryName => Category.ToString();
        public ObservableCollection<IDevice> Devices { get; } = new ObservableCollection<IDevice>();

        public DeviceCategoryGroup(DeviceCategory category)
        {
            Category = category;
        }

        [JsonIgnore]
        private IDevice _selectedDevice;
        [JsonIgnore]
        public IDevice SelectedDevice
        {
            get => _selectedDevice;
            set
            {
                if (_selectedDevice != value)
                {
                    _selectedDevice = value;
                    RaisePropertyChanged();
                }
            }
        }
    }
}
