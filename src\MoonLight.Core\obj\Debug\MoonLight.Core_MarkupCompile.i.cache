﻿MoonLight.Core


library
C#
.cs
C:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_V2_20150514\src\MoonLight.Core\obj\Debug\
MoonLight.Core
none
false
DEBUG;TRACE

36-1102983867

313418529153
401145694386
Assets\Collection.xaml;Assets\Modules\FlowManager\Views\FlowTreeView.xaml;Assets\Modules\FlowManager\Views\PropertyPanelView.xaml;Assets\Modules\FlowManager\Views\RunManagerView.xaml;Assets\Modules\Output\Views\LogView.xaml;Assets\Modules\Output\Views\OutputView.xaml;Assets\Modules\PosManager\Views\PosManagerView.xaml;Assets\Modules\RenderControl\Views\RenderViewWpf.xaml;Assets\Modules\RenderControl\Views\VisionView.xaml;Assets\Modules\Settings\Views\SettingSysView.xaml;Assets\Modules\ToolBar\Views\CanvasSetView.xaml;Assets\Modules\ToolBar\Views\CommunicationSetView.xaml;Assets\Modules\ToolBar\Views\GlobalVarView.xaml;Assets\Modules\ToolBox\Views\ToolBoxView.xaml;Assets\Modules\ToolOutput\Views\DeviceStatusView.xaml;Assets\Modules\ToolOutput\Views\ToolOutputView.xaml;Assets\Styles\ButtonGroupStyle.xaml;Assets\Styles\ButtonStyle.xaml;Assets\Styles\Converter.xaml;Assets\Styles\Icon.xaml;Assets\Styles\MenuStyle.xaml;Assets\Styles\PropertyGridStyle.xaml;Assets\Styles\RadioButtonStyle.xaml;Assets\Styles\TabControlStyle.xaml;Assets\Styles\TextBoxStyle.xaml;Assets\Styles\TreeViewStyle.xaml;Devices\Communication\CommunicationSetView.xaml;Devices\Motion\Views\AxisControlView.xaml;Devices\Motion\Views\CardView.xaml;Views\ChangePwdView.xaml;Views\EditRemarksView.xaml;Views\HardwareConfigView.xaml;Views\LoadingView.xaml;Views\LoginView.xaml;Views\MessageBoxView.xaml;Views\VarLinkView.xaml;

True

