﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

namespace MoonLight.Core.Assets.Converter
{
    public class ObjectToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value==null)
            {
                return "null";
            }
            else if (value.GetType().IsGenericType && value.GetType().GetGenericTypeDefinition() == typeof(List<>))
            {
                string values = string.Join(", ", (value as IList).Cast<object>().Select(item =>
                {
                    // 检查 item 是否能转换为数值，若能则格式化为两位小数  
                    if (item is IConvertible convertibleItem)
                    {
                        return Math.Round(convertibleItem.ToDouble(null), 2).ToString("F2");
                    }
                    return item.ToString(); // 对于非数值类型，直接转换为字符串  
                }));
                return $"count={(value as IList).Count}  values={values}";
            }
            return value.ToString();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return Binding.DoNothing;
        }
    }
}
