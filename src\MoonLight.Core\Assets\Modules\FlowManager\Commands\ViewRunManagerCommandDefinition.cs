﻿using MoonLight.UI.Framework.Commands;

namespace MoonLight.Modules.FlowManager.Commands
{
    [CommandDefinition]
    public class ViewRunManagerCommandDefinition : CommandDefinition
    {
        public const string CommandName = "View.RunManager";

        public override string Name
        {
            get { return CommandName; }
        }

        public override string Text
        {
            get { return "运行栏"; }
        }

        public override string ToolTip
        {
            get { return "运行栏"; }
        }
    }
}
