﻿using MoonLight.Core.Common.Helper;
using MoonLight.Core.Models;
using System;
using System.Collections.ObjectModel;
using System.Windows;

namespace MoonLight.Modules.Output.ViewModels
{
    public class LogViewModel : NotifyPropertyBase
    {
        #region Singleton
        private static readonly LogViewModel _instance = new LogViewModel();

        private LogViewModel()
        {
        }
        public static LogViewModel Ins
        {
            get { return _instance; }
        }
        #endregion
        #region Prop
        // 显示日志集合
        private ObservableCollection<LogModel> _DisplayLogCollection = new ObservableCollection<LogModel>();
        public ObservableCollection<LogModel> DisplayLogCollection
        {
            get { return _DisplayLogCollection; }
            set { Set(ref _DisplayLogCollection, value); }
        }
        // 所有日志集合
        private ObservableCollection<LogModel> _AllLogCollection = new ObservableCollection<LogModel>();
        public ObservableCollection<LogModel> AllLogCollection
        {
            get { return _AllLogCollection; }
            set { Set(ref _AllLogCollection, value); }
        }
        // 消息日志集合
        private ObservableCollection<LogModel> _InfoCollection = new ObservableCollection<LogModel>();
        public ObservableCollection<LogModel> InfoCollection
        {
            get { return _InfoCollection; }
            set { Set(ref _InfoCollection, value); }
        }
        // 警告日志集合
        private ObservableCollection<LogModel> _WarnCollection = new ObservableCollection<LogModel>();
        public ObservableCollection<LogModel> WarnCollection
        {
            get { return _WarnCollection; }
            set { Set(ref _WarnCollection, value); }
        }
        // 错误日志集合
        private ObservableCollection<LogModel> _ErrorCollection = new ObservableCollection<LogModel>();
        public ObservableCollection<LogModel> ErrorCollection
        {
            get { return _ErrorCollection; }
            set { Set(ref _ErrorCollection, value); }
        }
        // 报警日志集合
        private ObservableCollection<LogModel> _AlarmCollection = new ObservableCollection<LogModel>();
        public ObservableCollection<LogModel> AlarmCollection
        {
            get { return _AlarmCollection; }
            set { Set(ref _AlarmCollection, value); }
        }

        private LogModel _SelectedItem;
        public LogModel SelectedItem
        {
            get { return _SelectedItem; }
            set { Set(ref _SelectedItem, value); }
        }

        private int _InfoCount;
        public int InfoCount
        {
            get { return _InfoCount; }
            set { Set(ref _InfoCount, value); }
        }

        private int _WarnCount;
        public int WarnCount
        {
            get { return _WarnCount; }
            set { Set(ref _WarnCount, value); }
        }

        private int _ErrorCount;
        public int ErrorCount
        {
            get { return _ErrorCount; }
            set { Set(ref _ErrorCount, value); }
        }

        private int _AlarmCount;
        public int AlarmCount
        {
            get { return _AlarmCount; }
            set { Set(ref _AlarmCount, value); }
        }

        private bool _InfoFilter;
        public bool InfoFilter
        {
            get { return _InfoFilter; }
            set
            {
                Set(ref _InfoFilter, value);
                if (_InfoFilter == true)
                {
                    WarnFilter = false;
                    ErrorFilter = false;
                    ErrorFilter = false;
                    DisplayLogCollection.Clear();
                    foreach (var item in InfoCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
                else
                {
                    DisplayLogCollection.Clear();
                    foreach (var item in AllLogCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
            }
        }

        private bool _WarnFilter;
        public bool WarnFilter
        {
            get { return _WarnFilter; }
            set
            {
                Set(ref _WarnFilter, value);
                if (_WarnFilter == true)
                {
                    InfoFilter = false;
                    ErrorFilter = false;
                    ErrorFilter = false;
                    DisplayLogCollection.Clear();
                    foreach (var item in WarnCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
                else
                {
                    DisplayLogCollection.Clear();
                    foreach (var item in AllLogCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
            }
        }

        private bool _ErrorFilter;
        public bool ErrorFilter
        {
            get { return _ErrorFilter; }
            set
            {
                Set(ref _ErrorFilter, value);
                if (_ErrorFilter == true)
                {
                    InfoFilter = false;
                    WarnFilter = false;
                    AlarmFilter = false;
                    DisplayLogCollection.Clear();
                    foreach (var item in ErrorCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
                else
                {
                    DisplayLogCollection.Clear();
                    foreach (var item in AllLogCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
            }
        }

        private bool _AlarmFilter;
        public bool AlarmFilter
        {
            get { return _AlarmFilter; }
            set
            {
                Set(ref _AlarmFilter, value);
                if (_AlarmFilter == true)
                {
                    InfoFilter = false;
                    WarnFilter = false;
                    ErrorFilter = false;
                    DisplayLogCollection.Clear();
                    foreach (var item in AlarmCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
                else
                {
                    DisplayLogCollection.Clear();
                    foreach (var item in AllLogCollection)
                    {
                        DisplayLogCollection.Add(item);
                    }
                }
            }
        }
        #endregion
        #region Command
        // 清除报警
        [NonSerialized]
        private CommandBase _ClearAlarmCommand;
        public CommandBase ClearAlarmCommand
        {
            get
            {
                if (_ClearAlarmCommand == null)
                {
                    _ClearAlarmCommand = new CommandBase((obj) =>
                    {
                        AlarmCollection.Clear();
                        AlarmCount = AlarmCollection.Count;
                        CommonMethods.Mach.AlmFlag = false;
                    });
                }
                return _ClearAlarmCommand;
            }
        }

        // 清除所有日志
        [NonSerialized]
        private CommandBase _ClearAllLogCommand;
        public CommandBase ClearAllLogCommand
        {
            get
            {
                if (_ClearAllLogCommand == null)
                {
                    _ClearAllLogCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            DisplayLogCollection.Clear();
                            InfoCollection.Clear();
                            WarnCollection.Clear();
                            ErrorCollection.Clear();
                            AlarmCollection.Clear();
                            InfoCount = InfoCollection.Count;
                            WarnCount = WarnCollection.Count;
                            ErrorCount = ErrorCollection.Count;
                            AlarmCount = AlarmCollection.Count;
                            CommonMethods.Mach.AlmFlag = false;
                        }
                        catch (Exception) { }
                    });
                }
                return _ClearAllLogCommand;
            }
        }

        // 复制日志
        [NonSerialized]
        private CommandBase _CopyLogCommand;
        public CommandBase CopyLogCommand
        {
            get
            {
                if (_CopyLogCommand == null)
                {
                    _CopyLogCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            if (SelectedItem != null)
                            {
                                Clipboard.SetText(SelectedItem.Content);
                            }
                        }
                        catch (Exception) { }
                    });
                }
                return _CopyLogCommand;
            }
        }
        #endregion
    }
}
