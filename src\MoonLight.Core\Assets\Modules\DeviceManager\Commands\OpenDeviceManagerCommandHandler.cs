﻿using MahApps.Metro.Controls;
using MoonLight.Core.Assets.Modules.DeviceManager.Views;
using MoonLight.UI.Framework.Commands;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework.Threading;
using MoonLight.UI.Modules.Settings.ViewModels;
using System.ComponentModel.Composition;
using System.Threading.Tasks;
using System.Windows;

namespace MoonLight.Core.Commands
{
    [CommandHandler]
    public class OpenDeviceManagerCommandHandler : CommandHandlerBase<OpenDeviceManagerCommandDefinition>
    {

        private readonly IWindowManager _windowManager;

        [ImportingConstructor]
        public OpenDeviceManagerCommandHandler(IWindowManager windowManager)
        {
            _windowManager = windowManager;
        }

        public override async Task Run(Command command)
        {
            var view = new DeviceManagerView();
            view.ShowDialog();
            await TaskUtility.Completed;
            //await _windowManager.ShowDialogAsync(new DeviceManagerView());
        }
    }
}
