﻿<UserControl x:Class="MoonLight.Modules.Measuring.Views.MeasuringPropertyView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="clr-namespace:MoonLight.Modules.Measuring.ViewModels"
             xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:MoonLight.Modules.Measuring.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="350">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/MoonLight.Core;component/Assets/Collection.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid x:Name="gd">
        <Grid.RowDefinitions>
            <RowDefinition Height="70"/>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <GroupBox Header="图像链接">
            <StackPanel Orientation="Horizontal" Margin="25 0">
                <TextBlock Text="输入图像" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                <TextBox HorizontalAlignment="Center" VerticalAlignment="Center" BorderThickness="0 0 0 1" Width="200" Margin="5 0"
                    Style="{StaticResource LinkTextBox}"
                    Text="{Binding InputImageLinkText}"
                    mah:TextBoxHelper.LinkButtonCommand="{Binding SelectedLinkCommand,Mode=OneWay}"
                    mah:TextBoxHelper.LinkButtonCommandParameter="{x:Static vm:Link.InputImageLink}" TextChanged="TextBox_TextChanged"/>
            </StackPanel>
        </GroupBox>
        <GroupBox Header="拟合参数" Grid.Row="1">
            <StackPanel>
                <Grid>
                    <Button Content="设置卡尺" Command="{Binding SetCaliperCommand}"/>
                    <Button Content="确认" Command="{Binding NoSetCaliperCommand}" Visibility="{Binding IsSetCaliper, Converter={StaticResource Bool2VisibilityConverter}}"/>
                </Grid>
                <StackPanel Orientation="Horizontal" Margin="15 5">
                    <TextBlock Text="灰度阈值：" VerticalAlignment="Center" Width="60" TextAlignment="Right"/>
                    <mah:NumericUpDown Width="200" TextAlignment="Left" Margin="5 0" BorderThickness="0 0 0 1" Value="{Binding MeasureParam.MinEdgeCurvature}" Minimum="1"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="15 5">
                    <TextBlock Text="平滑系数：" VerticalAlignment="Center" Width="60" TextAlignment="Right"/>
                    <mah:NumericUpDown Width="200" TextAlignment="Left" Margin="5 0" BorderThickness="0 0 0 1" Value="{Binding MeasureParam.Sigma}" Minimum="1"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="15 5">
                    <TextBlock Text="颜色模式：" VerticalAlignment="Center" Width="60" TextAlignment="Right"/>
                    <ComboBox Width="200" Margin="5 0" BorderThickness="0 0 0 1" SelectedItem="{Binding MeasureParam.MeasMode}" ItemsSource="{Binding MeasModes}"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="15 5">
                    <TextBlock Text="点筛选：" VerticalAlignment="Center" Width="60" TextAlignment="Right"/>
                    <ComboBox Width="200" Margin="5 0" BorderThickness="0 0 0 1" SelectedItem="{Binding MeasureParam.MeasSelect}" ItemsSource="{Binding MeasSelects}"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>
        <GroupBox Header="显示设置" Grid.Row="2">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="10 5">
                    <CheckBox Content="显示结果点" IsChecked="{Binding ShowResultPoint}"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>
    </Grid>
</UserControl>
