﻿using MoonLight.UI.Framework.Commands;

namespace MoonLight.Modules.Output.Commands
{
    [CommandDefinition]
    public class ViewOutputCommandDefinition : CommandDefinition
    {
        public const string CommandName = "View.Output";

        public override string Name
        {
            get { return CommandName; }
        }

        public override string Text
        {
            get { return "输出"; }
        }

        public override string ToolTip
        {
            get { return "输出"; }
        }
    }
}
