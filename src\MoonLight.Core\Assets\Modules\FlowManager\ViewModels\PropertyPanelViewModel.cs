﻿using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Core.Services;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework;
using System.ComponentModel.Composition;
using System.Windows.Controls;
using System.Windows;
using MoonLight.Modules.FlowManager.Views;
using System.Windows.Forms;
using System.Windows.Threading;
using System.Windows.Media;
using MoonLight.Core.Common.Helper;
using UserControl = System.Windows.Controls.UserControl;
using System;

namespace MoonLight.Modules.FlowManager.ViewModels
{
    [Export]
    public class PropertyPanelViewModel : Tool
    {

        public UserControl VisualControl { get; set; }


        private ToolUnit _SelectedTool;
        public ToolUnit SelectedTool
        {
            get { return _SelectedTool; }
            set
            {
                try
                {
                    if (value == null)
                    {
                        PropertyControl = null;
                        if (SelectedTool != null)
                        {
                            int id = SelectedTool.Prj.DispViewID;
                            SelectedTool.ClearWindow(IoC.Get<IRenderViewManager>().GetView(id));
                        }
                        return;
                    }

                    value.IsEdit = IsEnabled;
                    Set(ref _SelectedTool, value);

                    if (VisualControl != null) VisualControl.Visibility = Visibility.Collapsed;

                    var childs = WPFElementTool.GetChildObjects<System.Windows.Controls.UserControl>(_grid, typeof(System.Windows.Controls.UserControl));
                    var control = SelectedTool.GetUserControl();

                    if (!childs.Contains(control))
                    {
                        _grid.Children.Add(control);
                    }
                    VisualControl = control;
                    VisualControl.Visibility = Visibility.Visible;
                    SelectedTool.Loaded(IoC.Get<IRenderViewManager>().GetView(SelectedTool.Prj.DispViewID), null);
                }
                catch(Exception ex)
                {

                }
            }
        }

        private FrameworkElement _PropertyControl;
        public FrameworkElement PropertyControl
        {
            get { return _PropertyControl; }
            set { Set(ref _PropertyControl, value); }
        }

        private FrameworkElement _view;
        private Grid _grid;


        private bool _IsEnabled = true;
        public bool IsEnabled
        {
            get { return _IsEnabled; }
            set
            {
                Set(ref _IsEnabled, value);
                if (SelectedTool != null)
                {
                    SelectedTool.IsEdit = IsEnabled;
                    if (IsEnabled)
                    {
                        SelectedTool.ShowHRoi();
                    }
                }
            }
        }


        public override PaneLocation PreferredLocation => PaneLocation.Right;

        public PropertyPanelViewModel()
        {
            DisplayName = "属性栏";
        }

        protected override void OnViewLoaded(object view)
        {
            _view = (PropertyPanelView)view;
            _grid = WPFElementTool.GetVisualChild<Grid>(_view);
        }
    }
}
