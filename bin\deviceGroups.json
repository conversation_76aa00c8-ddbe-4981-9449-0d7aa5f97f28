{"$id": "1", "$type": "System.Collections.Generic.List`1[[MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": [{"$id": "2", "$type": "MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "Category": 0, "CategoryName": "None", "Devices": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Devices.IDevice, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": []}}, {"$id": "3", "$type": "MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "Category": 1, "CategoryName": "Camera", "Devices": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Devices.IDevice, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": [{"$id": "4", "$type": "MoonLight.Devices.Camera.Daheng.CameraDaheng, MoonLight.Devices.Camera, Version=*******, Culture=neutral, PublicKeyToken=null", "TimeoutOnce": 500, "Type": 0, "TrigMode": 1, "IsHWindow": false, "ExposureMin": 10, "ExposureMax": 10000, "Exposure": 1000.0, "GainMin": 1.0, "GainMax": 100.0, "Gain": 1.0, "PixelX": 2048, "PixelY": 2048, "IsRGBToGray": false, "IsRotate": false, "Angle": 0, "Width": 0, "Height": 0, "CameraInfo": {"$id": "5", "$type": "MoonLight.Core.Devices.Camera.CameraInfo, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "CamName": "MER2-501-23GM-P(169.254.176.12[00-21-49-06-8C-F5])", "SerialNO": "FKT24100039"}, "Id": "a3ee57ea-5f00-4172-a47b-b001cd7e7507", "Name": "Camera", "Category": 1, "IsEnabled": false, "Configuration": {"$type": "System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"}, "IsConnected": false, "IsUse": true}]}}, {"$id": "6", "$type": "MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "Category": 2, "CategoryName": "BarcodeReader", "Devices": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Devices.IDevice, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": []}}, {"$id": "7", "$type": "MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "Category": 3, "CategoryName": "NetworkDevice", "Devices": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Devices.IDevice, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": []}}, {"$id": "8", "$type": "MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "Category": 4, "CategoryName": "SerialDevice", "Devices": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Devices.IDevice, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": []}}, {"$id": "9", "$type": "MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "Category": 5, "CategoryName": "MotionController", "Devices": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Devices.IDevice, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": [{"$id": "10", "$type": "MoonLight.Devices.Motion.Pmac.CardPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "ListCardCoordinates": {"$id": "11", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate[], MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "$values": [{"$id": "12", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "13", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "14", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "15", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "16", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "17", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "18", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "19", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "20", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "21", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "22", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "23", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "24", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "25", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "26", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "27", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "28", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "29", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "30", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "31", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "32", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "33", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "34", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "35", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "36", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "37", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "38", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "39", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "40", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "41", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}, {"$id": "42", "$type": "MoonLight.Devices.Motion.Pmac.CardCoordinate, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "CoordinateStatus": {"$id": "43", "$type": "MoonLight.Devices.Motion.Pmac.CoordinateStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "TimerEnabled": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved": false, "BlockRequest": false, "TimersEnabled": false, "RadiusErrorBit1": false, "RadiusErrorBit0": false, "SoftLimit": false, "RunTimeError": false, "PvtError": false, "LinToPvtError": false, "ErrorStatusBit1": false, "ErrorStatusBit0": false, "Csolve": false, "LinToPvtBuf": false, "FeedHoldBit1": false, "FeedHoldBit0": false, "BlockActive": false, "ContMotion": false, "CCModeBit1": false, "CCModeBit0": false, "MoveModeBit1": false, "MoveModeBit0": false, "SegMoveBit1": false, "SegMoveBit0": false, "SegMoveAccel": false, "SegMoveDecel": false, "SegEnabled": false, "SegStopReq": false, "LookAheadWrap": false, "LookAheadLookBack": false, "LookAheadDir": false, "LookAheadStop": false, "LookAheadChange": false, "LookAheadReCalc": false, "LookAheadFlush": false, "LookAheadActive": false, "CCAddedArc": false, "CCOffReq": false, "CCMoveTypeBit1": false, "CCMoveTypeBit0": false, "EndDelayActive": false, "CC3Active": false, "SharpCornerStop": false, "AddedDwellDis": false}}]}, "ListAxis": {"$id": "44", "$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Motion.AxisBase, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": [{"$id": "45", "$type": "MoonLight.Devices.Motion.Pmac.AxisPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "Limit": {"$id": "46", "$type": "MoonLight.Devices.Motion.Pmac.LimitPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "JerkMax": 1000.0, "JerkMin": 0.1, "JerkMaxPulse": 1.0, "JerkMinPulse": 1.0, "PosMax": 10000.0, "PosMin": -100.0, "VelMax": 10000.0, "VelMin": 0.001, "AccMax": 10000.0, "AccMin": 0.001, "PosMaxPulse": 1.0, "PosMinPulse": 1.0, "VelMaxPulse": 1.0, "VelMinPulse": 1.0, "AccMaxPulse": 1.0, "AccMinPulse": 1.0, "IsUse": true, "Name": "名称"}, "CoordSystem": 1, "ProgName": "Myprog1", "IsVirtual": false, "Status": {"$id": "47", "$type": "MoonLight.Devices.Motion.Pmac.StatusAxisPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "PosFollow": 0.0, "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "Reserved_4_2": false, "Reserved_4_1": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved_6_4": false, "BlockRequest": false, "PhaseFound": false, "TriggerSpeedSel": false, "GantryHomed": false, "SpindleMotor_bit1": false, "SpindleMotor_bit2": false, "Reserved_8_8": false, "Reserved_8_4": false, "Reserved_8_2": false, "Reserved_8_1": false, "Csolve": false, "SoftLimit": false, "DacLimit": false, "BlDir": false, "SoftLimitDir": false, "Reserved_10_4": false, "Reserved_10_2": false, "Reserved_10_1": false, "IsEnable": false, "IsEnableLast": false, "IsHome": false, "IsHoming": false, "IsPlusLimit": false, "IsMinusLimit": false, "IsOrigin": false, "IsError": false, "IsMove": false, "IsPos": false, "IsPlus": false, "Pos": 0.0, "PosLast": 0.0, "PosPlan": 0.0}, "AxisPmacStatus": {"$id": "48", "$type": "MoonLight.Devices.Motion.Pmac.AxisPmacStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "Reserved1": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved2": false, "BlockRequest": false, "PhaseFound": false, "TriggerSpeedSel": false, "GantryHomed": false, "SpindleMotorBit1": false, "SpindleMotorBit0": false, "Reserved3": false, "Csolve": false, "SoftLimit": false, "DacLimit": false, "BlDir": false, "SoftLimitDir": false}, "HomeProgram": "", "HomeOK": "", "HomeTimeout": 10000, "MoveTimeout": 10000, "Name": "Axis0", "ID": 0, "Card": null, "PluseScale": 1.0, "FollowError": 0.003, "StepValue": 1.0, "IsVirtualAxis": false, "PosLimitMax": 10000.0, "PosLimitMin": -10.0, "DesPos": 0.0, "DesVel": 0.0, "DesVelMax": 0.0, "DesVelMin": 0.0, "DesAccMax": 0.0, "DesAccMin": 0.0, "ActPos": 0.0, "ActVel": 0.0, "ActVelMax": 0.0, "ActVelMin": 0.0, "ActAccMax": 0.0, "ActAccMin": 0.0, "IsEnable": false, "IsHome": false, "IsHoming": false, "IsPlusLimit": false, "IsMinusLimit": false, "IsOrigin": false, "IsError": false, "IsMoving": false, "IsInPos": false, "VelHome": 0.0, "VelStep": 0.0, "VelJog": 0.0, "IsUse": true, "IsPlan": false, "VelType": 0, "StepType": 0, "IsReverse": false, "IsHomeOffset": false, "HomeOffset": 0.0}, {"$id": "49", "$type": "MoonLight.Devices.Motion.Pmac.AxisPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "Limit": {"$id": "50", "$type": "MoonLight.Devices.Motion.Pmac.LimitPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "JerkMax": 1000.0, "JerkMin": 0.1, "JerkMaxPulse": 1.0, "JerkMinPulse": 1.0, "PosMax": 10000.0, "PosMin": -100.0, "VelMax": 10000.0, "VelMin": 0.001, "AccMax": 10000.0, "AccMin": 0.001, "PosMaxPulse": 1.0, "PosMinPulse": 1.0, "VelMaxPulse": 1.0, "VelMinPulse": 1.0, "AccMaxPulse": 1.0, "AccMinPulse": 1.0, "IsUse": true, "Name": "名称"}, "CoordSystem": 1, "ProgName": "Myprog1", "IsVirtual": false, "Status": {"$id": "51", "$type": "MoonLight.Devices.Motion.Pmac.StatusAxisPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "PosFollow": 0.0, "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "Reserved_4_2": false, "Reserved_4_1": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved_6_4": false, "BlockRequest": false, "PhaseFound": false, "TriggerSpeedSel": false, "GantryHomed": false, "SpindleMotor_bit1": false, "SpindleMotor_bit2": false, "Reserved_8_8": false, "Reserved_8_4": false, "Reserved_8_2": false, "Reserved_8_1": false, "Csolve": false, "SoftLimit": false, "DacLimit": false, "BlDir": false, "SoftLimitDir": false, "Reserved_10_4": false, "Reserved_10_2": false, "Reserved_10_1": false, "IsEnable": false, "IsEnableLast": false, "IsHome": false, "IsHoming": false, "IsPlusLimit": false, "IsMinusLimit": false, "IsOrigin": false, "IsError": false, "IsMove": false, "IsPos": false, "IsPlus": false, "Pos": 0.0, "PosLast": 0.0, "PosPlan": 0.0}, "AxisPmacStatus": {"$id": "52", "$type": "MoonLight.Devices.Motion.Pmac.AxisPmacStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "Reserved1": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved2": false, "BlockRequest": false, "PhaseFound": false, "TriggerSpeedSel": false, "GantryHomed": false, "SpindleMotorBit1": false, "SpindleMotorBit0": false, "Reserved3": false, "Csolve": false, "SoftLimit": false, "DacLimit": false, "BlDir": false, "SoftLimitDir": false}, "HomeProgram": "", "HomeOK": "", "HomeTimeout": 10000, "MoveTimeout": 10000, "Name": "Axis1", "ID": 1, "Card": null, "PluseScale": 1.0, "FollowError": 0.003, "StepValue": 1.0, "IsVirtualAxis": false, "PosLimitMax": 10000.0, "PosLimitMin": -10.0, "DesPos": 0.0, "DesVel": 0.0, "DesVelMax": 0.0, "DesVelMin": 0.0, "DesAccMax": 0.0, "DesAccMin": 0.0, "ActPos": 0.0, "ActVel": 0.0, "ActVelMax": 0.0, "ActVelMin": 0.0, "ActAccMax": 0.0, "ActAccMin": 0.0, "IsEnable": false, "IsHome": false, "IsHoming": false, "IsPlusLimit": false, "IsMinusLimit": false, "IsOrigin": false, "IsError": false, "IsMoving": false, "IsInPos": false, "VelHome": 0.0, "VelStep": 0.0, "VelJog": 0.0, "IsUse": true, "IsPlan": false, "VelType": 0, "StepType": 0, "IsReverse": false, "IsHomeOffset": false, "HomeOffset": 0.0}, {"$id": "53", "$type": "MoonLight.Devices.Motion.Pmac.AxisPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "Limit": {"$id": "54", "$type": "MoonLight.Devices.Motion.Pmac.LimitPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "JerkMax": 1000.0, "JerkMin": 0.1, "JerkMaxPulse": 1.0, "JerkMinPulse": 1.0, "PosMax": 10000.0, "PosMin": -100.0, "VelMax": 10000.0, "VelMin": 0.001, "AccMax": 10000.0, "AccMin": 0.001, "PosMaxPulse": 1.0, "PosMinPulse": 1.0, "VelMaxPulse": 1.0, "VelMinPulse": 1.0, "AccMaxPulse": 1.0, "AccMinPulse": 1.0, "IsUse": true, "Name": "名称"}, "CoordSystem": 1, "ProgName": "Myprog1", "IsVirtual": false, "Status": {"$id": "55", "$type": "MoonLight.Devices.Motion.Pmac.StatusAxisPmac, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "PosFollow": 0.0, "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "Reserved_4_2": false, "Reserved_4_1": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved_6_4": false, "BlockRequest": false, "PhaseFound": false, "TriggerSpeedSel": false, "GantryHomed": false, "SpindleMotor_bit1": false, "SpindleMotor_bit2": false, "Reserved_8_8": false, "Reserved_8_4": false, "Reserved_8_2": false, "Reserved_8_1": false, "Csolve": false, "SoftLimit": false, "DacLimit": false, "BlDir": false, "SoftLimitDir": false, "Reserved_10_4": false, "Reserved_10_2": false, "Reserved_10_1": false, "IsEnable": false, "IsEnableLast": false, "IsHome": false, "IsHoming": false, "IsPlusLimit": false, "IsMinusLimit": false, "IsOrigin": false, "IsError": false, "IsMove": false, "IsPos": false, "IsPlus": false, "Pos": 0.0, "PosLast": 0.0, "PosPlan": 0.0}, "AxisPmacStatus": {"$id": "56", "$type": "MoonLight.Devices.Motion.Pmac.AxisPmacStatus, MoonLight.Devices.Motion, Version=*******, Culture=neutral, PublicKeyToken=null", "TriggerMove": false, "HomeInProgress": false, "MinusLimit": false, "PlusLimit": false, "FeWarn": false, "FeFatal": false, "LimitStop": false, "AmpFault": false, "SoftMinusLimit": false, "SoftPlusLimit": false, "I2tFault": false, "TriggerNotFound": false, "AmpWarn": false, "EncLoss": false, "AuxFault": false, "Reserved1": false, "HomeComplete": false, "DesVelZero": false, "ClosedLoop": false, "AmpEna": false, "InPos": false, "Reserved2": false, "BlockRequest": false, "PhaseFound": false, "TriggerSpeedSel": false, "GantryHomed": false, "SpindleMotorBit1": false, "SpindleMotorBit0": false, "Reserved3": false, "Csolve": false, "SoftLimit": false, "DacLimit": false, "BlDir": false, "SoftLimitDir": false}, "HomeProgram": "", "HomeOK": "", "HomeTimeout": 10000, "MoveTimeout": 10000, "Name": "Axis2", "ID": 2, "Card": null, "PluseScale": 1.0, "FollowError": 0.003, "StepValue": 1.0, "IsVirtualAxis": false, "PosLimitMax": 10000.0, "PosLimitMin": -10.0, "DesPos": 0.0, "DesVel": 0.0, "DesVelMax": 0.0, "DesVelMin": 0.0, "DesAccMax": 0.0, "DesAccMin": 0.0, "ActPos": 0.0, "ActVel": 0.0, "ActVelMax": 0.0, "ActVelMin": 0.0, "ActAccMax": 0.0, "ActAccMin": 0.0, "IsEnable": false, "IsHome": false, "IsHoming": false, "IsPlusLimit": false, "IsMinusLimit": false, "IsOrigin": false, "IsError": false, "IsMoving": false, "IsInPos": false, "VelHome": 0.0, "VelStep": 0.0, "VelJog": 0.0, "IsUse": true, "IsPlan": false, "VelType": 0, "StepType": 0, "IsReverse": false, "IsHomeOffset": false, "HomeOffset": 0.0}]}, "ListDi": {"$id": "57", "$type": "System.Collections.Generic.List`1[[MoonLight.Core.Motion.IOBase, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": []}, "ListDo": {"$id": "58", "$type": "System.Collections.Generic.List`1[[MoonLight.Core.Motion.IOBase, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": []}, "IP": "*************", "Port": 22, "UserName": "root", "Password": "deltatau", "Type": 0, "Id": "ee64445f-f8b6-4942-bf15-3de9134c4817", "Name": "控制卡", "Category": 5, "IsEnabled": false, "Configuration": {"$type": "System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"}, "IsConnected": false, "IsUse": true}]}}, {"$id": "59", "$type": "MoonLight.Core.Modules.DeviceCategoryGroup, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null", "Category": 6, "CategoryName": "Folder", "Devices": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[MoonLight.Core.Devices.IDevice, MoonLight.Core, Version=*******, Culture=neutral, PublicKeyToken=null]], System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089", "$values": []}}]}