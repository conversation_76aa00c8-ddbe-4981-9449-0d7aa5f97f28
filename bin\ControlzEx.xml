<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ControlzEx</name>
    </assembly>
    <members>
        <member name="T:ControlzEx.Automation.Peers.TabControlExAutomationPeer">
            <summary>
                Automation-Peer for <see cref="T:ControlzEx.Controls.TabControlEx" />.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabControlExAutomationPeer.#ctor(System.Windows.Controls.TabControl)">
            <summary>
                Initializes a new instance.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabControlExAutomationPeer.CreateItemAutomationPeer(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:ControlzEx.Automation.Peers.TabItemExAutomationPeer">
            <summary>
                Automation-Peer for <see cref="T:System.Windows.Controls.TabItem" /> in <see cref="T:ControlzEx.Controls.TabControlEx" />.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabItemExAutomationPeer.#ctor(System.Object,System.Windows.Automation.Peers.TabControlAutomationPeer)">
            <summary>
                Initializes a new instance.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabItemExAutomationPeer.GetChildrenCore">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabItemExAutomationPeer.GetWrapper">
            <summary>
                Gets the real tab item.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.Badge"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.Badge">
            <summary>
            Gets or sets the Badge content to display.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontFamilyProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontFamily"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontFamily">
            <summary>
            The BadgeFontFamily property specifies the name of font family.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontStyleProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontStyle"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontStyle">
            <summary>
            The BadgeFontStyle property requests normal, italic, and oblique faces within a font family.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontWeightProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontWeight"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontWeight">
            <summary>
            The BadgeFontWeight property specifies the weight of the font.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontStretchProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontStretch"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontStretch">
            <summary>
            The BadgeFontStretch property selects a normal, condensed, or extended face from a font family.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontSizeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontSize"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontSize">
            <summary>
            The BadgeFontSize property specifies the size of the font.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeBackgroundProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeBackground"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeBackground">
            <summary>
            Gets or sets the background brush for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeForegroundProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeForeground"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeForeground">
            <summary>
            Gets or sets the foreground brush for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeBorderBrushProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeBorderBrush"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeBorderBrush">
            <summary>
            Gets or sets the border brush for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeBorderThicknessProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeBorderThickness"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeBorderThickness">
            <summary>
            Gets or sets the border thickness for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgePlacementModeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgePlacementMode"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgePlacementMode">
            <summary>
            Gets or sets the placement of the Badge relative to its content.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeMarginProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeMargin"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeMargin">
            <summary>
            Gets or sets a margin which can be used to make minor adjustments to the placement of the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeTemplateProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeTemplate"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> for the Badge
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeTemplateSelectorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeTemplateSelector"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeTemplateSelector">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Controls.DataTemplateSelector"/> for the Badge
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeStringFormatProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeStringFormat"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeStringFormat">
            <summary>
            Gets or sets a composite string that specifies how to format the Badge property if it is displayed as a string.
            </summary>
            <remarks> 
            This property is ignored if <seealso cref="P:ControlzEx.BadgedEx.BadgeTemplate"/> is set.
            </remarks>
        </member>
        <member name="F:ControlzEx.BadgedEx.IsBadgeSetProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.IsBadgeSet"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.IsBadgeSet">
            <summary>
            Indicates whether the Badge has content to display.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.GlowBrushProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowBrush"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowBrush">
            <summary>
            Gets or sets a brush which is used as the glow when the window is active.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.NonActiveGlowBrushProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.NonActiveGlowBrush"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.NonActiveGlowBrush">
            <summary>
            Gets or sets a brush which is used as the glow when the window is not active.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.IsGlowTransitionEnabledProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.IsGlowTransitionEnabled"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.IsGlowTransitionEnabled">
            <summary>
            Defines whether glow transitions should be used or not.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.ResizeBorderThicknessProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.ResizeBorderThickness"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.ResizeBorderThickness">
            <summary>
            Gets or sets resize border thickness.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.GlowWindowBehavior.OnDetaching">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Behaviors.GlowWindowBehavior.Update">
            <summary>
            Updates all glow windows (visible, hidden, collapsed)
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.GlowWindowBehavior.SetOpacityTo(System.Double)">
            <summary>
            Sets the opacity to all glow windows
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.GlowWindowBehavior.StartOpacityStoryboard">
            <summary>
            Starts the opacity storyboard 0 -> 1
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.GlowWindowBehavior.Show">
            <summary>
            Shows all glow windows
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.GlowWindowBehavior.Close">
            <summary>
            Closes all glow windows
            </summary>
        </member>
        <member name="T:ControlzEx.Behaviors.TextBoxInputMaskBehavior">
            <summary>
            Enables an InputMask for <see cref="T:System.Windows.Controls.TextBox"/> with 2 Properties: <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.InputMask"/>, <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.PromptChar"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.InputMaskProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.InputMask"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.PromptCharProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.PromptChar"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.ResetOnSpaceProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.ResetOnSpace"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.IgnoreSpaceProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.IgnoreSpace"/> dependency property.</summary>
        </member>
        <member name="M:ControlzEx.Behaviors.TextBoxInputMaskBehavior.Pasting(System.Object,System.Windows.DataObjectPastingEventArgs)">
            <summary>
            Pasting prüft ob korrekte Daten reingepastet werden
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.TextBoxInputMaskBehavior.TreatSelectedText">
            <summary>
            Falls eine Textauswahl vorliegt wird diese entsprechend behandelt.
            </summary>
        </member>
        <member name="T:ControlzEx.Behaviors.WindowChromeBehavior">
            <summary>
            With this class we can make custom window styles.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.windowHandle">
            <summary>Underlying HWND for the _window.</summary>
            <SecurityNote>
              Critical : Critical member
            </SecurityNote>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.hwndSource">
            <summary>Underlying HWND for the _window.</summary>
            <SecurityNote>
              Critical : Critical member provides access to HWND's window messages which are critical
            </SecurityNote>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThickness">
            <summary>
            Mirror property for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThickness"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThicknessProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThickness"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.IgnoreTaskbarOnMaximize">
            <summary>
            Defines if the Taskbar should be ignored when maximizing a Window.
            This only works with WindowStyle = None.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.IgnoreTaskbarOnMaximizeProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.IgnoreTaskbarOnMaximize"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.KeepBorderOnMaximize">
            <summary>
            Gets/sets if the border thickness value should be kept on maximize
            if the MaxHeight/MaxWidth of the window is less than the monitor resolution.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.KeepBorderOnMaximizeProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.KeepBorderOnMaximize"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.TryToBeFlickerFree">
            <summary>
            Gets or sets whether the resizing of the window should be tried in a way that does not cause flicker/jitter, especially when resizing from the left side.
            </summary>
            <remarks>
            Please note that setting this to <c>true</c> may cause resize lag and black areas appearing on some systems.
            </remarks>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.TryToBeFlickerFreeProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.TryToBeFlickerFree"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.IsNCActiveProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.IsNCActive"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.IsNCActive">
            <summary>
            Gets whether the non-client area is active or not.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.EnableMinimize">
            <summary>
            Gets or sets whether if the minimize button is visible and the minimize system menu is enabled.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.EnableMaxRestore">
            <summary>
            Gets or sets whether if the maximize/restore button is visible and the maximize/restore system menu is enabled.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.OnAttached">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.GetDefaultResizeBorderThickness">
            <summary>
            Gets the default resize border thicknes from the system parameters.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.OnCleanup">
            <summary>
            Occurs during the cleanup of this behavior.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.OnDetaching">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.AssociatedObject_Loaded(System.Object,System.Windows.RoutedEventArgs)">
            <summary>
            Is called when the associated object of this instance is loaded
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.HandleBorderAndResizeBorderThicknessDuringMaximize">
            <summary>
            This fix is needed because style triggers don't work if someone sets the value locally/directly on the window.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.#ctor">
            <SecurityNote>
              Critical : Store critical methods in critical callback table
              Safe     : Demands full trust permissions
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._OnChromePropertyChangedThatRequiresRepaint">
            <SecurityNote>
              Critical : Calls critical methods
              Safe     : Demands full trust permissions
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._ApplyNewCustomChrome">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.WindowProc(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Accesses critical _hwnd
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCUAHDRAWCAPTION(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleSYSCOMMAND(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCACTIVATE(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.AdjustWorkingAreaForAutoHide(System.IntPtr,ControlzEx.Standard.RECT)">
            <summary>
            This method handles the window size if the taskbar is set to auto-hide.
            </summary>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCCALCSIZE(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical Marshal.PtrToStructure
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCPAINT(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCHITTEST(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCRBUTTONUP(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical method
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleSIZE(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical method
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleWINDOWPOSCHANGING(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical Marshal.PtrToStructure
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleWINDOWPOSCHANGED(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical Marshal.PtrToStructure
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleGETMINMAXINFO(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleDWMCOMPOSITIONCHANGED(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleENTERSIZEMOVEForAnimation(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleEXITSIZEMOVEForAnimation(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleMOVEForRealSize(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleDPICHANGED(ControlzEx.Standard.WM,System.IntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._ModifyStyle(ControlzEx.Standard.WS,ControlzEx.Standard.WS)">
            <summary>Add and remove a native WindowStyle from the HWND.</summary>
            <param name="removeStyle">The styles to be removed.  These can be bitwise combined.</param>
            <param name="addStyle">The styles to be added.  These can be bitwise combined.</param>
            <returns>Whether the styles of the HWND were modified as a result of this call.</returns>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._GetHwndState">
            <summary>
            Get the WindowState as the native HWND knows it to be.  This isn't necessarily the same as what Window thinks.
            </summary>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._GetWindowRect">
            <summary>
            Get the bounding rectangle for the window in physical coordinates.
            </summary>
            <returns>The bounding rectangle for the window.</returns>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._UpdateSystemMenu(System.Nullable{System.Windows.WindowState})">
            <summary>
            Update the items in the system menu based on the current, or assumed, WindowState.
            </summary>
            <param name="assumeState">
            The state to assume that the Window is in.  This can be null to query the Window's state.
            </param>
            <remarks>
            We want to update the menu while we have some control over whether the caption will be repainted.
            </remarks>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._UpdateFrameState(System.Boolean)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._ClearRegion">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._GetClientRectRelativeToWindowRect(System.IntPtr)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._SetRegion(System.Nullable{ControlzEx.Standard.WINDOWPOS})">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._CreateRectRgn(System.Windows.Rect)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.hitTestBorders">
            <summary>
            Matrix of the HT values to return when responding to NC window messages.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._RestoreStandardChromeState(System.Boolean)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._RestoreHrgn">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="T:ControlzEx.Controls.GlowWindow">
            <summary>
            GlowWindow
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.GlowWindow.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:ControlzEx.Controls.TabControlEx">
             <summary>
             The standard WPF TabControl is quite bad in the fact that it only
             even contains the current TabItem in the VisualTree, so if you
             have complex views it takes a while to re-create the view each tab
             selection change.Which makes the standard TabControl very sticky to
             work with. This class along with its associated ControlTemplate
             allow all TabItems to remain in the VisualTree without it being Sticky.
             It does this by keeping all TabItem content in the VisualTree but
             hides all inactive TabItem content, and only keeps the active TabItem
             content shown.
             
             Acknowledgement
                 Eric Burke
                     http://eric.burke.name/dotnetmania/2009/04/26/22.09.28
                 Sacha Barber: https://sachabarbs.wordpress.com/about-me/
                     http://stackoverflow.com/a/10210889/920384
                 http://stackoverflow.com/a/7838955/920384
             </summary>
             <remarks>
             We use two attached properties to later recognize the content presenters we generated.
             We need the OwningItem because the TabItem associated with an item can later change.
            
             We need the OwningTabItem to reduce the amount of lookups we have to do.
             </remarks>
        </member>
        <member name="F:ControlzEx.Controls.TabControlEx.ChildContentVisibilityProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Controls.TabControlEx.ChildContentVisibility"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Controls.TabControlEx.TabPanelVisibilityProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Controls.TabControlEx.TabPanelVisibility"/> dependency property.</summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.GetOwningTabItem(System.Windows.DependencyObject)">
            <summary>Helper for getting <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> from <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to read <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> from.</param>
            <returns>OwningTabItem property value.</returns>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.SetOwningTabItem(System.Windows.DependencyObject,System.Windows.Controls.TabItem)">
            <summary>Helper for setting <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> on <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to set <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> on.</param>
            <param name="value">OwningTabItem property value.</param>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.SetOwningItem(System.Windows.DependencyObject,System.Object)">
            <summary>Helper for setting <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> on <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to set <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> on.</param>
            <param name="value">OwningItem property value.</param>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.GetOwningItem(System.Windows.DependencyObject)">
            <summary>Helper for getting <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> from <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to read <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> from.</param>
            <returns>OwningItem property value.</returns>
        </member>
        <member name="F:ControlzEx.Controls.TabControlEx.MoveFocusToContentWhenSelectionChangesProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Controls.TabControlEx.MoveFocusToContentWhenSelectionChanges"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.Controls.TabControlEx.MoveFocusToContentWhenSelectionChanges">
            <summary>
            Gets or sets whether keyboard focus should be moved to the content area when the selected item changes.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.#ctor">
            <summary>
            Initializes a new instance.
            </summary>
        </member>
        <member name="P:ControlzEx.Controls.TabControlEx.TabPanelVisibility">
            <summary>
            Defines if the TabPanel (Tab-Header) are visible.
            </summary>
        </member>
        <member name="P:ControlzEx.Controls.TabControlEx.ChildContentVisibility">
            <summary>
            Gets or sets the child content visibility.
            </summary>
            <value>
            The child content visibility.
            </value>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
            <summary>
            When the items change we remove any generated panel children and add any new ones as necessary.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnSelectionChanged(System.Windows.Controls.SelectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.GetSelectedTabItem">
            <summary>
            Copied from <see cref="T:System.Windows.Controls.TabControl"/>. wish it were protected in that class instead of private.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.RefreshItemsHolder">
            <summary>
            Clears all current children by calling <see cref="M:ControlzEx.Controls.TabControlEx.ClearItemsHolder"/> and calls <see cref="M:ControlzEx.Controls.TabControlEx.UpdateSelectedContent"/> afterwards.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.UpdateSelectedContent">
            <summary>
            Generate a ContentPresenter for the selected item and control the visibility of already created presenters.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.CreateChildContentPresenterIfRequired(System.Object,System.Windows.Controls.TabItem)">
            <summary>
            Create the child ContentPresenter for the given item (could be data or a TabItem) if none exists.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.FindChildContentPresenter(System.Object,System.Windows.Controls.TabItem)">
            <summary>
            Find the <see cref="T:System.Windows.Controls.ContentPresenter"/> for the given object. Data could be a TabItem or a piece of data.
            </summary>
        </member>
        <member name="M:ControlzEx.Internal.ResourceDictionaryHelper.GetValueFromKey(System.Windows.ResourceDictionary,System.Object)">
            <summary>
            Gets the value associated with <paramref name="key"/> directly from <paramref name="resourceDictionary"/>.
            </summary>
        </member>
        <member name="M:ControlzEx.Internal.ResourceDictionaryHelper.ContainsKey(System.Windows.ResourceDictionary,System.Object)">
            <summary>
            Checks if <paramref name="resourceDictionary"/> directly contains <paramref name="key"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.KeyboardNavigationEx">
            <summary>
            Helper class for a common focusing problem.
            The focus itself isn't the problem. If we use the common focusing methods the control get the focus
            but it doesn't get the focus visual style.
            The KeyboardNavigation class handles the visual style only if the control get the focus from a keyboard
            device or if the SystemParameters.KeyboardCues is true.
            </summary>
        </member>
        <member name="P:ControlzEx.KeyboardNavigationEx.Instance">
            <summary>
            Gets the KeyboardNavigationEx singleton instance.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.ShowFocusVisualInternal">
            <summary>
            Shows the focus visual of the current focused UI element.
            Works only together with AlwaysShowFocusVisual property.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.Focus(System.Windows.UIElement)">
            <summary>
            Focuses the specified element and shows the focus visual style.
            </summary>
            <param name="element">The element which will be focused.</param>
        </member>
        <member name="F:ControlzEx.KeyboardNavigationEx.AlwaysShowFocusVisualProperty">
            <summary>
            Attached DependencyProperty for setting AlwaysShowFocusVisual for a UI element.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.GetAlwaysShowFocusVisual(System.Windows.UIElement)">
            <summary>
            Gets a the value which indicates if the UI element always show the focus visual style.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.SetAlwaysShowFocusVisual(System.Windows.UIElement,System.Boolean)">
            <summary>
            Sets a the value which indicates if the UI element always show the focus visual style.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ApplicationAssociationRegistration">
            <summary>IID_IApplicationAssociationRegistration</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ConnectionPoint">
            <summary>IID_IConnectionPoint</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ConnectionPointContainer">
            <summary>IID_IConnectionPointContainer</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.EnumConnectionPoints">
            <summary>IID_IEnumConnectionPoints</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.EnumConnections">
            <summary>IID_IEnumConnections</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.EnumIdList">
            <summary>IID_IEnumIDList</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.EnumObjects">
            <summary>IID_IEnumObjects</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.FileDialog">
            <summary>IID_IFileDialog</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.FileDialogEvents">
            <summary>IID_IFileDialogEvents</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.FileOpenDialog">
            <summary>IID_IFileOpenDialog</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.FileSaveDialog">
            <summary>IID_IFileSaveDialog</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.HtmlDocument">
            <summary>IID_IHTMLDocument</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.HtmlDocument2">
            <summary>IID_IHTMLDocument2</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ModalWindow">
            <summary>IID_IModalWindow</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ObjectArray">
            <summary>IID_IObjectArray</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ObjectCollection">
            <summary>IID_IObjectCollection</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.PropertyNotifySink">
            <summary>IID_IPropertyNotifySink</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.PropertyStore">
            <summary>IID_IPropertyStore</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ServiceProvider">
            <summary>IID_IServiceProvider</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ShellFolder">
            <summary>IID_IShellFolder</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ShellLink">
            <summary>IID_IShellLink</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ShellItem">
            <summary>IID_IShellItem</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ShellItem2">
            <summary>IID_IShellItem2</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ShellItemArray">
            <summary>IID_IShellItemArray</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.TaskbarList">
            <summary>IID_ITaskbarList</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.TaskbarList2">
            <summary>IID_ITaskbarList2</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.Unknown">
            <summary>IID_IUnknown</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WebBrowser2">
            <summary>IID_IWebBrowser2</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WebBrowserEvents">
            <summary>DIID_DWebBrowserEvents</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WebBrowserEvents2">
            <summary>IID_DWebBrowserEvents2</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICBitmapDecoder">
            <summary>IID_IWICBitmapDecoder</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICBitmapFlipRotator">
            <summary>IID_IWICBitmapFlipRotator</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICBitmapFrameDecode">
            <summary>IID_IWICBitmapFrameDecode</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICBitmap">
            <summary>IID_IWICBitmap</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICBitmapSource">
            <summary>IID_IWICBitmapSource</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICFormatConverter">
            <summary>IID_IWICFormatConverter</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICImagingFactory">
            <summary>IID_IWICImagingFactory</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.WICStream">
            <summary>IID_IWICStream</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ApplicationDestinations">
            <summary>IID_IApplicationDestinations</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ApplicationDocumentLists">
            <summary>IID_IApplicationDocumentLists</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.CustomDestinationList">
            <summary>IID_ICustomDestinationList</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ObjectWithAppUserModelId">
            <summary>IID_IObjectWithAppUserModelID</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.ObjectWithProgId">
            <summary>IID_IObjectWithProgID</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.TaskbarList3">
            <summary>IID_ITaskbarList3</summary>
        </member>
        <member name="F:ControlzEx.Standard.IID.TaskbarList4">
            <summary>IID_ITaskbarList4</summary>
        </member>
        <member name="F:ControlzEx.Standard.SID.SWebBrowserApp">
            <summary>SID_SWebBrowserApp</summary>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.ApplicationAssociationRegistration">
            <summary>CLSID_ApplicationAssociationRegistration</summary>
            <remarks>IID_IApplicationAssociationRegistration</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.DragDropHelper">
            <summary>CLSID_DragDropHelper</summary>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.FileOpenDialog">
            <summary>CLSID_FileOpenDialog</summary>
            <remarks>IID_IFileOpenDialog</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.FileSaveDialog">
            <summary>CLSID_FileSaveDialog</summary>
            <remarks>IID_IFileSaveDialog</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.TaskbarList">
            <summary>CLSID_TaskbarList</summary>
            <remarks>IID_ITaskbarList</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.EnumerableObjectCollection">
            <summary>CLSID_EnumerableObjectCollection</summary>
            <remarks>IID_IEnumObjects.</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.ShellLink">
            <summary>CLSID_ShellLink</summary>
            <remarks>IID_IShellLink</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.WICImagingFactory">
            <summary>CLSID_WICImagingFactory</summary>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.DestinationList">
            <summary>CLSID_DestinationList</summary>
            <remarks>IID_ICustomDestinationList</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.ApplicationDestinations">
            <summary>CLSID_ApplicationDestinations</summary>
            <remarks>IID_IApplicationDestinations</remarks>
        </member>
        <member name="F:ControlzEx.Standard.CLSID.ApplicationDocumentLists">
            <summary>CLSID_ApplicationDocumentLists</summary>
            <remarks>IID_IApplicationDocumentLists</remarks>
        </member>
        <member name="T:ControlzEx.Standard.Assert">
            <summary>A static class for verifying assumptions.</summary>
        </member>
        <member name="T:ControlzEx.Standard.Assert.EvaluateFunction">
            <summary>A function signature for Assert.Evaluate.</summary>
        </member>
        <member name="T:ControlzEx.Standard.Assert.ImplicationFunction">
            <summary>A function signature for Assert.Implies.</summary>
            <returns>Returns the truth of a predicate.</returns>
        </member>
        <member name="M:ControlzEx.Standard.Assert.Evaluate(ControlzEx.Standard.Assert.EvaluateFunction)">
            <summary>
            Executes the specified argument.
            </summary>
            <param name="argument">The function to execute.</param>
        </member>
        <member name="M:ControlzEx.Standard.Assert.Equals``1(``0,``0)">
            <summary>Obsolete: Use Standard.Assert.AreEqual instead of Assert.Equals</summary>
            <typeparam name="T">The generic type to compare for equality.</typeparam>
            <param name="expected">The first generic type data to compare.  This is is the expected value.</param>
            <param name="actual">The second generic type data to compare.  This is the actual value.</param>
        </member>
        <member name="M:ControlzEx.Standard.Assert.AreEqual``1(``0,``0)">
            <summary>
            Verifies that two generic type data are equal.  The assertion fails if they are not.
            </summary>
            <typeparam name="T">The generic type to compare for equality.</typeparam>
            <param name="expected">The first generic type data to compare.  This is is the expected value.</param>
            <param name="actual">The second generic type data to compare.  This is the actual value.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.AreNotEqual``1(``0,``0)">
            <summary>
            Verifies that two generic type data are not equal.  The assertion fails if they are.
            </summary>
            <typeparam name="T">The generic type to compare for inequality.</typeparam>
            <param name="notExpected">The first generic type data to compare.  This is is the value that's not expected.</param>
            <param name="actual">The second generic type data to compare.  This is the actual value.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.Implies(System.Boolean,System.Boolean)">
            <summary>
            Verifies that if the specified condition is true, then so is the result.
            The assertion fails if the condition is true but the result is false.
            </summary>
            <param name="condition">if set to <c>true</c> [condition].</param>
            <param name="result">
            A second Boolean statement.  If the first was true then so must this be.
            If the first statement was false then the value of this is ignored.
            </param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.Implies(System.Boolean,ControlzEx.Standard.Assert.ImplicationFunction)">
            <summary>
            Lazy evaluation overload.  Verifies that if a condition is true, then so is a secondary value.
            </summary>
            <param name="condition">The conditional value.</param>
            <param name="result">A function to be evaluated for truth if the condition argument is true.</param>
            <remarks>
            This overload only evaluates the result if the first condition is true.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsNeitherNullNorEmpty(System.String)">
            <summary>
            Verifies that a string has content.  I.e. it is not null and it is not empty.
            </summary>
            <param name="value">The string to verify.</param>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsNeitherNullNorWhitespace(System.String)">
            <summary>
            Verifies that a string has content.  I.e. it is not null and it is not purely whitespace.
            </summary>
            <param name="value">The string to verify.</param>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsNotNull``1(``0)">
            <summary>
            Verifies the specified value is not null.  The assertion fails if it is.
            </summary>
            <typeparam name="T">The generic reference type.</typeparam>
            <param name="value">The value to check for nullness.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsFalse(System.Boolean)">
            <summary>
            Verifies that the specified condition is false.  The assertion fails if it is true.
            </summary>
            <param name="condition">The expression that should be <c>false</c>.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsFalse(System.Boolean,System.String)">
            <summary>
            Verifies that the specified condition is false.  The assertion fails if it is true.
            </summary>
            <param name="condition">The expression that should be <c>false</c>.</param>
            <param name="message">The message to display if the condition is <c>true</c>.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsTrue(System.Boolean)">
            <summary>
            Verifies that the specified condition is true.  The assertion fails if it is not.
            </summary>
            <param name="condition">A condition that is expected to be <c>true</c>.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsTrue(System.Boolean,System.String)">
            <summary>
            Verifies that the specified condition is true.  The assertion fails if it is not.
            </summary>
            <param name="condition">A condition that is expected to be <c>true</c>.</param>
            <param name="message">The message to write in case the condition is <c>false</c>.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.Fail">
            <summary>
            This line should never be executed.  The assertion always fails.
            </summary>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.Fail(System.String)">
            <summary>
            This line should never be executed.  The assertion always fails.
            </summary>
            <param name="message">The message to display if this function is executed.</param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsNull``1(``0)">
            <summary>
            Verifies that the specified object is null.  The assertion fails if it is not.
            </summary>
            <param name="item">The item to verify is null.</param>
        </member>
        <member name="M:ControlzEx.Standard.Assert.BoundedDoubleInc(System.Double,System.Double,System.Double)">
            <summary>
            Verifies that the specified value is within the expected range.  The assertion fails if it isn't.
            </summary>
            <param name="lowerBoundInclusive">The lower bound inclusive value.</param>
            <param name="value">The value to verify.</param>
            <param name="upperBoundInclusive">The upper bound inclusive value.</param>
        </member>
        <member name="M:ControlzEx.Standard.Assert.BoundedInteger(System.Int32,System.Int32,System.Int32)">
            <summary>
            Verifies that the specified value is within the expected range.  The assertion fails if it isn't.
            </summary>
            <param name="lowerBoundInclusive">The lower bound inclusive value.</param>
            <param name="value">The value to verify.</param>
            <param name="upperBoundExclusive">The upper bound exclusive value.</param>
        </member>
        <member name="M:ControlzEx.Standard.Assert.IsApartmentState(System.Threading.ApartmentState)">
            <summary>
            Verify the current thread's apartment state is what's expected.  The assertion fails if it isn't
            </summary>
            <param name="expectedState">
            The expected apartment state for the current thread.
            </param>
            <remarks>This breaks into the debugger in the case of a failed assertion.</remarks>
        </member>
        <member name="T:ControlzEx.Standard.DoubleUtilities">
            <summary>
            DoubleUtil uses fixed eps to provide fuzzy comparison functionality for doubles.
            Note that FP noise is a big problem and using any of these compare 
            methods is not a complete solution, but rather the way to reduce 
            the probability of repeating unnecessary work.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.DoubleUtilities.Epsilon">
            <summary>
            Epsilon - more or less random, more or less small number.
            </summary>
        </member>
        <member name="M:ControlzEx.Standard.DoubleUtilities.AreClose(System.Double,System.Double)">
            <summary>
            AreClose returns whether or not two doubles are "close".  That is, whether or 
            not they are within epsilon of each other.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false. 
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the AreClose comparision.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DoubleUtilities.IsStrictlyLessThan(System.Double,System.Double)">
            <summary>
            LessThan returns whether or not the first double is less than the second double.
            That is, whether or not the first is strictly less than *and* not within epsilon of
            the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the LessThan comparision.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DoubleUtilities.IsStrictlyGreaterThan(System.Double,System.Double)">
            <summary>
            GreaterThan returns whether or not the first double is greater than the second double.
            That is, whether or not the first is strictly greater than *and* not within epsilon of
            the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the GreaterThan comparision.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DoubleUtilities.IsLessThanOrCloseTo(System.Double,System.Double)">
            <summary>
            LessThanOrClose returns whether or not the first double is less than or close to
            the second double.  That is, whether or not the first is strictly less than or within
            epsilon of the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the LessThanOrClose comparision.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DoubleUtilities.IsGreaterThanOrCloseTo(System.Double,System.Double)">
            <summary>
            GreaterThanOrClose returns whether or not the first double is greater than or close to
            the second double.  That is, whether or not the first is strictly greater than or within
            epsilon of the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the GreaterThanOrClose comparision.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DoubleUtilities.IsFinite(System.Double)">
            <summary>
            Test to see if a double is a finite number (is not NaN or Infinity).
            </summary>
            <param name='value'>The value to test.</param>
            <returns>Whether or not the value is a finite number.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DoubleUtilities.IsValidSize(System.Double)">
            <summary>
            Test to see if a double a valid size value (is finite and > 0).
            </summary>
            <param name='value'>The value to test.</param>
            <returns>Whether or not the value is a valid size value.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DpiHelper.LogicalPixelsToDevice(System.Windows.Point,System.Double,System.Double)">
            <summary>
            Convert a point in device independent pixels (1/96") to a point in the system coordinates.
            </summary>
            <param name="logicalPoint">A point in the logical coordinate system.</param>
            <returns>Returns the parameter converted to the system's coordinates.</returns>
        </member>
        <member name="M:ControlzEx.Standard.DpiHelper.DevicePixelsToLogical(System.Windows.Point,System.Double,System.Double)">
            <summary>
            Convert a point in system coordinates to a point in device independent pixels (1/96").
            </summary>
            <param name="devicePoint">A point in the physical coordinate system.</param>
            <returns>Returns the parameter converted to the device independent coordinate system.</returns>
        </member>
        <member name="T:ControlzEx.Standard.Win32Error">
            <summary>
            Wrapper for common Win32 status codes.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_SUCCESS">
            <summary>The operation completed successfully.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_INVALID_FUNCTION">
            <summary>Incorrect function.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_FILE_NOT_FOUND">
            <summary>The system cannot find the file specified.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_PATH_NOT_FOUND">
            <summary>The system cannot find the path specified.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_TOO_MANY_OPEN_FILES">
            <summary>The system cannot open the file.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_ACCESS_DENIED">
            <summary>Access is denied.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_INVALID_HANDLE">
            <summary>The handle is invalid.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_OUTOFMEMORY">
            <summary>Not enough storage is available to complete this operation.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_NO_MORE_FILES">
            <summary>There are no more files.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_SHARING_VIOLATION">
            <summary>The process cannot access the file because it is being used by another process.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_INVALID_PARAMETER">
            <summary>The parameter is incorrect.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_INSUFFICIENT_BUFFER">
            <summary>The data area passed to a system call is too small.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_NESTING_NOT_ALLOWED">
            <summary>Cannot nest calls to LoadModule.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_KEY_DELETED">
            <summary>Illegal operation attempted on a registry key that has been marked for deletion.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_NOT_FOUND">
            <summary>Element not found.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_NO_MATCH">
            <summary>There was no match for the specified key in the index.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_BAD_DEVICE">
            <summary>An invalid device was specified.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_CANCELLED">
            <summary>The operation was canceled by the user.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_CANNOT_FIND_WND_CLASS">
            <summary>Cannot find window class.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_CLASS_ALREADY_EXISTS">
            <summary>The window class was already registered.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Win32Error.ERROR_INVALID_DATATYPE">
            <summary>The specified datatype is invalid.</summary>
        </member>
        <member name="M:ControlzEx.Standard.Win32Error.#ctor(System.Int32)">
            <summary>
            Create a new Win32 error.
            </summary>
            <param name="i">The integer value of the error.</param>
        </member>
        <member name="M:ControlzEx.Standard.Win32Error.op_Explicit(ControlzEx.Standard.Win32Error)~ControlzEx.Standard.HRESULT">
            <summary>Performs HRESULT_FROM_WIN32 conversion.</summary>
            <param name="error">The Win32 error being converted to an HRESULT.</param>
            <returns>The equivilent HRESULT value.</returns>
        </member>
        <member name="M:ControlzEx.Standard.Win32Error.ToHRESULT">
            <summary>Performs HRESULT_FROM_WIN32 conversion.</summary>
            <returns>The equivilent HRESULT value.</returns>
        </member>
        <member name="M:ControlzEx.Standard.Win32Error.GetLastError">
            <summary>Performs the equivalent of Win32's GetLastError()</summary>
            <returns>A Win32Error instance with the result of the native GetLastError</returns>
        </member>
        <member name="M:ControlzEx.Standard.Win32Error.op_Equality(ControlzEx.Standard.Win32Error,ControlzEx.Standard.Win32Error)">
            <summary>
            Compare two Win32 error codes for equality.
            </summary>
            <param name="errLeft">The first error code to compare.</param>
            <param name="errRight">The second error code to compare.</param>
            <returns>Whether the two error codes are the same.</returns>
        </member>
        <member name="M:ControlzEx.Standard.Win32Error.op_Inequality(ControlzEx.Standard.Win32Error,ControlzEx.Standard.Win32Error)">
            <summary>
            Compare two Win32 error codes for inequality.
            </summary>
            <param name="errLeft">The first error code to compare.</param>
            <param name="errRight">The second error code to compare.</param>
            <returns>Whether the two error codes are not the same.</returns>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Null">
            <summary>FACILITY_NULL</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Rpc">
            <summary>FACILITY_RPC</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Dispatch">
            <summary>FACILITY_DISPATCH</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Storage">
            <summary>FACILITY_STORAGE</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Itf">
            <summary>FACILITY_ITF</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Win32">
            <summary>FACILITY_WIN32</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Windows">
            <summary>FACILITY_WINDOWS</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Control">
            <summary>FACILITY_CONTROL</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.Ese">
            <summary>MSDN doced facility code for ESE errors.</summary>
        </member>
        <member name="F:ControlzEx.Standard.Facility.WinCodec">
            <summary>FACILITY_WINCODEC (WIC)</summary>
        </member>
        <member name="T:ControlzEx.Standard.HRESULT">
            <summary>Wrapper for HRESULT status codes.</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.S_OK">
            <summary>S_OK</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.S_FALSE">
            <summary>S_FALSE</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_PENDING">
            <summary>E_PENDING</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_NOTIMPL">
            <summary>E_NOTIMPL</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_NOINTERFACE">
            <summary>E_NOINTERFACE</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_POINTER">
            <summary>E_POINTER</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_ABORT">
            <summary>E_ABORT</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_FAIL">
            <summary>E_FAIL</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_UNEXPECTED">
            <summary>E_UNEXPECTED</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.STG_E_INVALIDFUNCTION">
            <summary>STG_E_INVALIDFUNCTION</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.REGDB_E_CLASSNOTREG">
            <summary>REGDB_E_CLASSNOTREG</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.DESTS_E_NO_MATCHING_ASSOC_HANDLER">
            <summary>DESTS_E_NO_MATCHING_ASSOC_HANDLER.  Win7 internal error code for Jump Lists.</summary>
            <remarks>There is no Assoc Handler for the given item registered by the specified application.</remarks>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.DESTS_E_NORECDOCS">
            <summary>DESTS_E_NORECDOCS.  Win7 internal error code for Jump Lists.</summary>
            <remarks>The given item is excluded from the recent docs folder by the NoRecDocs bit on its registration.</remarks>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.DESTS_E_NOTALLCLEARED">
            <summary>DESTS_E_NOTALLCLEARED.  Win7 internal error code for Jump Lists.</summary>
            <remarks>Not all of the items were successfully cleared</remarks>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_ACCESSDENIED">
            <summary>E_ACCESSDENIED</summary>
            <remarks>Win32Error ERROR_ACCESS_DENIED.</remarks>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_OUTOFMEMORY">
            <summary>E_OUTOFMEMORY</summary>
            <remarks>Win32Error ERROR_OUTOFMEMORY.</remarks>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.E_INVALIDARG">
            <summary>E_INVALIDARG</summary>
            <remarks>Win32Error ERROR_INVALID_PARAMETER.</remarks>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.INTSAFE_E_ARITHMETIC_OVERFLOW">
            <summary>INTSAFE_E_ARITHMETIC_OVERFLOW</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.COR_E_OBJECTDISPOSED">
            <summary>COR_E_OBJECTDISPOSED</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.WC_E_GREATERTHAN">
            <summary>WC_E_GREATERTHAN</summary>
        </member>
        <member name="F:ControlzEx.Standard.HRESULT.WC_E_SYNTAX">
            <summary>WC_E_SYNTAX</summary>
        </member>
        <member name="M:ControlzEx.Standard.HRESULT.#ctor(System.UInt32)">
            <summary>
            Create an HRESULT from an integer value.
            </summary>
            <param name="i"></param>
        </member>
        <member name="M:ControlzEx.Standard.HRESULT.op_Explicit(ControlzEx.Standard.HRESULT)~System.Int32">
            <summary>
            Convert an HRESULT to an int.  Used for COM interface declarations out of our control.
            </summary>
        </member>
        <member name="P:ControlzEx.Standard.HRESULT.Facility">
            <summary>
            retrieve HRESULT_FACILITY
            </summary>
        </member>
        <member name="P:ControlzEx.Standard.HRESULT.Code">
            <summary>
            retrieve HRESULT_CODE
            </summary>
        </member>
        <member name="M:ControlzEx.Standard.HRESULT.ToString">
            <summary>
            Get a string representation of this HRESULT.
            </summary>
            <returns></returns>
        </member>
        <member name="M:ControlzEx.Standard.HRESULT.ThrowLastError">
            <summary>
            Convert the result of Win32 GetLastError() into a raised exception.
            </summary>
        </member>
        <member name="M:ControlzEx.Standard.MonitorHelper.GetMonitorInfoFromPoint">
            <summary>
            Gets the monitor information from the current cursor position.
            </summary>
            <returns>The monitor information.</returns>
        </member>
        <member name="M:ControlzEx.Standard.MonitorHelper.TryGetMonitorInfoFromPoint(ControlzEx.Standard.MONITORINFO@)">
            <summary>
            Gets the monitor information from the current cursor position.
            </summary>
            <returns>True when getting the monitor information was successful.</returns>
        </member>
        <member name="T:ControlzEx.Standard.HCF">
            <summary>
            HIGHCONTRAST flags
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.DROPIMAGETYPE">
            <summary>
            DROPIMAGE_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.BI">
            <summary>
            BITMAPINFOHEADER Compression type.  BI_*.
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.RGN">
            <summary>
            CombingRgn flags.  RGN_*
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.RGN.AND">
            <summary>
            Creates the intersection of the two combined regions.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.RGN.OR">
            <summary>
            Creates the union of two combined regions.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.RGN.XOR">
            <summary>
            Creates the union of two combined regions except for any overlapping areas.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.RGN.DIFF">
            <summary>
            Combines the parts of hrgnSrc1 that are not part of hrgnSrc2.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.RGN.COPY">
            <summary>
            Creates a copy of the region identified by hrgnSrc1.
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.OLECMDEXECOPT">
            <summary>
            For IWebBrowser2.  OLECMDEXECOPT_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.OLECMDF">
            <summary>
            For IWebBrowser2.  OLECMDF_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.OLECMDID">
            <summary>
            For IWebBrowser2.  OLECMDID_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.READYSTATE">
            <summary>
            For IWebBrowser2.  READYSTATE_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.DOGIF">
            <summary>
            DATAOBJ_GET_ITEM_FLAGS.  DOGIF_*.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.ErrorModes.Default">
            <summary>Use the system default, which is to display all error dialog boxes.</summary>
        </member>
        <member name="F:ControlzEx.Standard.ErrorModes.FailCriticalErrors">
            <summary>
            The system does not display the critical-error-handler message box. 
            Instead, the system sends the error to the calling process.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.ErrorModes.NoGpFaultErrorBox">
            <summary>
            64-bit Windows:  The system automatically fixes memory alignment faults and makes them 
            invisible to the application. It does this for the calling process and any descendant processes.
            After this value is set for a process, subsequent attempts to clear the value are ignored.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.ErrorModes.NoAlignmentFaultExcept">
            <summary>
            The system does not display the general-protection-fault message box. 
            This flag should only be set by debugging applications that handle general 
            protection (GP) faults themselves with an exception handler.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.ErrorModes.NoOpenFileErrorBox">
            <summary>
            The system does not display a message box when it fails to find a file. 
            Instead, the error is returned to the calling process.
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.HT">
            <summary>
            Non-client hit test values, HT*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.GCLP">
            <summary>
            GetClassLongPtr values, GCLP_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.GW">
            <summary>
            https://docs.microsoft.com/en-us/windows/desktop/api/winuser/nf-winuser-getwindow
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.GWL">
            <summary>
            GetWindowLongPtr values, GWL_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.SM">
            <summary>
            SystemMetrics.  SM_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.SPI">
            <summary>
            SystemParameterInfo values, SPI_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.SPIF">
            <summary>
            SystemParameterInfo flag values, SPIF_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.CS">
            <summary>
            CS_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.WS">
            <summary>
            WindowStyle values, WS_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.WM">
            <summary>
            Window message values, WM_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.WS_EX">
            <summary>
            Window style extended values, WS_EX_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.DeviceCap">
            <summary>
            GetDeviceCaps nIndex values.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.DeviceCap.BITSPIXEL">
            <summary>Number of bits per pixel
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.DeviceCap.PLANES">
            <summary>
            Number of planes
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.DeviceCap.LOGPIXELSX">
            <summary>
            Logical pixels inch in X
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.DeviceCap.LOGPIXELSY">
            <summary>
            Logical pixels inch in Y
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.FOF">
            <summary>
            "FILEOP_FLAGS", FOF_*.
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.MF">
            <summary>
            EnableMenuItem uEnable values, MF_*
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.MF.DOES_NOT_EXIST">
            <summary>
            Possible return value for EnableMenuItem
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.WINDOWTHEMEATTRIBUTETYPE">
            <summary>Specifies the type of visual style attribute to set on a window.</summary>
        </member>
        <member name="F:ControlzEx.Standard.WINDOWTHEMEATTRIBUTETYPE.WTA_NONCLIENT">
            <summary>Non-client area window attributes will be set.</summary>
        </member>
        <member name="T:ControlzEx.Standard.DWMFLIP3D">
            <summary>
            DWMFLIP3DWINDOWPOLICY.  DWMFLIP3D_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.DWMNCRP">
            <summary>
            DWMNCRENDERINGPOLICY. DWMNCRP_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.DWMWA">
            <summary>
            DWMWINDOWATTRIBUTE.  DWMWA_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.WTNCA">
            <summary>
            WindowThemeNonClientAttributes
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.WTNCA.NODRAWCAPTION">
            <summary>Prevents the window caption from being drawn.</summary>
        </member>
        <member name="F:ControlzEx.Standard.WTNCA.NODRAWICON">
            <summary>Prevents the system icon from being drawn.</summary>
        </member>
        <member name="F:ControlzEx.Standard.WTNCA.NOSYSMENU">
            <summary>Prevents the system icon menu from appearing.</summary>
        </member>
        <member name="F:ControlzEx.Standard.WTNCA.NOMIRRORHELP">
            <summary>Prevents mirroring of the question mark, even in right-to-left (RTL) layout.</summary>
        </member>
        <member name="F:ControlzEx.Standard.WTNCA.VALIDBITS">
            <summary> A mask that contains all the valid bits.</summary>
        </member>
        <member name="T:ControlzEx.Standard.SWP">
            <summary>
            SetWindowPos options
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.ASYNCWINDOWPOS">
            <summary>
            If the calling thread and the thread that owns the window are attached to different input queues, the system posts the request to the thread that owns the window. This prevents the calling thread from blocking its execution while other threads process the request.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.DEFERERASE">
            <summary>
            Prevents generation of the WM_SYNCPAINT message.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.DRAWFRAME">
            <summary>
            Draws a frame (defined in the window's class description) around the window.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.FRAMECHANGED">
            <summary>
            Applies new frame styles set using the SetWindowLong function. Sends a WM_NCCALCSIZE message to the window, even if the window's size is not being changed. If this flag is not specified, WM_NCCALCSIZE is sent only when the window's size is being changed.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.HIDEWINDOW">
            <summary>
            Hides the window.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOACTIVATE">
            <summary>
            Does not activate the window. If this flag is not set, the window is activated and moved to the top of either the topmost or non-topmost group (depending on the setting of the hWndInsertAfter parameter).
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOCOPYBITS">
            <summary>
            Discards the entire contents of the client area. If this flag is not specified, the valid contents of the client area are saved and copied back into the client area after the window is sized or repositioned.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOMOVE">
            <summary>
            Retains the current position (ignores X and Y parameters).
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOOWNERZORDER">
            <summary>
            Does not change the owner window's position in the Z order.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOREDRAW">
            <summary>
            Does not redraw changes. If this flag is set, no repainting of any kind occurs. This applies to the client area, the nonclient area (including the title bar and scroll bars), and any part of the parent window uncovered as a result of the window being moved. When this flag is set, the application must explicitly invalidate or redraw any parts of the window and parent window that need redrawing.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOREPOSITION">
            <summary>
            Same as the SWP_NOOWNERZORDER flag.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOSENDCHANGING">
            <summary>
            Prevents the window from receiving the WM_WINDOWPOSCHANGING message.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOSIZE">
            <summary>
            Retains the current size (ignores the cx and cy parameters).
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.NOZORDER">
            <summary>
            Retains the current Z order (ignores the hWndInsertAfter parameter).
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SWP.SHOWWINDOW">
            <summary>
            Displays the window.
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.SW">
            <summary>
            ShowWindow options
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SC.F_ISSECURE">
            <summary>
            SCF_ISSECURE
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.Status">
            <summary>
            GDI+ Status codes
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.MSGFLT">
            <summary>
            MSGFLT_*.  New in Vista.  Realiased in Windows 7.
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.NIM">
            <summary>
            Shell_NotifyIcon messages.  NIM_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.SHARD">
            <summary>
            SHAddToRecentDocuments flags.  SHARD_*
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.NIF">
            <summary>
            Shell_NotifyIcon flags.  NIF_*
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NIF.REALTIME">
            <summary>
            Vista only.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NIF.SHOWTIP">
            <summary>
            Vista only.
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.NIIF">
            <summary>
            Shell_NotifyIcon info flags.  NIIF_*
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NIIF.USER">
            <summary>XP SP2 and later.</summary>
        </member>
        <member name="F:ControlzEx.Standard.NIIF.NOSOUND">
            <summary>XP and later.</summary>
        </member>
        <member name="F:ControlzEx.Standard.NIIF.LARGE_ICON">
            <summary>Vista and later.</summary>
        </member>
        <member name="F:ControlzEx.Standard.NIIF.NIIF_RESPECT_QUIET_TIME">
            <summary>Windows 7 and later</summary>
        </member>
        <member name="F:ControlzEx.Standard.NIIF.XP_ICON_MASK">
            <summary>XP and later.  Native version called NIIF_ICON_MASK.</summary>
        </member>
        <member name="T:ControlzEx.Standard.AC">
            <summary>
            AC_*
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NOTIFYICONDATA.dwState">
            <summary>
            The state of the icon.  There are two flags that can be set independently.
            NIS_HIDDEN = 1.  The icon is hidden.
            NIS_SHAREDICON = 2.  The icon is shared.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.SHARDAPPIDINFOIDLIST.pidl">
            <summary>The idlist for the shell item that should be added to the recent docs folder.</summary>
        </member>
        <member name="F:ControlzEx.Standard.SHARDAPPIDINFOIDLIST.pszAppID">
            <summary>The id of the application that should be associated with this recent doc.</summary>
        </member>
        <member name="T:ControlzEx.Standard.WTA_OPTIONS">
            <summary>Defines options that are used to set window visual style attributes.</summary>
        </member>
        <member name="F:ControlzEx.Standard.WTA_OPTIONS.dwFlags">
            <summary>
            A combination of flags that modify window visual style attributes.
            Can be a combination of the WTNCA constants.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.WTA_OPTIONS.dwMask">
            <summary>
            A bitmask that describes how the values specified in dwFlags should be applied.
            If the bit corresponding to a value in dwFlags is 0, that flag will be removed.
            If the bit is 1, the flag will be added.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.MARGINS.cxLeftWidth">
            <summary>Width of left border that retains its size.</summary>
        </member>
        <member name="F:ControlzEx.Standard.MARGINS.cxRightWidth">
            <summary>Width of right border that retains its size.</summary>
        </member>
        <member name="F:ControlzEx.Standard.MARGINS.cyTopHeight">
            <summary>Height of top border that retains its size.</summary>
        </member>
        <member name="F:ControlzEx.Standard.MARGINS.cyBottomHeight">
            <summary>Height of bottom border that retains its size.</summary>
        </member>
        <member name="M:ControlzEx.Standard.WINDOWPOS.ToString">
            <inheritdoc />
        </member>
        <member name="F:ControlzEx.Standard.APPBARDATA.cbSize">
            <summary>
            initialize this field using: Marshal.SizeOf(typeof(APPBARDATA));
            </summary>
        </member>
        <member name="T:ControlzEx.Standard.WndProc">
            <summary>Delegate declaration that matches native WndProc signatures.</summary>
        </member>
        <member name="T:ControlzEx.Standard.MessageHandler">
            <summary>Delegate declaration that matches managed WndProc signatures.</summary>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.TryGetRelativeMousePosition(System.IntPtr,System.Windows.Point@)">
            <summary>
            Try to get the relative mouse position to the given handle in client coordinates.
            </summary>
            <param name="hWnd">The handle for this method.</param>
            <param name="point">The relative mouse position to the given handle.</param>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.GetWindow(System.IntPtr,ControlzEx.Standard.GW)">
            <summary>
            https://docs.microsoft.com/en-us/windows/desktop/api/winuser/nf-winuser-getwindow
            </summary>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.SetWindowThemeAttribute(System.IntPtr,ControlzEx.Standard.WINDOWTHEMEATTRIBUTETYPE,ControlzEx.Standard.WTA_OPTIONS@,System.UInt32)">
            <summary>
            Sets attributes to control how visual styles are applied to a specified window.
            </summary>
            <param name="hwnd">
            Handle to a window to apply changes to.
            </param>
            <param name="eAttribute">
            Value of type WINDOWTHEMEATTRIBUTETYPE that specifies the type of attribute to set.
            The value of this parameter determines the type of data that should be passed in the pvAttribute parameter.
            Can be the following value:
            <list>WTA_NONCLIENT (Specifies non-client related attributes).</list>
            pvAttribute must be a pointer of type WTA_OPTIONS.
            </param>
            <param name="pvAttribute">
            A pointer that specifies attributes to set. Type is determined by the value of the eAttribute value.
            </param>
            <param name="cbAttribute">
            Specifies the size, in bytes, of the data pointed to by pvAttribute.
            </param>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.GetWindowPlacement(System.IntPtr,ControlzEx.Standard.WINDOWPLACEMENT)">
            <summary>
            Retrieves the show state and the restored, minimized, and maximized positions of the specified window.
            </summary>
            <param name="hwnd">A handle to the window.</param>
            <param name="lpwndpl">A pointer to the WINDOWPLACEMENT structure that receives the show state and position information.</param>
            <remarks>
            Before calling GetWindowPlacement, set the length member to sizeof(WINDOWPLACEMENT).
            GetWindowPlacement fails if lpwndpl-> length is not set correctly.
            </remarks>
            <returns>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero.</returns>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.SetWindowPlacement(System.IntPtr,ControlzEx.Standard.WINDOWPLACEMENT)">
            <summary>
            Sets the show state and the restored, minimized, and maximized positions of the specified window.
            </summary>
            <param name="hWnd">A handle to the window.</param>
            <param name="lpwndpl">A pointer to a WINDOWPLACEMENT structure that specifies the new show state and window positions.</param>
            <remarks>
            Before calling SetWindowPlacement, set the length member of the WINDOWPLACEMENT structure to sizeof(WINDOWPLACEMENT).
            SetWindowPlacement fails if the length member is not set correctly.
            </remarks>
            <returns>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero.</returns>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.GetWindowTextLength(System.IntPtr)">
            <summary>
            Retrieves the length, in characters, of the specified window's title bar text (if the window has a title bar).
            If the specified window is a control, the function retrieves the length of the text within the control. However,
            GetWindowTextLength cannot retrieve the length of the text of an edit control in another application.
            </summary>
            <param name="hWnd">A handle to the window or control.</param>
            <returns>
            If the function succeeds, the return value is the length, in characters, of the text. Under certain
            conditions, this value may actually be greater than the length of the text. For more information, see the following
            Remarks section.
            <para>If the window has no text, the return value is zero. To get extended error information, call GetLastError.</para>
            </returns>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.GetWindowText(System.IntPtr,System.Text.StringBuilder,System.Int32)">
            <summary>
            Copies the text of the specified window's title bar (if it has one) into a buffer. If the specified window is
            a control, the text of the control is copied. However, GetWindowText cannot retrieve the text of a control in another
            application.
            </summary>
            <param name="hWnd">A handle to the window or control containing the text.</param>
            <param name="strText">
            The buffer that will receive the text. If the string is as long or longer than the buffer, the
            string is truncated and terminated with a null character.
            </param>
            <param name="maxCount">
            The maximum number of characters to copy to the buffer, including the null character. If the
            text exceeds this limit, it is truncated.
            </param>
            <returns>
            If the function succeeds, the return value is the length, in characters, of the copied string, not including
            the terminating null character. If the window has no title bar or text, if the title bar is empty, or if the window or
            control handle is invalid, the return value is zero. To get extended error information, call GetLastError.
            <para>This function cannot retrieve the text of an edit control in another application.</para>
            </returns>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.MonitorFromRect(ControlzEx.Standard.RECT@,ControlzEx.Standard.MonitorOptions)">
            <summary>
            The MonitorFromRect function retrieves a handle to the display monitor that 
            has the largest area of intersection with a specified rectangle.
            </summary>
            <param name="lprc">Pointer to a RECT structure that specifies the rectangle of interest in 
            virtual-screen coordinates</param>
            <param name="dwFlags">Determines the function's return value if the rectangle does not intersect 
            any display monitor</param>
            <returns>
            If the rectangle intersects one or more display monitor rectangles, the return value 
            is an HMONITOR handle to the display monitor that has the largest area of intersection with the rectangle.
            If the rectangle does not intersect a display monitor, the return value depends on the value of dwFlags.
            </returns>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods.LoadImage(System.IntPtr,System.IntPtr,System.UInt32,System.Int32,System.Int32,System.UInt32)">
            <summary>
            Loads an icon, cursor, animated cursor, or bitmap.
            </summary>
            <param name="hinst">Handle to the module of either a DLL or executable (.exe) that contains the image to be loaded</param>
            <param name="lpszName">Specifies the image to load</param>
            <param name="uType">Specifies the type of image to be loaded. </param>
            <param name="cxDesired">Specifies the width, in pixels, of the icon or cursor</param>
            <param name="cyDesired">Specifies the height, in pixels, of the icon or cursor</param>
            <param name="fuLoad">This parameter can be one or more of the following values.</param>
            <returns>If the function succeeds, the return value is the requested value.If the function fails, the return value is zero. To get extended error information, call GetLastError. </returns>
        </member>
        <member name="F:ControlzEx.Standard.NativeMethods.FlashWindowFlag.FLASHW_STOP">
            <summary>
            Stop flashing. The system restores the window to its original state. 
            </summary>    
        </member>
        <member name="F:ControlzEx.Standard.NativeMethods.FlashWindowFlag.FLASHW_CAPTION">
            <summary>
            Flash the window caption 
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NativeMethods.FlashWindowFlag.FLASHW_TRAY">
            <summary>
            Flash the taskbar button. 
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NativeMethods.FlashWindowFlag.FLASHW_ALL">
            <summary>
            Flash both the window caption and taskbar button.
            This is equivalent to setting the FLASHW_CAPTION | FLASHW_TRAY flags. 
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NativeMethods.FlashWindowFlag.FLASHW_TIMER">
            <summary>
            Flash continuously, until the FLASHW_STOP flag is set.
            </summary>
        </member>
        <member name="F:ControlzEx.Standard.NativeMethods.FlashWindowFlag.FLASHW_TIMERNOFG">
            <summary>
            Flash continuously until the window comes to the foreground. 
            </summary>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods._SystemParametersInfo_NONCLIENTMETRICS(ControlzEx.Standard.SPI,System.Int32,ControlzEx.Standard.NONCLIENTMETRICS@,ControlzEx.Standard.SPIF)">
            <summary>Overload of SystemParametersInfo for getting and setting NONCLIENTMETRICS.</summary>
        </member>
        <member name="M:ControlzEx.Standard.NativeMethods._SystemParametersInfo_HIGHCONTRAST(ControlzEx.Standard.SPI,System.Int32,ControlzEx.Standard.HIGHCONTRAST@,ControlzEx.Standard.SPIF)">
            <summary>Overload of SystemParametersInfo for getting and setting HIGHCONTRAST.</summary>
        </member>
        <member name="T:ControlzEx.Standard.ComStream">
            <summary>
            Wraps an IStream interface pointer from COM into a form consumable by .Net.
            </summary>
            <remarks>
            This implementation is immutable, though it's possible that the underlying
            stream can be changed in another context.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ComStream.#ctor(System.Runtime.InteropServices.ComTypes.IStream@)">
            <summary>
            Wraps a native IStream interface into a CLR Stream subclass.
            </summary>
            <param name="stream">
            The stream that this object wraps.
            </param>
            <remarks>
            Note that the parameter is passed by ref.  On successful creation it is
            zeroed out to the caller.  This object becomes responsible for the lifetime
            management of the wrapped IStream.
            </remarks>
        </member>
        <member name="T:ControlzEx.Standard.ManagedIStream">
            <summary>
            Wraps a managed stream instance into an interface pointer consumable by COM.
            </summary>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the ManagedIStream class with the specified managed Stream object.
            </summary>
            <param name="source">
            The stream that this IStream reference is wrapping.
            </param>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Clone(System.Runtime.InteropServices.ComTypes.IStream@)">
            <summary>
            Creates a new stream object with its own seek pointer that
            references the same bytes as the original stream. 
            </summary>
            <param name="ppstm">
            When this method returns, contains the new stream object. This parameter is passed uninitialized.
            </param>
            <remarks>
            For more information, see the existing documentation for IStream::Clone in the MSDN library.
            This class doesn't implement Clone.  A COMException is thrown if it is used.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Commit(System.Int32)">
            <summary>
            Ensures that any changes made to a stream object that is open in transacted
            mode are reflected in the parent storage. 
            </summary>
            <param name="grfCommitFlags">
            A value that controls how the changes for the stream object are committed. 
            </param>
            <remarks>
            For more information, see the existing documentation for IStream::Commit in the MSDN library.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.CopyTo(System.Runtime.InteropServices.ComTypes.IStream,System.Int64,System.IntPtr,System.IntPtr)">
            <summary>
            Copies a specified number of bytes from the current seek pointer in the
            stream to the current seek pointer in another stream. 
            </summary>
            <param name="pstm">
            A reference to the destination stream. 
            </param>
            <param name="cb">
            The number of bytes to copy from the source stream. 
            </param>
            <param name="pcbRead">
            On successful return, contains the actual number of bytes read from the source.
            (Note the native signature is to a ULARGE_INTEGER*, so 64 bits are written
            to this parameter on success.)
            </param>
            <param name="pcbWritten">
            On successful return, contains the actual number of bytes written to the destination.
            (Note the native signature is to a ULARGE_INTEGER*, so 64 bits are written
            to this parameter on success.)
            </param>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.LockRegion(System.Int64,System.Int64,System.Int32)">
            <summary>
            Restricts access to a specified range of bytes in the stream. 
            </summary>
            <param name="libOffset">
            The byte offset for the beginning of the range. 
            </param>
            <param name="cb">
            The length of the range, in bytes, to restrict.
            </param>
            <param name="dwLockType">
            The requested restrictions on accessing the range.
            </param>
            <remarks>
            For more information, see the existing documentation for IStream::LockRegion in the MSDN library.
            This class doesn't implement LockRegion.  A COMException is thrown if it is used.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Read(System.Byte[],System.Int32,System.IntPtr)">
            <summary>
            Reads a specified number of bytes from the stream object into memory starting at the current seek pointer. 
            </summary>
            <param name="pv">
            When this method returns, contains the data read from the stream. This parameter is passed uninitialized.
            </param>
            <param name="cb">
            The number of bytes to read from the stream object. 
            </param>
            <param name="pcbRead">
            A pointer to a ULONG variable that receives the actual number of bytes read from the stream object.
            </param>
            <remarks>
            For more information, see the existing documentation for ISequentialStream::Read in the MSDN library.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Revert">
            <summary>
            Discards all changes that have been made to a transacted stream since the last Commit call.
            </summary>
            <remarks>
            This class doesn't implement Revert.  A COMException is thrown if it is used.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Seek(System.Int64,System.Int32,System.IntPtr)">
            <summary>
            Changes the seek pointer to a new location relative to the beginning of the
            stream, to the end of the stream, or to the current seek pointer.
            </summary>
            <param name="dlibMove">
            The displacement to add to dwOrigin.
            </param>
            <param name="dwOrigin">
            The origin of the seek. The origin can be the beginning of the file, the current seek pointer, or the end of the file. 
            </param>
            <param name="plibNewPosition">
            On successful return, contains the offset of the seek pointer from the beginning of the stream.
            (Note the native signature is to a ULARGE_INTEGER*, so 64 bits are written
            to this parameter on success.)
            </param>
            <remarks>
            For more information, see the existing documentation for IStream::Seek in the MSDN library.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.SetSize(System.Int64)">
            <summary>
            Changes the size of the stream object. 
            </summary>
            <param name="libNewSize">
            The new size of the stream as a number of bytes. 
            </param>
            <remarks>
            For more information, see the existing documentation for IStream::SetSize in the MSDN library.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Stat(System.Runtime.InteropServices.ComTypes.STATSTG@,System.Int32)">
            <summary>
            Retrieves the STATSTG structure for this stream. 
            </summary>
            <param name="pstatstg">
            When this method returns, contains a STATSTG structure that describes this stream object.
            This parameter is passed uninitialized.
            </param>
            <param name="grfStatFlag">
            Members in the STATSTG structure that this method does not return, thus saving some memory allocation operations. 
            </param>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.UnlockRegion(System.Int64,System.Int64,System.Int32)">
            <summary>
            Removes the access restriction on a range of bytes previously restricted with the LockRegion method.
            </summary>
            <param name="libOffset">The byte offset for the beginning of the range.
            </param>
            <param name="cb">
            The length, in bytes, of the range to restrict.
            </param>
            <param name="dwLockType">
            The access restrictions previously placed on the range.
            </param>
            <remarks>
            For more information, see the existing documentation for IStream::UnlockRegion in the MSDN library.
            This class doesn't implement UnlockRegion.  A COMException is thrown if it is used.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Write(System.Byte[],System.Int32,System.IntPtr)">
            <summary>
            Writes a specified number of bytes into the stream object starting at the current seek pointer.
            </summary>
            <param name="pv">
            The buffer to write this stream to.
            </param>
            <param name="cb">
            The number of bytes to write to the stream. 
            </param>
            <param name="pcbWritten">
            On successful return, contains the actual number of bytes written to the stream object. 
            If the caller sets this pointer to null, this method does not provide the actual number
            of bytes written.
            </param>
        </member>
        <member name="M:ControlzEx.Standard.ManagedIStream.Dispose">
            <summary>
            Releases resources controlled by this object.
            </summary>
            <remarks>
            Dispose can be called multiple times, but trying to use the object
            after it has been disposed will generally throw ObjectDisposedExceptions.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.Utility.SafeCopyFile(System.String,System.String,ControlzEx.Standard.SafeCopyFileOptions)">
            <summary>
            Wrapper around File.Copy to provide feedback as to whether the file wasn't copied because it didn't exist.
            </summary>
            <returns></returns>
        </member>
        <member name="M:ControlzEx.Standard.Utility.SafeDeleteFile(System.String)">
            <summary>
            Simple guard against the exceptions that File.Delete throws on null and empty strings.
            </summary>
            <param name="path">The path to delete.  Unlike File.Delete, this can be null or empty.</param>
            <remarks>
            Note that File.Delete, and by extension SafeDeleteFile, does not throw an exception
            if the file does not exist.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.Utility.GeneratePropertyString(System.Text.StringBuilder,System.String,System.String)">
            <summary>
            Utility to help classes catenate their properties for implementing ToString().
            </summary>
            <param name="source">The StringBuilder to catenate the results into.</param>
            <param name="propertyName">The name of the property to be catenated.</param>
            <param name="value">The value of the property to be catenated.</param>
        </member>
        <member name="M:ControlzEx.Standard.Utility.GenerateToString``1(``0)">
            <summary>
            Generates ToString functionality for a struct.  This is an expensive way to do it,
            it exists for the sake of debugging while classes are in flux.
            Eventually this should just be removed and the classes should
            do this without reflection.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="object"></param>
            <returns></returns>
        </member>
        <member name="M:ControlzEx.Standard.Utility.UrlEncode(System.String)">
            <summary>
            Encodes a URL string.  Duplicated functionality from System.Web.HttpUtility.UrlEncode.
            </summary>
            <param name="url"></param>
            <returns></returns>
            <remarks>
            Duplicated from System.Web.HttpUtility because System.Web isn't part of the client profile.
            URL Encoding replaces ' ' with '+' and unsafe ASCII characters with '%XX'.
            Safe characters are defined in RFC2396 (http://www.ietf.org/rfc/rfc2396.txt).
            They are the 7-bit ASCII alphanumerics and the mark characters "-_.!~*'()".
            This implementation does not treat '~' as a safe character to be consistent with the System.Web version.
            </remarks>
        </member>
        <member name="M:ControlzEx.Standard.Utility.SafeDeleteObject(System.IntPtr@)">
            <summary>GDI's DeleteObject</summary>
        </member>
        <member name="M:ControlzEx.Standard.Utility.SafeDisposeImage(System.IntPtr@)">
            <summary>GDI+'s DisposeImage</summary>
            <param name="gdipImage"></param>
        </member>
        <member name="M:ControlzEx.Standard.Utility._GetBestMatch(System.Collections.Generic.IList{System.Windows.Media.Imaging.BitmapFrame},System.Int32,System.Int32,System.Int32)">
            From a list of BitmapFrames find the one that best matches the requested dimensions.
            The methods used here are copied from Win32 sources.  We want to be consistent with
            system behaviors.
        </member>
        <member name="M:ControlzEx.Standard.Utility.ColorFromArgbDword(System.UInt32)">
            <summary>Convert a native integer that represent a color with an alpha channel into a Color struct.</summary>
            <param name="color">The integer that represents the color.  Its bits are of the format 0xAARRGGBB.</param>
            <returns>A Color representation of the parameter.</returns>
        </member>
        <member name="T:ControlzEx.Standard.Verify">
            <summary>
            A static class for retail validated assertions.
            Instead of breaking into the debugger an exception is thrown.
            </summary>
        </member>
        <member name="M:ControlzEx.Standard.Verify.IsApartmentState(System.Threading.ApartmentState,System.String)">
            <summary>
            Ensure that the current thread's apartment state is what's expected.
            </summary>
            <param name="requiredState">
            The required apartment state for the current thread.
            </param>
            <param name="message">
            The message string for the exception to be thrown if the state is invalid.
            </param>
            <exception cref="T:System.InvalidOperationException">
            Thrown if the calling thread's apartment state is not the same as the requiredState.
            </exception>
        </member>
        <member name="M:ControlzEx.Standard.Verify.IsNeitherNullNorEmpty(System.String,System.String)">
            <summary>
            Ensure that an argument is neither null nor empty.
            </summary>
            <param name="value">The string to validate.</param>
            <param name="name">The name of the parameter that will be presented if an exception is thrown.</param>
        </member>
        <member name="M:ControlzEx.Standard.Verify.IsNeitherNullNorWhitespace(System.String,System.String)">
            <summary>
            Ensure that an argument is neither null nor does it consist only of whitespace.
            </summary>
            <param name="value">The string to validate.</param>
            <param name="name">The name of the parameter that will be presented if an exception is thrown.</param>
        </member>
        <member name="M:ControlzEx.Standard.Verify.IsNotDefault``1(``0,System.String)">
            <summary>Verifies that an argument is not null.</summary>
            <typeparam name="T">Type of the object to validate.  Must be a class.</typeparam>
            <param name="obj">The object to validate.</param>
            <param name="name">The name of the parameter that will be presented if an exception is thrown.</param>
        </member>
        <member name="M:ControlzEx.Standard.Verify.IsNotNull``1(``0,System.String)">
            <summary>Verifies that an argument is not null.</summary>
            <typeparam name="T">Type of the object to validate.  Must be a class.</typeparam>
            <param name="obj">The object to validate.</param>
            <param name="name">The name of the parameter that will be presented if an exception is thrown.</param>
        </member>
        <member name="M:ControlzEx.Standard.Verify.IsNull``1(``0,System.String)">
            <summary>Verifies that an argument is null.</summary>
            <typeparam name="T">Type of the object to validate.  Must be a class.</typeparam>
            <param name="obj">The object to validate.</param>
            <param name="name">The name of the parameter that will be presented if an exception is thrown.</param>
        </member>
        <member name="M:ControlzEx.Standard.Verify.IsTrue(System.Boolean,System.String,System.String)">
            <summary>
            Verifies the specified statement is true.  Throws an ArgumentException if it's not.
            </summary>
            <param name="statement">The statement to be verified as true.</param>
            <param name="name">Name of the parameter to include in the ArgumentException.</param>
            <param name="message">The message to include in the ArgumentException.</param>
        </member>
        <member name="M:ControlzEx.Standard.Verify.BoundedInteger(System.Int32,System.Int32,System.Int32,System.String)">
            <summary>
            Verifies that the specified value is within the expected range.  The assertion fails if it isn't.
            </summary>
            <param name="lowerBoundInclusive">The lower bound inclusive value.</param>
            <param name="value">The value to verify.</param>
            <param name="upperBoundExclusive">The upper bound exclusive value.</param>
            <param name="parameterName">The name of the parameter that caused the current exception.</param>
        </member>
        <member name="M:ControlzEx.Windows.Shell.SystemCommands.ShowSystemMenu(System.Windows.Window,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Shows the system menu at the current mouse position.
            </summary>
            <param name="window">The window for which the system menu should be shown.</param>
            <param name="e">The mouse event args.</param>
        </member>
        <member name="M:ControlzEx.Windows.Shell.SystemCommands.ShowSystemMenu(System.Windows.Media.Visual,System.Windows.Point)">
            <summary>Display the system menu at a specified location.</summary>
            <param name="visual">The visual for which the system menu should be displayed.</param>
            <param name="elementPoint">The location to display the system menu, in logical screen coordinates.</param>
        </member>
        <member name="M:ControlzEx.Windows.Shell.SystemCommands.ShowSystemMenuPhysicalCoordinates(System.Windows.Media.Visual,System.Windows.Point)">
            <summary>Display the system menu at a specified location.</summary>
            <param name="visual">The visual for which the system menu should be displayed.</param>
            <param name="physicalScreenLocation">The location to display the system menu, in physical screen coordinates.</param>
            <remarks>
            The dpi of <paramref name="visual"/> is NOT used to calculate the final coordinates.
            So you have to pass the final coordinates.
            </remarks>
        </member>
        <member name="M:ControlzEx.Windows.Shell.SystemCommands.ShowSystemMenuPhysicalCoordinates(System.Windows.Interop.HwndSource,System.Windows.Point)">
            <summary>Display the system menu at a specified location.</summary>
            <param name="source">The source/hwnd for which the system menu should be displayed.</param>
            <param name="physicalScreenLocation">The location to display the system menu, in physical screen coordinates.</param>
            <remarks>
            The dpi of <paramref name="source"/> is NOT used to calculate the final coordinates.
            So you have to pass the final coordinates.
            </remarks>
        </member>
        <member name="M:ControlzEx.Windows.Shell.SystemParameters2.#ctor">
            <summary>
            Private constructor.  The public way to access this class is through the static Current property.
            </summary>
        </member>
        <member name="P:ControlzEx.Windows.Shell.SystemParameters2.DpiX">
            <SecurityNote>
              Critical as this accesses Native methods.
              TreatAsSafe - it would be ok to expose this information - DPI in partial trust
            </SecurityNote>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.Invalidate">
            <summary>
            Invalidates the rectangle or region that you specify in lprcUpdate or hrgnUpdate.
            You can set only one of these parameters to a non-NULL value. If both are NULL, RDW_INVALIDATE invalidates the entire window.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.InternalPaint">
            <summary>Causes the OS to post a WM_PAINT message to the window regardless of whether a portion of the window is invalid.</summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.Erase">
            <summary>
            Causes the window to receive a WM_ERASEBKGND message when the window is repainted.
            Specify this value in combination with the RDW_INVALIDATE value; otherwise, RDW_ERASE has no effect.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.Validate">
            <summary>
            Validates the rectangle or region that you specify in lprcUpdate or hrgnUpdate.
            You can set only one of these parameters to a non-NULL value. If both are NULL, RDW_VALIDATE validates the entire window.
            This value does not affect internal WM_PAINT messages.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.NoErase">
            <summary>Suppresses any pending WM_ERASEBKGND messages.</summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.NoChildren">
            <summary>Excludes child windows, if any, from the repainting operation.</summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.AllChildren">
            <summary>Includes child windows, if any, in the repainting operation.</summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.UpdateNow">
            <summary>Causes the affected windows, which you specify by setting the RDW_ALLCHILDREN and RDW_NOCHILDREN values, to receive WM_ERASEBKGND and WM_PAINT messages before the RedrawWindow returns, if necessary.</summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.RedrawWindowFlags.EraseNow">
            <summary>
            Causes the affected windows, which you specify by setting the RDW_ALLCHILDREN and RDW_NOCHILDREN values, to receive WM_ERASEBKGND messages before RedrawWindow returns, if necessary.
            The affected windows receive WM_PAINT messages at the ordinary time.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.Constants.CC_ANYCOLOR">
            <summary>
            Causes the dialog box to display all available colors in the set of basic colors. 
            </summary>
        </member>
        <member name="T:ControlzEx.Native.UnsafeNativeMethods">
            <devdoc>http://msdn.microsoft.com/en-us/library/ms182161.aspx</devdoc>
        </member>
        <member name="M:ControlzEx.Native.UnsafeNativeMethods.MonitorFromWindow(System.IntPtr,ControlzEx.Standard.MonitorOptions)">
            <devdoc>http://msdn.microsoft.com/en-us/library/dd145064%28v=VS.85%29.aspx</devdoc>
        </member>
        <member name="M:ControlzEx.Native.UnsafeNativeMethods.LoadString(ControlzEx.Native.SafeLibraryHandle,System.UInt32,System.Text.StringBuilder,System.Int32)">
            <devdoc>http://msdn.microsoft.com/en-us/library/windows/desktop/ms647486%28v=vs.85%29.aspx</devdoc>
        </member>
        <member name="M:ControlzEx.Native.UnsafeNativeMethods.IsWindow(System.IntPtr)">
            <devdoc>http://msdn.microsoft.com/en-us/library/windows/desktop/ms633528(v=vs.85).aspx</devdoc>
        </member>
        <member name="M:ControlzEx.Native.UnsafeNativeMethods.LoadLibrary(System.String)">
            <devdoc>http://msdn.microsoft.com/en-us/library/windows/desktop/ms684175%28v=vs.85%29.aspx</devdoc>
        </member>
        <member name="M:ControlzEx.Native.UnsafeNativeMethods.FreeLibrary(System.IntPtr)">
            <devdoc>http://msdn.microsoft.com/en-us/library/windows/desktop/ms683152%28v=vs.85%29.aspx</devdoc>
        </member>
        <member name="T:ControlzEx.PackIconBase`1">
            <summary>
            Base class for creating an icon control for icon packs.
            </summary>
            <typeparam name="TKind"></typeparam>
        </member>
        <member name="M:ControlzEx.PackIconBase`1.#ctor(System.Func{System.Collections.Generic.IDictionary{`0,System.String}})">
            <param name="dataIndexFactory">
            Inheritors should provide a factory for setting up the path data index (per icon kind).
            The factory will only be utilized once, across all closed instances (first instantiation wins).
            </param>
        </member>
        <member name="F:ControlzEx.PackIconBase`1.KindProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PackIconBase`1.Kind"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PackIconBase`1.Kind">
            <summary>
            Gets or sets the icon to display.
            </summary>
        </member>
        <member name="F:ControlzEx.PackIconBase`1.DataProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PackIconBase`1.Data"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PackIconBase`1.Data">
            <summary>
            Gets the icon path data for the current <see cref="P:ControlzEx.PackIconBase`1.Kind"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.PopupEx">
            <summary>
            This custom popup can be used by validation error templates or something else.
            It provides some additional nice features:
                - repositioning if host-window size or location changed
                - repositioning if host-window gets maximized and vice versa
                - it's only topmost if the host-window is activated
            </summary>
        </member>
        <member name="F:ControlzEx.PopupEx.CloseOnMouseLeftButtonDownProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PopupEx.CloseOnMouseLeftButtonDown"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PopupEx.CloseOnMouseLeftButtonDown">
            <summary>
            Gets or sets if the popup can be closed by left mouse button down.
            </summary>
        </member>
        <member name="F:ControlzEx.PopupEx.AllowTopMostProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PopupEx.AllowTopMost"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PopupEx.AllowTopMost">
            <summary>
            Gets or sets whether if the Popup should be always on top.
            </summary>
        </member>
        <member name="M:ControlzEx.PopupEx.RefreshPosition">
            <summary>
            Causes the popup to update it's position according to it's current settings.
            </summary>
        </member>
        <member name="T:ControlzEx.PopupEx.SWP">
            <summary>
            SetWindowPos options
            </summary>
        </member>
        <member name="T:ControlzEx.PropertyChangeNotifier">
            <summary>
            AddValueChanged of dependency property descriptor results in memory leak as you already know.
            So, as described here, you can create custom class PropertyChangeNotifier to listen
            to any dependency property changes.
            
            This class takes advantage of the fact that bindings use weak references to manage associations
            so the class will not root the object who property changes it is watching. It also uses a WeakReference
            to maintain a reference to the object whose property it is watching without rooting that object.
            In this way, you can maintain a collection of these objects so that you can unhook the property
            change later without worrying about that collection rooting the object whose values you are watching.
            
            Complete implementation can be found here: http://agsmith.wordpress.com/2008/04/07/propertydescriptor-addvaluechanged-alternative/
            </summary>
        </member>
        <member name="F:ControlzEx.PropertyChangeNotifier.ValueProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PropertyChangeNotifier.Value"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PropertyChangeNotifier.Value">
            <summary>
            Gets or sets the value of the watched property.
            </summary>
            <seealso cref="F:ControlzEx.PropertyChangeNotifier.ValueProperty"/>
        </member>
        <member name="T:ControlzEx.Theming.HSLColor">
            <summary>
            This struct represent a Color in HSL (Hue, Saturation, Luminance)
            
            Idea taken from here http://ciintelligence.blogspot.com/2012/02/converting-excel-theme-color-and-tint.html
            and here: https://en.wikipedia.org/wiki/HSL_and_HSV
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.#ctor(System.Windows.Media.Color)">
            <summary>
            Creates a new HSL Color
            </summary>
            <param name="color">Any System.Windows.Media.Color</param>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Creates a new HSL Color
            </summary>
            <param name="a">Alpha Channel [0;1]</param>
            <param name="h">Hue Channel [0;360]</param>
            <param name="s">Saturation Channel [0;1]</param>
            <param name="l">Luminance Channel [0;1]</param>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.A">
            <summary>
            Gets or sets the Alpha channel.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.H">
            <summary>
            Gets or sets the Hue channel.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.S">
            <summary>
            Gets or sets the Saturation channel.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.L">
            <summary>
            Gets or sets the Luminance channel.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.ToColor">
            <summary>
            Gets the ARGB-Color for this HSL-Color
            </summary>
            <returns>System.Windows.Media.Color</returns>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.GetTintedColor(System.Double)">
            <summary>
            Gets a lighter / darker color based on a tint value. If <paramref name="tint"/> is > 0 then the returned color is darker, otherwise it will be lighter.
            </summary>
            <param name="tint">Tint Value in the Range [-1;1].</param>
            <returns>a new <see cref="T:System.Windows.Media.Color"/> which is lighter or darker.</returns>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.GetTintedColor(System.Windows.Media.Color,System.Double)">
            <summary>
            Gets a lighter / darker color based on a tint value. If <paramref name="tint"/> is > 0 then the returned color is darker, otherwise it will be lighter.
            </summary>
            <param name="color">The input color which should be tinted.</param>
            <param name="tint">Tint Value in the Range [-1;1].</param>
            <returns>a new <see cref="T:System.Windows.Media.Color"/> which is lighter or darker.</returns>
        </member>
        <member name="T:ControlzEx.Theming.LibraryTheme">
            <summary>
            Represents a theme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.LibraryTheme.LibraryThemeInstanceKey">
            <summary>
            Gets the key for the library theme instance.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.LibraryTheme.LibraryThemeAlternativeColorSchemeKey">
            <summary>
            Gets the key for the theme color scheme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.LibraryTheme.RuntimeThemeColorValuesKey">
            <summary>
            Gets the key for the color values being used to generate a runtime theme.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.LibraryTheme.#ctor(System.Uri,ControlzEx.Theming.LibraryThemeProvider)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="resourceAddress">The URI of the theme ResourceDictionary.</param>
            <param name="libraryThemeProvider">The <see cref="T:ControlzEx.Theming.LibraryThemeProvider"/> which created this instance.</param>
        </member>
        <member name="M:ControlzEx.Theming.LibraryTheme.#ctor(System.Windows.ResourceDictionary,ControlzEx.Theming.LibraryThemeProvider)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="resourceDictionary">The ResourceDictionary of the theme.</param>
            <param name="libraryThemeProvider">The <see cref="T:ControlzEx.Theming.LibraryThemeProvider"/> which created this instance.</param>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.IsRuntimeGenerated">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.IsRuntimeGenerated"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.IsHighContrast">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.IsHighContrast"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.Name">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.Name"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.Origin">
            <summary>
            Get the origin of the theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.DisplayName">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.DisplayName"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.BaseColorScheme">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.BaseColorScheme"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.ColorScheme">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.ColorScheme"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.PrimaryAccentColor">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.PrimaryAccentColor"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.ShowcaseBrush">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.ShowcaseBrush"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.Resources">
            <summary>
            The root <see cref="T:System.Windows.ResourceDictionary"/> containing all resource dictionaries belonging to this instance as <see cref="P:System.Windows.ResourceDictionary.MergedDictionaries"/>
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.AlternativeColorScheme">
            <summary>
            Gets the alternative color scheme for this theme.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.RuntimeThemeGenerator.GetIdealTextColor(System.Windows.Media.Color)">
            <summary>
                Determining Ideal Text Color Based on Specified Background Color
                http://www.codeproject.com/KB/GDI-plus/IdealTextColor.aspx
            </summary>
            <param name="color">The background color.</param>
            <returns></returns>
        </member>
        <member name="T:ControlzEx.Theming.RuntimeThemeGeneratorOptions">
            <summary>
            Global options for <see cref="T:ControlzEx.Theming.RuntimeThemeGenerator"/>.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.RuntimeThemeGeneratorOptions.CreateRuntimeThemeOptions(System.Boolean,ControlzEx.Theming.ThemeGenerator.ThemeGeneratorParameters,ControlzEx.Theming.ThemeGenerator.ThemeGeneratorBaseColorScheme)">
            <summary>
            Used to create the options being used to generate a single <see cref="T:ControlzEx.Theming.LibraryTheme"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.Theming.RuntimeThemeOptions">
            <summary>
            Options being used to generate one single <see cref="T:ControlzEx.Theming.LibraryTheme"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.Theming.Theme">
            <summary>
            Represents a theme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeNameKey">
            <summary>
            Gets the key for the themes name.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeOriginKey">
            <summary>
            Gets the key for the themes origin.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeDisplayNameKey">
            <summary>
            Gets the key for the themes display name.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeBaseColorSchemeKey">
            <summary>
            Gets the key for the themes base color scheme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeColorSchemeKey">
            <summary>
            Gets the key for the themes color scheme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemePrimaryAccentColorKey">
            <summary>
            Gets the key for the themes primary accent color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeShowcaseBrushKey">
            <summary>
            Gets the key for the themes showcase brush.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeIsRuntimeGeneratedKey">
            <summary>
            Gets the key for the themes runtime generation flag.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeIsHighContrastKey">
            <summary>
            Gets the key for the themes high contrast flag.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeInstanceKey">
            <summary>
            Gets the key for the theme instance.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.Theme.#ctor(ControlzEx.Theming.LibraryTheme)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="libraryTheme">The first <see cref="T:ControlzEx.Theming.LibraryTheme"/> of the theme.</param>
        </member>
        <member name="P:ControlzEx.Theming.Theme.IsRuntimeGenerated">
            <summary>
            Gets whether this theme was generated at runtime.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.IsHighContrast">
            <summary>
            Gets whether this theme is for high contrast mode.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.Name">
            <summary>
            Gets the name of the theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.DisplayName">
            <summary>
            Gets the display name of the theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.BaseColorScheme">
            <summary>
            Get the base color scheme for this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.PrimaryAccentColor">
            <summary>
            Gets the primary accent color for this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.ColorScheme">
            <summary>
            Gets the color scheme for this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.ShowcaseBrush">
            <summary>
            Gets a brush which can be used to showcase this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.Resources">
            <summary>
            The root <see cref="T:System.Windows.ResourceDictionary"/> containing all resource dictionaries of all <see cref="T:ControlzEx.Theming.LibraryTheme"/> belonging to this instance as <see cref="P:System.Windows.ResourceDictionary.MergedDictionaries"/>
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.LibraryThemes">
            <summary>
            The ResourceDictionaries that represent this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.LibraryThemesInternal">
            <summary>
            The ResourceDictionaries that represent this theme.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.Theme.EnsureAllLibraryThemeProvidersProvided">
            <summary>
            Ensures that all <see cref="T:ControlzEx.Theming.LibraryThemeProvider"/> from <see cref="P:ControlzEx.Theming.ThemeManager.LibraryThemeProviders"/> provided a <see cref="T:ControlzEx.Theming.LibraryTheme"/> for this <see cref="T:ControlzEx.Theming.Theme"/>.
            </summary>
            <returns>This instance for fluent call usage.</returns>
        </member>
        <member name="M:ControlzEx.Theming.Theme.GetAllResources">
            <summary>
            Gets a flat list of all <see cref="T:System.Windows.ResourceDictionary"/> from all library themes.
            </summary>
            <returns>A flat list of all <see cref="T:System.Windows.ResourceDictionary"/> from all library themes.</returns>
        </member>
        <member name="M:ControlzEx.Theming.Theme.AddLibraryTheme(ControlzEx.Theming.LibraryTheme)">
            <summary>
            Adds a new <see cref="T:ControlzEx.Theming.LibraryTheme"/> to this <see cref="T:ControlzEx.Theming.Theme"/>.
            </summary>
            <param name="libraryTheme">The <see cref="T:ControlzEx.Theming.LibraryTheme"/> to add.</param>
            <returns>This instance for fluent call usage.</returns>
        </member>
        <member name="M:ControlzEx.Theming.Theme.ToString">
            <inheritdoc />
        </member>
        <member name="T:ControlzEx.Theming.ThemeChangedEventArgs">
            <summary>
            Class which is used as argument for an event to signal theme changes.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeChangedEventArgs.#ctor(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,ControlzEx.Theming.Theme)">
            <summary>
            Creates a new instance of this class.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.Target">
            <summary>
            The target object for which was targeted by the theme change.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.TargetResourceDictionary">
            <summary>
            The <see cref="T:System.Windows.ResourceDictionary"/> for which was targeted by the theme change.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.OldTheme">
            <summary>
            The old theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.NewTheme">
            <summary>
            The new theme.
            </summary>
        </member>
        <member name="T:ControlzEx.Theming.ThemeManager">
            <summary>
            A class that allows for the detection and alteration of a theme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorLight">
            <summary>
            Gets the name for the light base color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorLightConst">
            <summary>
            Gets the name for the light base color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorDark">
            <summary>
            Gets the name for the dark base color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorDarkConst">
            <summary>
            Gets the name for the dark base color.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.LibraryThemeProviders">
            <summary>
            Gets a list of all library theme providers.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.Themes">
            <summary>
            Gets a list of all themes.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.BaseColors">
            <summary>
            Gets a list of all available base colors.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.ColorSchemes">
            <summary>
            Gets a list of all available color schemes.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ClearThemes">
            <summary>
            Clears the internal themes list.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetTheme(System.String,System.Boolean)">
            <summary>
            Gets the <see cref="T:ControlzEx.Theming.Theme"/> with the given name.
            </summary>
            <returns>The <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c>, if the theme wasn't found</returns>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetTheme(System.String,System.String,System.Boolean)">
            <summary>
            Gets the <see cref="T:ControlzEx.Theming.Theme"/> with the given name.
            </summary>
            <returns>The <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c>, if the theme wasn't found</returns>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetTheme(System.Windows.ResourceDictionary)">
            <summary>
            Gets the <see cref="T:ControlzEx.Theming.Theme"/> with the given resource dictionary.
            </summary>
            <param name="resourceDictionary"><see cref="T:System.Windows.ResourceDictionary"/> from which the theme should be retrieved.</param>
            <returns>The <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c>, if the theme wasn't found.</returns>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetInverseTheme(ControlzEx.Theming.Theme)">
            <summary>
            Gets the inverse <see cref="T:ControlzEx.Theming.Theme" /> of the given <see cref="T:ControlzEx.Theming.Theme"/>.
            This method relies on the "Dark" or "Light" affix to be present.
            </summary>
            <param name="theme">The app theme.</param>
            <returns>The inverse <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c> if it couldn't be found.</returns>
            <remarks>
            Returns BaseLight, if BaseDark is given or vice versa.
            Custom Themes must end with "Dark" or "Light" for this to work, for example "CustomDark" and "CustomLight".
            </remarks>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.IsThemeDictionary(System.Windows.ResourceDictionary)">
            <summary>
            Determines whether the specified resource dictionary represents a <see cref="T:ControlzEx.Theming.Theme"/>.
            <para />
            This might include runtime themes which do not have a resource uri.
            </summary>
            <param name="resourceDictionary">The resources.</param>
            <returns><c>true</c> if the resource dictionary is an <see cref="T:ControlzEx.Theming.Theme"/>; otherwise, <c>false</c>.</returns>
            <exception cref="T:System.ArgumentNullException">resources</exception>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.IsRuntimeGeneratedThemeDictionary(System.Windows.ResourceDictionary)">
            <summary>
            Determines whether the specified resource dictionary represents a <see cref="T:ControlzEx.Theming.Theme"/> and was generated at runtime.
            <para />
            This might include runtime themes which do not have a resource uri.
            </summary>
            <param name="resourceDictionary">The resources.</param>
            <returns><c>true</c> if the resource dictionary is an <see cref="T:ControlzEx.Theming.Theme"/>; otherwise, <c>false</c>.</returns>
            <exception cref="T:System.ArgumentNullException">resources</exception>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.Application,System.String,System.Boolean)">
            <summary>
            Change the theme for the whole application.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.FrameworkElement,System.String,System.Boolean)">
            <summary>
            Change theme for the given window.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.Application,ControlzEx.Theming.Theme)">
            <summary>
            Change theme for the whole application.
            </summary>
            <param name="app">The instance of Application to change.</param>
            <param name="newTheme">The theme to apply.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.FrameworkElement,ControlzEx.Theming.Theme)">
            <summary>
            Change theme for the given ResourceDictionary.
            </summary>
            <param name="frameworkElement">The FrameworkElement to change.</param>
            <param name="newTheme">The theme to apply.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme)">
            <summary>
            Change theme for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to change.</param>
            <param name="newTheme">The theme to apply.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.Application,System.String,System.String)">
            <summary>
            Change base color and color scheme of for the given application.
            </summary>
            <param name="app">The application to modify.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.FrameworkElement,System.String,System.String)">
            <summary>
            Change base color and color scheme of for the given window.
            </summary>
            <param name="frameworkElement">The FrameworkElement to modify.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,System.String,System.String)">
            <summary>
            Change base color and color scheme of for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="oldTheme">The old/current theme.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeBaseColor(System.Windows.Application,System.String)">
            <summary>
            Change base color for the given application.
            </summary>
            <param name="app">The application to change.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeBaseColor(System.Windows.FrameworkElement,System.String)">
            <summary>
            Change base color for the given window.
            </summary>
            <param name="frameworkElement">The FrameworkElement to change.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeBaseColor(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,System.String)">
            <summary>
            Change base color of for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="oldTheme">The old/current theme.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeColorScheme(System.Windows.Application,System.String)">
            <summary>
            Change color scheme for the given application.
            </summary>
            <param name="app">The application to change.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeColorScheme(System.Windows.FrameworkElement,System.String)">
            <summary>
            Change color scheme for the given window.
            </summary>
            <param name="frameworkElement">The FrameworkElement to change.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeColorScheme(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,System.String)">
            <summary>
            Change color scheme for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="oldTheme">The old/current theme.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ApplyThemeResourcesFromTheme(System.Windows.ResourceDictionary,ControlzEx.Theming.Theme)">
            <summary>
            Changes the theme of a ResourceDictionary directly.
            </summary>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="newTheme">The theme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme">
            <summary>
            Scans the resources and returns it's theme.
            </summary>
            <remarks>If the theme can't be detected from the <see cref="P:System.Windows.Application.MainWindow"/> we try to detect it from <see cref="P:System.Windows.Application.Current"/>.</remarks>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme(System.Windows.Application)">
            <summary>
            Scans the application resources and returns it's theme.
            </summary>
            <param name="app">The Application instance to scan.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme(System.Windows.FrameworkElement)">
            <summary>
            Scans the resources and returns it's theme.
            </summary>
            <param name="frameworkElement">The FrameworkElement to scan.</param>
            <remarks>If the theme can't be detected from the <paramref name="frameworkElement"/> we try to detect it from <see cref="P:System.Windows.Application.Current"/>.</remarks>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme(System.Windows.ResourceDictionary)">
            <summary>
            Scans a resources and returns it's theme.
            </summary>
            <param name="resourceDictionary">The ResourceDictionary to scan.</param>
        </member>
        <member name="E:ControlzEx.Theming.ThemeManager.ThemeChanged">
            <summary>
            This event fires if the theme was changed
            this should be using the weak event pattern, but for now it's enough
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.OnThemeChanged(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,ControlzEx.Theming.Theme)">
            <summary>
            Invalidates global colors and resources.
            Sometimes the ContextMenu is not changing the colors, so this will fix it.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.SyncWithAppMode">
            <summary>
            Gets or sets whether changes to the "app mode" setting from windows should be detected at runtime and the current <see cref="T:ControlzEx.Theming.Theme"/> be changed accordingly.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.SyncWithAccent">
            <summary>
            Gets or sets whether changes to the accent color settings from windows should be detected at runtime and the current <see cref="T:ControlzEx.Theming.Theme"/> be changed accordingly.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.SyncWithHighContrast">
            <summary>
            Gets or sets whether changes to the high contrast setting from windows should be detected at runtime and the current <see cref="T:ControlzEx.Theming.Theme"/> be changed accordingly.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.XamlThemeHelper.FixXamlReaderXmlNsIssue(System.String)">
            <summary>
            Works around an issue in the XamlReader.
            Without this fix the XamlReader would not be able to read the XAML we produced earlier because it does not know where to look for the types.
            The real issue is that we can't use the full namespace, with assembly hint, at compile time of the original project because said assembly does not yet exist and would cause a compile time error.
            Hence we have to use this workaround to enable both.
            The issue 
            </summary>
            <returns>The fixed version of <paramref name="xamlContent"/>.</returns>
            <example>
            If you have the following in your XAML file:
            xmlns:markup="clr-namespace:MahApps.Metro.Markup"
            xmlns:markupWithAssembly="clr-namespace:MahApps.Metro.Markup;assembly=MahApps.Metro"
            It get's converted to:
            xmlns:markup="clr-namespace:MahApps.Metro.Markup;assembly=MahApps.Metro"
            xmlns:markupWithAssembly="clr-namespace:MahApps.Metro.Markup;assembly=MahApps.Metro"
            </example>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.GetAutoMove(System.Windows.Controls.ToolTip)">
            <summary>
            Indicates whether a tooltip should follow the mouse cursor.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.SetAutoMove(System.Windows.Controls.ToolTip,System.Boolean)">
            <summary>
            Sets whether a tooltip should follow the mouse cursor.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.GetAutoMoveHorizontalOffset(System.Windows.Controls.ToolTip)">
            <summary>
            Gets the horizontal offset for the relative placement of the Tooltip.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.SetAutoMoveHorizontalOffset(System.Windows.Controls.ToolTip,System.Double)">
            <summary>
            Sets the horizontal offset for the relative placement of the Tooltip.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.GetAutoMoveVerticalOffset(System.Windows.Controls.ToolTip)">
            <summary>
            Gets the vertical offset for the relative placement of the Tooltip.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.SetAutoMoveVerticalOffset(System.Windows.Controls.ToolTip,System.Double)">
            <summary>
            Sets the vertical offset for the relative placement of the Tooltip.
            </summary>
        </member>
    </members>
</doc>
