﻿#pragma checksum "..\..\HalconSerializationTestWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "97093D6082B5E05E3CBC78A8BDD51BE89604242FB16FDF365B09B95A737D65D4"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MoonLight.App.Demo {
    
    
    /// <summary>
    /// HalconSerializationTestWindow
    /// </summary>
    public partial class HalconSerializationTestWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 31 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateHTupleBtn;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SerializeHTupleBtn;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeserializeHTupleBtn;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HTupleInfoTextBox;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HTupleJsonTextBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateHRegionBtn;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SerializeHRegionBtn;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeserializeHRegionBtn;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HRegionInfoTextBox;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HRegionJsonTextBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateHImageBtn;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SerializeHImageBtn;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeserializeHImageBtn;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HImageInfoTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HImageJsonTextBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BatchTestBtn;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PerformanceTestBtn;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveToFileBtn;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadFromFileBtn;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BatchTestResultTextBox;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\HalconSerializationTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MoonLight.App.Demo;component/halconserializationtestwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\HalconSerializationTestWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CreateHTupleBtn = ((System.Windows.Controls.Button)(target));
            
            #line 31 "..\..\HalconSerializationTestWindow.xaml"
            this.CreateHTupleBtn.Click += new System.Windows.RoutedEventHandler(this.CreateHTuple_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SerializeHTupleBtn = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\HalconSerializationTestWindow.xaml"
            this.SerializeHTupleBtn.Click += new System.Windows.RoutedEventHandler(this.SerializeHTuple_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DeserializeHTupleBtn = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\HalconSerializationTestWindow.xaml"
            this.DeserializeHTupleBtn.Click += new System.Windows.RoutedEventHandler(this.DeserializeHTuple_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.HTupleInfoTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.HTupleJsonTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CreateHRegionBtn = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\HalconSerializationTestWindow.xaml"
            this.CreateHRegionBtn.Click += new System.Windows.RoutedEventHandler(this.CreateHRegion_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SerializeHRegionBtn = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\HalconSerializationTestWindow.xaml"
            this.SerializeHRegionBtn.Click += new System.Windows.RoutedEventHandler(this.SerializeHRegion_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DeserializeHRegionBtn = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\HalconSerializationTestWindow.xaml"
            this.DeserializeHRegionBtn.Click += new System.Windows.RoutedEventHandler(this.DeserializeHRegion_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.HRegionInfoTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.HRegionJsonTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.CreateHImageBtn = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\HalconSerializationTestWindow.xaml"
            this.CreateHImageBtn.Click += new System.Windows.RoutedEventHandler(this.CreateHImage_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SerializeHImageBtn = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\HalconSerializationTestWindow.xaml"
            this.SerializeHImageBtn.Click += new System.Windows.RoutedEventHandler(this.SerializeHImage_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.DeserializeHImageBtn = ((System.Windows.Controls.Button)(target));
            
            #line 85 "..\..\HalconSerializationTestWindow.xaml"
            this.DeserializeHImageBtn.Click += new System.Windows.RoutedEventHandler(this.DeserializeHImage_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.HImageInfoTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.HImageJsonTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.BatchTestBtn = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\HalconSerializationTestWindow.xaml"
            this.BatchTestBtn.Click += new System.Windows.RoutedEventHandler(this.BatchTest_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PerformanceTestBtn = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\HalconSerializationTestWindow.xaml"
            this.PerformanceTestBtn.Click += new System.Windows.RoutedEventHandler(this.PerformanceTest_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.SaveToFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\HalconSerializationTestWindow.xaml"
            this.SaveToFileBtn.Click += new System.Windows.RoutedEventHandler(this.SaveToFile_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.LoadFromFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\HalconSerializationTestWindow.xaml"
            this.LoadFromFileBtn.Click += new System.Windows.RoutedEventHandler(this.LoadFromFile_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.BatchTestResultTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

