﻿++解决方案 'MoonLight' ‎ (41 个项目，共 39 个)
i:{00000000-0000-0000-0000-000000000000}:MoonLight.sln
++Solution Items
++.editorconfig
++05 设备类
++MoonLight.Devices.Motion
i:{a93d9933-a48b-4020-884d-186bfc797f97}:MoonLight.Devices.Motion
++Properties
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.motion\properties\
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\properties\
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\properties\
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\properties\
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\properties\
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\properties\
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\properties\
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\properties\
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\properties\
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\properties\
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\properties\
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\properties\
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\properties\
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\properties\
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\properties\
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\properties\
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\properties\
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\properties\
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\properties\
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\properties\
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\properties\
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\properties\
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\properties\
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\properties\
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\properties\
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\properties\
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\properties\
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\properties\
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\properties\
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\properties\
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\properties\
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\properties\
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\properties\
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\properties\
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\properties\
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\properties\
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\properties\
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\properties\
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\properties\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\properties\
++引用
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++AssemblyInfo.cs
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.motion\properties\assemblyinfo.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\properties\assemblyinfo.cs
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\properties\assemblyinfo.cs
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\properties\assemblyinfo.cs
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\properties\assemblyinfo.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\properties\assemblyinfo.cs
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\properties\assemblyinfo.cs
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\properties\assemblyinfo.cs
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\properties\assemblyinfo.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\properties\assemblyinfo.cs
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\properties\assemblyinfo.cs
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\properties\assemblyinfo.cs
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\properties\assemblyinfo.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\properties\assemblyinfo.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\properties\assemblyinfo.cs
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\properties\assemblyinfo.cs
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\properties\assemblyinfo.cs
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\properties\assemblyinfo.cs
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\properties\assemblyinfo.cs
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\properties\assemblyinfo.cs
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\properties\assemblyinfo.cs
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\properties\assemblyinfo.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\properties\assemblyinfo.cs
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\properties\assemblyinfo.cs
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\properties\assemblyinfo.cs
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\properties\assemblyinfo.cs
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\properties\assemblyinfo.cs
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\properties\assemblyinfo.cs
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\properties\assemblyinfo.cs
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\properties\assemblyinfo.cs
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\properties\assemblyinfo.cs
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\properties\assemblyinfo.cs
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\properties\assemblyinfo.cs
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\properties\assemblyinfo.cs
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\properties\assemblyinfo.cs
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\properties\assemblyinfo.cs
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\properties\assemblyinfo.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\properties\assemblyinfo.cs
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\properties\assemblyinfo.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\assemblyinfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\properties\assemblyinfo.cs
++Microsoft.CSharp
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Core
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Data
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Data.DataSetExtensions
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Net.Http
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Xml
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Xml.Linq
i:{456bf73e-6b91-48ce-a564-6f3a2fba5f98}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++MoonLight.Devices.Camera
i:{a93d9933-a48b-4020-884d-186bfc797f97}:MoonLight.Devices.Camera
++Basler.Pylon
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
++GxIAPINET
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++halcondotnetxl
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++MahApps.Metro
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++MoonLight
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{4f3a03be-d1fc-46d0-b3ae-4cf3aad28ecc}:MoonLight
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++MoonLight.Core
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{4f3a03be-d1fc-46d0-b3ae-4cf3aad28ecc}:MoonLight.Core
++MvCameraControl.Net
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++PresentationCore
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++PresentationFramework
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Xaml
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++WindowsBase
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{0804c515-cb6d-4422-8199-134218579ac1}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++Balser
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\balser\
++CameraBasler.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\balser\camerabasler.cs
++StatusBasler.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\balser\statusbasler.cs
++Daheng
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\daheng\
++CameraDaheng.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\daheng\cameradaheng.cs
++StatusDaheng.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\daheng\statusdaheng.cs
++Hik
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\hik\
++CameraHIK.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\hik\camerahik.cs
++StatusHIK.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\hik\statushik.cs
++app.config
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\app.config
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\app.config
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\app.config
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\app.config
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\app.config
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\app.config
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\app.config
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\app.config
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\app.config
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\app.config
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\app.config
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\app.config
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\app.config
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\app.config
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\app.config
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\app.config
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\app.config
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\app.config
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\app.config
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\app.config
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\app.config
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\app.config
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\app.config
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\app.config
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\app.config
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\app.config
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\app.config
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\app.config
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\app.config
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\app.config
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\app.config
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\app.config
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\app.config
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\app.config
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\app.config
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\app.config
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\app.config
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\app.config
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\app.config
++CameraView.xaml
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\cameraview.xaml
++CameraView.xaml.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\cameraview.xaml.cs
++CameraViewModel.cs
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.devices.camera\cameraviewmodel.cs
++MoonLight.App.Demo
i:{00000000-0000-0000-0000-000000000000}:MoonLight.App.Demo
++ControlzEx
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{************************************}:
++EventMgrLib
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{22317764-5f08-44d0-8089-346dfe980538}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:
i:{a460d352-b73e-468f-b57d-b87269c197a3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++ICSharpCode.CodeCompletion
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++Microsoft.Xaml.Behaviors
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:
i:{73d6fc2c-3bc6-4879-8fa0-9b6e85d73f12}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.ComponentModel.Composition
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{50db1135-5091-4644-9416-002cd4e7bc80}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Windows.Forms
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++WindowsFormsIntegration
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:
i:{f7132a01-96f2-478c-8550-a88d9421d156}:
i:{c9aec89a-77d4-408e-b97b-02addcada342}:
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:
i:{acfce13d-24b6-432c-af63-9e061041a935}:
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:
i:{853b9914-2808-42e6-9483-f5e8af91778d}:
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:
i:{944a5e32-f542-44f7-9986-911712db4463}:
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++App.config
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\app.config
++App.xaml
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\app.xaml
++MainLayoutPage.xaml
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\mainlayoutpage.xaml
++MainWindow.xaml
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\mainwindow.xaml
++MainWindowViewModel.cs
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\mainwindowviewmodel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainwindow\viewmodels\mainwindowviewmodel.cs
++Resources.resx
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\properties\resources.resx
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\properties\resources.resx
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\properties\resources.resx
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\properties\resources.resx
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\properties\resources.resx
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\properties\resources.resx
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\properties\resources.resx
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\properties\resources.resx
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\properties\resources.resx
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\properties\resources.resx
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\properties\resources.resx
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\properties\resources.resx
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\properties\resources.resx
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\properties\resources.resx
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\properties\resources.resx
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\properties\resources.resx
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\properties\resources.resx
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\properties\resources.resx
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\properties\resources.resx
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\properties\resources.resx
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\properties\resources.resx
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\properties\resources.resx
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\properties\resources.resx
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\properties\resources.resx
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\properties\resources.resx
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\properties\resources.resx
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\properties\resources.resx
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\properties\resources.resx
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\properties\resources.resx
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\properties\resources.resx
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\properties\resources.resx
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\properties\resources.resx
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\properties\resources.resx
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\properties\resources.resx
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\properties\resources.resx
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\properties\resources.resx
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\properties\resources.resx
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\resources.resx
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\properties\resources.resx
++Settings.settings
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\properties\settings.settings
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\properties\settings.settings
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\properties\settings.settings
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\properties\settings.settings
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\properties\settings.settings
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\properties\settings.settings
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\properties\settings.settings
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\properties\settings.settings
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\properties\settings.settings
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\properties\settings.settings
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\properties\settings.settings
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\properties\settings.settings
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\properties\settings.settings
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\properties\settings.settings
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\properties\settings.settings
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\properties\settings.settings
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\properties\settings.settings
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\properties\settings.settings
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\properties\settings.settings
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\properties\settings.settings
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\properties\settings.settings
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\properties\settings.settings
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\properties\settings.settings
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\properties\settings.settings
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\properties\settings.settings
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\properties\settings.settings
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\properties\settings.settings
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\properties\settings.settings
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\properties\settings.settings
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\properties\settings.settings
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\properties\settings.settings
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\properties\settings.settings
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\properties\settings.settings
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\properties\settings.settings
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\properties\settings.settings
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\properties\settings.settings
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\properties\settings.settings
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\settings.settings
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\properties\settings.settings
++App.xaml.cs
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\app.xaml.cs
++MainLayoutPage.xaml.cs
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\mainlayoutpage.xaml.cs
++MainWindow.xaml.cs
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\mainwindow.xaml.cs
++Resources.Designer.cs
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\properties\resources.designer.cs
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\properties\resources.designer.cs
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\properties\resources.designer.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\properties\resources.designer.cs
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\properties\resources.designer.cs
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\properties\resources.designer.cs
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\properties\resources.designer.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\properties\resources.designer.cs
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\properties\resources.designer.cs
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\properties\resources.designer.cs
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\properties\resources.designer.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\properties\resources.designer.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\properties\resources.designer.cs
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\properties\resources.designer.cs
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\properties\resources.designer.cs
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\properties\resources.designer.cs
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\properties\resources.designer.cs
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\properties\resources.designer.cs
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\properties\resources.designer.cs
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\properties\resources.designer.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\properties\resources.designer.cs
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\properties\resources.designer.cs
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\properties\resources.designer.cs
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\properties\resources.designer.cs
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\properties\resources.designer.cs
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\properties\resources.designer.cs
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\properties\resources.designer.cs
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\properties\resources.designer.cs
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\properties\resources.designer.cs
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\properties\resources.designer.cs
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\properties\resources.designer.cs
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\properties\resources.designer.cs
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\properties\resources.designer.cs
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\properties\resources.designer.cs
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\properties\resources.designer.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\properties\resources.designer.cs
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\properties\resources.designer.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\resources.designer.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\properties\resources.designer.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\location\resources.designer.cs
++Settings.Designer.cs
i:{8a9b470b-6676-44d2-b64b-44d8c3d4b321}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.app.demo\properties\settings.designer.cs
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\properties\settings.designer.cs
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\properties\settings.designer.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\properties\settings.designer.cs
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\properties\settings.designer.cs
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\properties\settings.designer.cs
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\properties\settings.designer.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\properties\settings.designer.cs
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\properties\settings.designer.cs
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\properties\settings.designer.cs
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\properties\settings.designer.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\properties\settings.designer.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\properties\settings.designer.cs
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\properties\settings.designer.cs
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\properties\settings.designer.cs
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\properties\settings.designer.cs
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\properties\settings.designer.cs
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\properties\settings.designer.cs
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\properties\settings.designer.cs
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\properties\settings.designer.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\properties\settings.designer.cs
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\properties\settings.designer.cs
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\properties\settings.designer.cs
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\properties\settings.designer.cs
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\properties\settings.designer.cs
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\properties\settings.designer.cs
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\properties\settings.designer.cs
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\properties\settings.designer.cs
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\properties\settings.designer.cs
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\properties\settings.designer.cs
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\properties\settings.designer.cs
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\properties\settings.designer.cs
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\properties\settings.designer.cs
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\properties\settings.designer.cs
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\properties\settings.designer.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\properties\settings.designer.cs
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\properties\settings.designer.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\settings.designer.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\properties\settings.designer.cs
++04 检测识别
++MoonLight.Modules.Matching
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.Matching
++Models
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\models\
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\models\
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\models\
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\models\
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\models\
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\models\
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\models\
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\models\
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\models\
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\models\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\models\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\models\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\models\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\models\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\models\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\models\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\models\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\models\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\models\
++ViewModels
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\viewmodels\
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\viewmodels\
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\viewmodels\
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\viewmodels\
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\viewmodels\
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\viewmodels\
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\viewmodels\
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\viewmodels\
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\viewmodels\
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\viewmodels\
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\viewmodels\
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\viewmodels\
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\viewmodels\
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\viewmodels\
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\viewmodels\
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\viewmodels\
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\viewmodels\
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\viewmodels\
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\viewmodels\
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\viewmodels\
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\viewmodels\
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\viewmodels\
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\viewmodels\
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\viewmodels\
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\viewmodels\
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\viewmodels\
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\viewmodels\
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\viewmodels\
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\viewmodels\
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\viewmodels\
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\viewmodels\
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\viewmodels\
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\viewmodels\
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\viewmodels\
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\viewmodels\
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainwindow\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\viewmodels\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\viewmodels\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\viewmodels\
++Views
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\views\
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\views\
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\views\
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\views\
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\views\
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\views\
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\views\
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\views\
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\views\
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\views\
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\views\
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\views\
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\views\
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\views\
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\views\
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\views\
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\views\
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\views\
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\views\
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\views\
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\views\
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\views\
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\views\
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\views\
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\views\
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\views\
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\views\
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\views\
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\views\
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\views\
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\views\
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\views\
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\views\
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\views\
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\views\
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainwindow\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\views\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\views\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\views\
++MathTemplateModel.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\models\mathtemplatemodel.cs
++EditViewModel.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\viewmodels\editviewmodel.cs
++MatchingViewModel.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\viewmodels\matchingviewmodel.cs
++EditView.xaml
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\views\editview.xaml
++EditView.xaml.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\views\editview.xaml.cs
++MatchingView.xaml
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\views\matchingview.xaml
++MatchPropertyView.xaml
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\views\matchpropertyview.xaml
++MatchingView.xaml.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\views\matchingview.xaml.cs
++MatchPropertyView.xaml.cs
i:{6169dfda-5242-4aab-8557-1ffa13a12984}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.module.matching\views\matchpropertyview.xaml.cs
++MoonLight.Modules.QRCode
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.QRCode
++QRCodeViewModel.cs
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\viewmodels\qrcodeviewmodel.cs
++QRCodePropertyView.xaml
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\views\qrcodepropertyview.xaml
++QRCodeView.xaml
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\views\qrcodeview.xaml
++QRCodePropertyView.xaml.cs
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\views\qrcodepropertyview.xaml.cs
++QRCodeView.xaml.cs
i:{86beda53-f39c-4b76-bef5-f0dec5773a79}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.qrcode\views\qrcodeview.xaml.cs
++01 图像处理
++MoonLight.Modules.PerProcessing
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.PerProcessing
++Resources
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\resources\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\resources\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\resources\
++add.png
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\resources\add.png
++delete.png
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\resources\delete.png
++down.png
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\resources\down.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\common\down.png
++PerProcessing.png
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\resources\perprocessing.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\perprocessing.png
++up.png
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\resources\up.png
++PerProcessingViewModel.cs
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\viewmodels\perprocessingviewmodel.cs
++PerProcessingPropertyView.xaml
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\views\perprocessingpropertyview.xaml
++PerProcessingView.xaml
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\views\perprocessingview.xaml
++PerProcessingPropertyView.xaml.cs
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\views\perprocessingpropertyview.xaml.cs
++PerProcessingView.xaml.cs
i:{8fea7058-af39-466d-a0f1-64d961ae736e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.perprocessing\views\perprocessingview.xaml.cs
++MoonLight.Modules.GrabImage
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.GrabImage
++ImageNameModel.cs
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\models\imagenamemodel.cs
++GrabImageViewModel.cs
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\viewmodels\grabimageviewmodel.cs
++GrabImagePropertyView.xaml
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\views\grabimagepropertyview.xaml
++GrabImageView.xaml
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\views\grabimageview.xaml
++GrabImagePropertyView.xaml.cs
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\views\grabimagepropertyview.xaml.cs
++GrabImageView.xaml.cs
i:{f7132a01-96f2-478c-8550-a88d9421d156}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.grabimage\views\grabimageview.xaml.cs
++MoonLight.Modules.Blob
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.Blob
++BinarizationParam.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\models\binarizationparam.cs
++DealResultModel.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\models\dealresultmodel.cs
++BlobViewModel.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\viewmodels\blobviewmodel.cs
++BlobPropertyView.xaml
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\views\blobpropertyview.xaml
++BlobView.xaml
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\views\blobview.xaml
++BlobPropertyView.xaml.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\views\blobpropertyview.xaml.cs
++BlobView.xaml.cs
i:{c9aec89a-77d4-408e-b97b-02addcada342}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.blob\views\blobview.xaml.cs
++MoonLight.Modules.SaveImage
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.SaveImage
++SaveImageViewModel.cs
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\viewmodels\saveimageviewmodel.cs
++SaveImagePropertyView.xaml
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\views\saveimagepropertyview.xaml
++SaveImageView.xaml
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\views\saveimageview.xaml
++SaveImagePropertyView.xaml.cs
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\views\saveimagepropertyview.xaml.cs
++SaveImageView.xaml.cs
i:{9dedb416-9028-403c-8d4d-edb08034fe78}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.saveimage\views\saveimageview.xaml.cs
++02 定位对位
++MoonLight.Modules.NPointCal
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.NPointCal
++NPointCalViewModel.cs
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\viewmodels\npointcalviewmodel.cs
++NPointCalPropertyView.xaml
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\views\npointcalpropertyview.xaml
++NPointCalView.xaml
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\views\npointcalview.xaml
++NPointCalPropertyView.xaml.cs
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\views\npointcalpropertyview.xaml.cs
++NPointCalView.xaml.cs
i:{2f2102f1-9555-4959-bd3c-b7b772d6757b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.npointcal\views\npointcalview.xaml.cs
++MoonLight.Modules.RobotControl
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.RobotControl
++RobotControlViewModel.cs
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\viewmodels\robotcontrolviewmodel.cs
++RobotControlPropertyView.xaml
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\views\robotcontrolpropertyview.xaml
++RobotControlView.xaml
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\views\robotcontrolview.xaml
++RobotControlPropertyView.xaml.cs
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\views\robotcontrolpropertyview.xaml.cs
++RobotControlView.xaml.cs
i:{00cee3f9-0b1c-475e-8d64-d74adde81379}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.robotcontrol\views\robotcontrolview.xaml.cs
++MoonLight.Modules.CoordinateMap
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.CoordinateMap
++CoordinateMapViewModel.cs
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\viewmodels\coordinatemapviewmodel.cs
++CoordinateMapPropertyView.xaml
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\views\coordinatemappropertyview.xaml
++CoordinateMapView.xaml
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\views\coordinatemapview.xaml
++CoordinateMapPropertyView.xaml.cs
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\views\coordinatemappropertyview.xaml.cs
++CoordinateMapView.xaml.cs
i:{22f4163f-6b5d-487d-9c2e-972742deb665}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinatemap\views\coordinatemapview.xaml.cs
++MoonLight.Modules.Coordinate
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.Coordinate
++CoordinateViewModel.cs
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\viewmodels\coordinateviewmodel.cs
++CoordinatePropertyView.xaml
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\views\coordinatepropertyview.xaml
++CoordinateView.xaml
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\views\coordinateview.xaml
++CoordinatePropertyView.xaml.cs
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\views\coordinatepropertyview.xaml.cs
++CoordinateView.xaml.cs
i:{62b673bc-ceb3-4066-8674-1a1e6f3bc782}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.coordinate\views\coordinateview.xaml.cs
++MoonLight.Modules.MeasureCalib
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.MeasureCalib
++MeasureCalibViewModel.cs
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\viewmodels\measurecalibviewmodel.cs
++MeasureCalibPropertyView.xaml
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\views\measurecalibpropertyview.xaml
++MeasureCalibView.xaml
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\views\measurecalibview.xaml
++MeasureCalibPropertyView.xaml.cs
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\views\measurecalibpropertyview.xaml.cs
++MeasureCalibView.xaml.cs
i:{944a5e32-f542-44f7-9986-911712db4463}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecalib\views\measurecalibview.xaml.cs
++03 测量工具
++MoonLight.Modules.Measuring
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.Measuring
++MeasuringViewModel.cs
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\viewmodels\measuringviewmodel.cs
++MeasuringPropertyView.xaml
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\views\measuringpropertyview.xaml
++MeasuringView.xaml
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\views\measuringview.xaml
++MeasuringPropertyView.xaml.cs
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\views\measuringpropertyview.xaml.cs
++MeasuringView.xaml.cs
i:{6e154bd2-f6ee-46df-9fa5-15f1345ba4aa}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measuring\views\measuringview.xaml.cs
++MoonLight.Modules.MeasureCircle
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.MeasureCircle
++MeasureCircleViewModel.cs
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\viewmodels\measurecircleviewmodel.cs
++MeasureCirclePropertyView.xaml
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\views\measurecirclepropertyview.xaml
++MeasureCircleView.xaml
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\views\measurecircleview.xaml
++MeasureCirclePropertyView.xaml.cs
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\views\measurecirclepropertyview.xaml.cs
++MeasureCircleView.xaml.cs
i:{853b9914-2808-42e6-9483-f5e8af91778d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurecircle\views\measurecircleview.xaml.cs
++MoonLight.Modules.FitLine
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.FitLine
++CoordinateModel.cs
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\models\coordinatemodel.cs
++FitLineViewModel.cs
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\viewmodels\fitlineviewmodel.cs
++FitLinePropertyView.xaml
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\views\fitlinepropertyview.xaml
++FitLineView.xaml
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\views\fitlineview.xaml
++FitLinePropertyView.xaml.cs
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\views\fitlinepropertyview.xaml.cs
++FitLineView.xaml.cs
i:{49c2364b-1ee6-4986-b5b5-7dc878bcc87d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.fitline\views\fitlineview.xaml.cs
++MoonLight.Modules.MetricToolkit
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.MetricToolkit
++MetricToolkitViewModel.cs
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\viewmodels\metrictoolkitviewmodel.cs
++packages.config
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\packages.config
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\packages.config
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\packages.config
++MetricToolKitModel.cs
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\models\metrictoolkitmodel.cs
++MetricToolkitPropertyView.xaml
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\views\metrictoolkitpropertyview.xaml
++MetricToolkitView.xaml
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\views\metrictoolkitview.xaml
++MetricToolkitPropertyView.xaml.cs
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\views\metrictoolkitpropertyview.xaml.cs
++MetricToolkitView.xaml.cs
i:{d4de70da-1fc1-478c-8781-8bf7ea98b7a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.metrictoolkit\views\metrictoolkitview.xaml.cs
++MoonLight.Modules.MeasureLines
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.MeasureLines
++MeasureLinesModel.cs
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\models\measurelinesmodel.cs
++MeasureLinesViewModel.cs
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\viewmodels\measurelinesviewmodel.cs
++MeasureLinesPropertyView.xaml
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\views\measurelinespropertyview.xaml
++MeasureLinesView.xaml
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\views\measurelinesview.xaml
++MeasureLinesPropertyView.xaml.cs
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\views\measurelinespropertyview.xaml.cs
++MeasureLinesView.xaml.cs
i:{925a877d-946e-42b9-b1d8-3d18174ec92b}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measurelines\views\measurelinesview.xaml.cs
++MoonLight.Modules.MeasureLine
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.MeasureLine
++MeasureLineViewModel.cs
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\viewmodels\measurelineviewmodel.cs
++MeasureLinePropertyView.xaml
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\views\measurelinepropertyview.xaml
++MeasureLineView.xaml
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\views\measurelineview.xaml
++MeasureLinePropertyView.xaml.cs
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\views\measurelinepropertyview.xaml.cs
++MeasureLineView.xaml.cs
i:{99762013-03ce-4c6b-a7ab-166dc7577f94}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.measureline\views\measurelineview.xaml.cs
++MoonLight.Modules.BuildLl
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.BuildLl
++BuildLlViewModel.cs
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\viewmodels\buildllviewmodel.cs
++BuildLlPropertyView.xaml
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\views\buildllpropertyview.xaml
++BuildLlView.xaml
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\views\buildllview.xaml
++BuildLlPropertyView.xaml.cs
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\views\buildllpropertyview.xaml.cs
++BuildLlView.xaml.cs
i:{acfce13d-24b6-432c-af63-9e061041a935}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildll\views\buildllview.xaml.cs
++MoonLight.Modules.BuildPp
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.BuildPp
++BuildPpViewModel.cs
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\viewmodels\buildppviewmodel.cs
++BuildPpPropertyView.xaml
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\views\buildpppropertyview.xaml
++BuildPpView.xaml
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\views\buildppview.xaml
++BuildPpPropertyView.xaml.cs
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\views\buildpppropertyview.xaml.cs
++BuildPpView.xaml.cs
i:{e7dca6f1-827f-43ec-8784-b12839234ad9}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpp\views\buildppview.xaml.cs
++MoonLight.Modules.BuildPL
i:{b2960e03-1cd3-4be8-b76e-6107f39b7fbc}:MoonLight.Modules.BuildPL
++BuildPLViewModel.cs
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\viewmodels\buildplviewmodel.cs
++BuildPLPropertyView.xaml
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\views\buildplpropertyview.xaml
++BuildPLView.xaml
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\views\buildplview.xaml
++BuildPLPropertyView.xaml.cs
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\views\buildplpropertyview.xaml.cs
++BuildPLView.xaml.cs
i:{32bf945d-0905-477c-9a27-b5c1b9998c9d}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.buildpl\views\buildplview.xaml.cs
++09 其他
++MoonLight.Modules.StopWhile
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.StopWhile
++StopWhileViewModel.cs
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\viewmodels\stopwhileviewmodel.cs
++StopWhilePropertyView.xaml
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\views\stopwhilepropertyview.xaml
++StopWhilePropertyView.xaml.cs
i:{0804c515-cb6d-4422-8199-134218579ac1}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.stopwhile\views\stopwhilepropertyview.xaml.cs
++MoonLight.Modules.Delay
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.Delay
++DelayViewModel.cs
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\viewmodels\delayviewmodel.cs
++DelayPropertyView.xaml
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\views\delaypropertyview.xaml
++DelayView.xaml
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\views\delayview.xaml
++DelayPropertyView.xaml.cs
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\views\delaypropertyview.xaml.cs
++DelayView.xaml.cs
i:{22317764-5f08-44d0-8089-346dfe980538}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.delay\views\delayview.xaml.cs
++MoonLight.Modules.If
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.If
++ICSharpCode.AvalonEdit
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
++ScintillaNET
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
++System.Drawing
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++ExpressionViewModel.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\viewmodels\expressionviewmodel.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\viewmodels\expressionviewmodel.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\viewmodels\expressionviewmodel.cs
++IfViewModel.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\viewmodels\ifviewmodel.cs
++ExpressionView.xaml
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\views\expressionview.xaml
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\views\expressionview.xaml
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\views\expressionview.xaml
++IfPropertyView.xaml
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\views\ifpropertyview.xaml
++IfView.xaml
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\views\ifview.xaml
++ExpressionView.xaml.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\views\expressionview.xaml.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\views\expressionview.xaml.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\views\expressionview.xaml.cs
++IfPropertyView.xaml.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\views\ifpropertyview.xaml.cs
++IfView.xaml.cs
i:{9b4dfb06-26bb-4d61-8bc8-407dae95a9da}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.if\views\ifview.xaml.cs
++MoonLight.Modules.SystemTime
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.SystemTime
++SystemTimeViewModel.cs
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\viewmodels\systemtimeviewmodel.cs
++SystemTimePropertyView.xaml
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\views\systemtimepropertyview.xaml
++SystemTimeView.xaml
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\views\systemtimeview.xaml
++SystemTimePropertyView.xaml.cs
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\views\systemtimepropertyview.xaml.cs
++SystemTimeView.xaml.cs
i:{da58d8a7-5b3f-4f57-8d91-92eb650eef00}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.systemtime\views\systemtimeview.xaml.cs
++MoonLight.Modules.Folder
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.Folder
++FolderViewModel.cs
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\viewmodels\folderviewmodel.cs
++FolderView.xaml
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\views\folderview.xaml
++FolderView.xaml.cs
i:{c095bb7c-b12b-43d1-bfa7-a3240eb31bc5}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.folder\views\folderview.xaml.cs
++MoonLight.Modules.TimeSlice
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.TimeSlice
++TimeSliceViewModel.cs
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\viewmodels\timesliceviewmodel.cs
++TimeSlicePropertyView.xaml
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\views\timeslicepropertyview.xaml
++TimeSliceView.xaml
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\views\timesliceview.xaml
++TimeSlicePropertyView.xaml.cs
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\views\timeslicepropertyview.xaml.cs
++TimeSliceView.xaml.cs
i:{96b4685e-63ce-4e9d-8c6d-bd41afe2a5b6}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.timeslice\views\timesliceview.xaml.cs
++MoonLight.Modules.VarDefine
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.VarDefine
++VarDefineViewModel.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\viewmodels\vardefineviewmodel.cs
++VarDefinePropertyView.xaml
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\views\vardefinepropertyview.xaml
++VarDefineView.xaml
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\views\vardefineview.xaml
++VarDefinePropertyView.xaml.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\views\vardefinepropertyview.xaml.cs
++VarDefineView.xaml.cs
i:{6536c396-ce87-49d7-b312-99dfb3bb3349}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.vardefine\views\vardefineview.xaml.cs
++MoonLight.Modules.VarSet
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.VarSet
++VarSetModel.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\models\varsetmodel.cs
++VarSetViewModel.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\viewmodels\varsetviewmodel.cs
++VarSetPropertyView.xaml
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\views\varsetpropertyview.xaml
++VarSetView.xaml
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\views\varsetview.xaml
++VarSetPropertyView.xaml.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\views\varsetpropertyview.xaml.cs
++VarSetView.xaml.cs
i:{663e72f2-599f-4d8c-aa6b-79b73eff79d3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.varset\views\varsetview.xaml.cs
++MoonLight.Modules.DataCheck
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.DataCheck
++DataCheckModel.cs
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\models\datacheckmodel.cs
++DataCheckViewModel.cs
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\viewmodels\datacheckviewmodel.cs
++DataCheckPropertyView.xaml
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\views\datacheckpropertyview.xaml
++DataCheckView.xaml
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\views\datacheckview.xaml
++DataCheckPropertyView.xaml.cs
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\views\datacheckpropertyview.xaml.cs
++DataCheckView.xaml.cs
i:{364d4801-72bb-42f9-8700-b6a3b6bb2189}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.datacheck\views\datacheckview.xaml.cs
++MoonLight.Modules.SplitString
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.SplitString
++SplitStringModel.cs
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\models\splitstringmodel.cs
++SplitStringViewModel.cs
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\viewmodels\splitstringviewmodel.cs
++SplitStringPropertyView.xaml
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\views\splitstringpropertyview.xaml
++SplitStringView.xaml
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\views\splitstringview.xaml
++SplitStringPropertyView.xaml.cs
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\views\splitstringpropertyview.xaml.cs
++SplitStringView.xaml.cs
i:{a8632556-894d-4882-88f6-31ad42c8f70e}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.splitstring\views\splitstringview.xaml.cs
++MoonLight.Modules.While
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.While
++WhileViewModel.cs
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\viewmodels\whileviewmodel.cs
++WhilePropertyView.xaml
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\views\whilepropertyview.xaml
++WhileView.xaml
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\views\whileview.xaml
++WhilePropertyView.xaml.cs
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\views\whilepropertyview.xaml.cs
++WhileView.xaml.cs
i:{e4ee3af6-6c66-4b54-9029-64e160229023}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.while\views\whileview.xaml.cs
++MoonLight.Modules.ParallelBlock
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.ParallelBlock
++ParallelBlockViewModel.cs
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\viewmodels\parallelblockviewmodel.cs
++ParallelBlockPropertyView.xaml
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\views\parallelblockpropertyview.xaml
++ParallelBlockView.xaml
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\views\parallelblockview.xaml
++ParallelBlockPropertyView.xaml.cs
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\views\parallelblockpropertyview.xaml.cs
++ParallelBlockView.xaml.cs
i:{43b599f9-b534-4ba0-a474-5f780fb67c75}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.parallelblock\views\parallelblockview.xaml.cs
++MoonLight.Modules.Motion
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.Motion
++AxisMoveViewModel.cs
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\viewmodels\axismoveviewmodel.cs
++AxisMoveView.xaml
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\views\axismoveview.xaml
++AxisMoveView.xaml.cs
i:{dd3ab3cf-474f-45da-ad8f-f27dfe7edc8a}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\wpfcontrollibrary2\views\axismoveview.xaml.cs
++MoonLight.Modules.ReceiveStr
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.ReceiveStr
++ReceiveStrViewModel.cs
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\viewmodels\receivestrviewmodel.cs
++ReceiveStrPropertyView.xaml
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\views\receivestrpropertyview.xaml
++ReceiveStrView.xaml
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\views\receivestrview.xaml
++ReceiveStrPropertyView.xaml.cs
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\views\receivestrpropertyview.xaml.cs
++ReceiveStrView.xaml.cs
i:{50db1135-5091-4644-9416-002cd4e7bc80}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.receivestr\views\receivestrview.xaml.cs
++MoonLight.Modules.SendStr
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.SendStr
++SendStrViewModel.cs
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\viewmodels\sendstrviewmodel.cs
++SendStrPropertyView.xaml
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\views\sendstrpropertyview.xaml
++SendStrView.xaml
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\views\sendstrview.xaml
++SendStrPropertyView.xaml.cs
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\views\sendstrpropertyview.xaml.cs
++SendStrView.xaml.cs
i:{a460d352-b73e-468f-b57d-b87269c197a3}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.sendstr\views\sendstrview.xaml.cs
++MoonLight.Modules.ProjectOutput
i:{02ea681e-c7d8-13c7-8484-4ac65e1b71e8}:MoonLight.Modules.ProjectOutput
++TextModel.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\models\textmodel.cs
++ProjectOutputViewModel.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\viewmodels\projectoutputviewmodel.cs
++ProjectOutputPropertyView.xaml
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\views\projectoutputpropertyview.xaml
++ProjectOutputView.xaml
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\views\projectoutputview.xaml
++TextView.xaml
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\views\textview.xaml
++ProjectOutputPropertyView.xaml.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\views\projectoutputpropertyview.xaml.cs
++ProjectOutputView.xaml.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\views\projectoutputview.xaml.cs
++TextView.xaml.cs
i:{d869379c-0349-4d10-9fd4-ec3cee4600ed}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.modules.projectoutput\views\textview.xaml.cs
++Platforms
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\
++UI
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\
++Resources.zh-Hans.resx
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\resources.zh-hans.resx
++AvalonDock
i:{************************************}:
++DotNetProjects.Wpf.Extended.Toolkit
i:{************************************}:
++PresentationFramework.Aero
i:{************************************}:
++System.ComponentModel.DataAnnotations
i:{************************************}:
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Configuration
i:{************************************}:
++System.Runtime.Serialization
i:{************************************}:
++System.Web
i:{************************************}:
++System.Web.Extensions
i:{************************************}:
++Action.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\action.cs
++ActionExecutionContext.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\actionexecutioncontext.cs
++ActionMessage.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\actionmessage.cs
++ActivateExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\activateextensions.cs
++ActivationEventArgs.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\activationeventargs.cs
++ActivationProcessedEventArgs.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\activationprocessedeventargs.cs
++AssemblySource.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\assemblysource.cs
++AsyncEventHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\asynceventhandler.cs
++AsyncEventHandlerExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\asynceventhandlerextensions.cs
++AttachedCollection.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\attachedcollection.cs
++Bind.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\bind.cs
++BindableCollection.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\bindablecollection.cs
++BindingScope.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\bindingscope.cs
++BooleanToVisibilityConverter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\booleantovisibilityconverter.cs
++Bootstrapper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\bootstrapper.cs
++ChildResolver.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\childresolver.cs
++CloseResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\closeresult.cs
++Conductor.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\conductor.cs
++ConductorBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\conductorbase.cs
++ConductorBaseWithActiveItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\conductorbasewithactiveitem.cs
++ConductorExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\conductorextensions.cs
++ConductorWithCollectionAllActive.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\conductorwithcollectionallactive.cs
++ConductorWithCollectionOneActive.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\conductorwithcollectiononeactive.cs
++ContainerExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\containerextensions.cs
++ContinueResultDecorator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\continueresultdecorator.cs
++ConventionManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\conventionmanager.cs
++Coroutine.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\coroutine.cs
++CoroutineExecutionContext.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\coroutineexecutioncontext.cs
++DeactivateExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\deactivateextensions.cs
++DeactivationEventArgs.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\deactivationeventargs.cs
++DebugLog.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\debuglog.cs
++DefaultCloseStrategy.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\defaultclosestrategy.cs
++DefaultPlatformProvider.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\defaultplatformprovider.cs
++DelegateResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\delegateresult.cs
++DependencyPropertyHelper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\dependencypropertyhelper.cs
++ElementConvention.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\elementconvention.cs
++EnumerableExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\enumerableextensions.cs
++EventAggregator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\eventaggregator.cs
++EventAggregatorExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\eventaggregatorextensions.cs
++Execute.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\execute.cs
++ExpressionExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\expressionextensions.cs
++ExtensionMethods.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\extensionmethods.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\extensionmethods.cs
++FrameAdapter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\frameadapter.cs
++IActivate.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iactivate.cs
++IChild.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ichild.cs
++IClose.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iclose.cs
++ICloseResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\icloseresult.cs
++ICloseStrategy.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iclosestrategy.cs
++IConductor.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iconductor.cs
++IDeactivate.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ideactivate.cs
++IEventAggregator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ieventaggregator.cs
++IGuardClose.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iguardclose.cs
++IHandle.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ihandle.cs
++IHaveActiveItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ihaveactiveitem.cs
++IHaveDisplayName.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ihavedisplayname.cs
++IHaveParameters.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ihaveparameters.cs
++ILog.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ilog.cs
++INavigationService.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\inavigationservice.cs
++INotifyPropertyChangedEx.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\inotifypropertychangedex.cs
++IObservableCollection.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iobservablecollection.cs
++IoC.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\ioc.cs
++IParent.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iparent.cs
++IPlatformProvider.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iplatformprovider.cs
++IResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iresult.cs
++IScreen.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iscreen.cs
++IViewAware.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\iviewaware.cs
++LogManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\logmanager.cs
++Message.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\message.cs
++MessageBinder.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\messagebinder.cs
++NameTransformer.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\nametransformer.cs
++NavigationExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\navigationextensions.cs
++NavigationHelper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\navigationhelper.cs
++OverrideCancelResultDecorator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\overridecancelresultdecorator.cs
++Parameter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\parameter.cs
++Parser.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\parser.cs
++PlatformProvider.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\platformprovider.cs
++PropertyChangedBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\propertychangedbase.cs
++RegExHelper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\regexhelper.cs
++RescueResultDecorator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\rescueresultdecorator.cs
++ResultCompletionEventArgs.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\resultcompletioneventargs.cs
++ResultDecoratorBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\resultdecoratorbase.cs
++ResultExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\resultextensions.cs
++Screen.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\screen.cs
++ScreenExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\screenextensions.cs
++SequentialResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\sequentialresult.cs
++SimpleContainer.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\simplecontainer.cs
++SimpleResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\simpleresult.cs
++StringSplitter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\stringsplitter.cs
++TaskExtensions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\taskextensions.cs
++TaskResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\taskresult.cs
++TypeMappingConfiguration.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\typemappingconfiguration.cs
++View.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\view.cs
++ViewAttachedEventArgs.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\viewattachedeventargs.cs
++ViewAware.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\viewaware.cs
++ViewLocator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\viewlocator.cs
++ViewModelBinder.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\viewmodelbinder.cs
++ViewModelLocator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\viewmodellocator.cs
++WeakValueDictionary.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\weakvaluedictionary.cs
++WindowManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\windowmanager.cs
++XamlPlatformProvider.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\platforms\xamlplatformprovider.cs
++Framework
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\
++Modules
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\
++Themes
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\themes\
++AppBootstrapper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\appbootstrapper.cs
++Resources.zh-Hans.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\properties\resources.zh-hans.designer.cs
++Behaviors
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\behaviors\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\behaviors\
++Commands
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\commands\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\commands\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\commands\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\commands\
++Controls
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\controls\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\controls\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\controls\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\
++Markup
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\markup\
++Menus
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\
++Results
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\
++Services
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\services\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\services\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\services\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\
++ShaderEffects
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\shadereffects\
++Threading
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\threading\
++ToolBars
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\
++Utils
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\utils\
++Win32
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\win32\
++AsyncCommand.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\asynccommand.cs
++Document.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\document.cs
++IDocument.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\idocument.cs
++ILayoutItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\ilayoutitem.cs
++IModule.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\imodule.cs
++IPersistedDocument.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\ipersisteddocument.cs
++ITool.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\itool.cs
++IWindow.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\iwindow.cs
++LayoutItemBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\layoutitembase.cs
++ModuleBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\modulebase.cs
++PersistedDocument.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\persisteddocument.cs
++RelayCommand.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\relaycommand.cs
++Tool.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\tool.cs
++VisualTreeUtility.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\visualtreeutility.cs
++WindowBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\windowbase.cs
++MainMenu
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\
++MainWindow
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainwindow\
++Settings
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\
++Shell
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\
++StatusBar
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\
++Toolbox
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\
++UndoRedo
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\
++Icons
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\
++VS2013
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\
++Generic.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\generic.xaml
++BindableTreeViewSelectedItemBehavior.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\behaviors\bindabletreeviewselecteditembehavior.cs
++KeyboardFocusBehavior.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\behaviors\keyboardfocusbehavior.cs
++WindowOptionsBehavior.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\behaviors\windowoptionsbehavior.cs
++Command.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\command.cs
++CommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commanddefinition.cs
++CommandDefinitionAttribute.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commanddefinitionattribute.cs
++CommandDefinitionBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commanddefinitionbase.cs
++CommandHandlerAttribute.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commandhandlerattribute.cs
++CommandHandlerWrapper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commandhandlerwrapper.cs
++CommandKeyboardShortcut.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commandkeyboardshortcut.cs
++CommandKeyGestureService.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commandkeygestureservice.cs
++CommandListDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commandlistdefinition.cs
++CommandRouter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commandrouter.cs
++CommandService.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\commandservice.cs
++ExcludeCommandKeyboardShortcut.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\excludecommandkeyboardshortcut.cs
++ICommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\icommandhandler.cs
++ICommandKeyGestureService.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\icommandkeygestureservice.cs
++ICommandRerouter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\icommandrerouter.cs
++ICommandRouter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\icommandrouter.cs
++ICommandService.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\icommandservice.cs
++ICommandUiItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\icommanduiitem.cs
++TargetableCommand.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\commands\targetablecommand.cs
++ClippingHwndHost.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\clippinghwndhost.cs
++DynamicStyle.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\dynamicstyle.cs
++ExpanderEx.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\expanderex.cs
++HwndMouse.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\hwndmouse.cs
++HwndMouseEventArgs.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\hwndmouseeventargs.cs
++HwndMouseState.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\hwndmousestate.cs
++HwndWrapper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\hwndwrapper.cs
++SliderEx.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\controls\sliderex.cs
++TranslateExtension.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\markup\translateextension.cs
++CommandMenuItemDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\commandmenuitemdefinition.cs
++ExcludeMenuDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\excludemenudefinition.cs
++ExcludeMenuItemDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\excludemenuitemdefinition.cs
++ExcludeMenuItemGroupDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\excludemenuitemgroupdefinition.cs
++MenuBarDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\menubardefinition.cs
++MenuDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\menudefinition.cs
++MenuDefinitionBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\menudefinitionbase.cs
++MenuItemDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\menuitemdefinition.cs
++MenuItemGroupDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\menuitemgroupdefinition.cs
++TextMenuItemDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\menus\textmenuitemdefinition.cs
++IOpenResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\iopenresult.cs
++LambdaResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\lambdaresult.cs
++OpenDocumentResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\opendocumentresult.cs
++OpenResultBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\openresultbase.cs
++Show.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\show.cs
++ShowCommonDialogResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\showcommondialogresult.cs
++ShowDialogResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\showdialogresult.cs
++ShowToolResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\showtoolresult.cs
++ShowWindowResult.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\results\showwindowresult.cs
++EditorFileType.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\editorfiletype.cs
++IEditorProvider.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\ieditorprovider.cs
++IInputManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\iinputmanager.cs
++IMainWindow.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\imainwindow.cs
++InputBindingTrigger.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\inputbindingtrigger.cs
++InputManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\inputmanager.cs
++IResourceManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\iresourcemanager.cs
++IShell.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\ishell.cs
++PaneLocation.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\panelocation.cs
++ResourceManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\resourcemanager.cs
++ServiceProvider.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\serviceprovider.cs
++SettingsPropertyChangedEventManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\services\settingspropertychangedeventmanager.cs
++GrayscaleEffect.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\shadereffects\grayscaleeffect.cs
++GrayscaleEffect.ps
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\shadereffects\grayscaleeffect.ps
++ShaderEffectBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\shadereffects\shadereffectbase.cs
++ShaderEffectUtility.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\shadereffects\shadereffectutility.cs
++BlueTheme.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\themes\bluetheme.cs
++DarkTheme.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\themes\darktheme.cs
++ITheme.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\themes\itheme.cs
++IThemeManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\themes\ithememanager.cs
++LightTheme.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\themes\lighttheme.cs
++ThemeManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\themes\thememanager.cs
++TaskUtility.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\threading\taskutility.cs
++CommandToolBarItemDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\commandtoolbaritemdefinition.cs
++ExcludeToolBarDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\excludetoolbardefinition.cs
++ExcludeToolBarItemDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\excludetoolbaritemdefinition.cs
++ExcludeToolBarItemGroupDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\excludetoolbaritemgroupdefinition.cs
++ToolBarDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\toolbardefinition.cs
++ToolBarItemDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\toolbaritemdefinition.cs
++ToolBarItemGroupDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\toolbars\toolbaritemgroupdefinition.cs
++ItemsControlUtility.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\utils\itemscontrolutility.cs
++NativeMethods.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\framework\win32\nativemethods.cs
++Converters
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\converters\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\converters\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\converters\
++IMenu.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\imenu.cs
++IMenuBuilder.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\imenubuilder.cs
++MenuBuilder.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\menubuilder.cs
++MenuDefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\menudefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\menudefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\menudefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\menudefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\menudefinitions.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\menudefinitions.cs
++SampleData
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\sampledata\
++ISettingsEditor.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\isettingseditor.cs
++ISettingsEditorAsync.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\isettingseditorasync.cs
++ToolBarDefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\toolbardefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\toolbardefinitions.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\toolbardefinitions.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\toolbardefinitions.cs
++IStatusBar.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\istatusbar.cs
++IStatusBarView.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\istatusbarview.cs
++IToolBar.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\itoolbar.cs
++IToolBarBuilder.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\itoolbarbuilder.cs
++IToolBars.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\itoolbars.cs
++Module.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\module.cs
++ToolBarBuilder.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\toolbarbuilder.cs
++Design
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\design\
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\design\
++IToolbox.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\itoolbox.cs
++ToolboxDragDrop.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\toolboxdragdrop.cs
++ToolboxItemAttribute.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\toolboxitemattribute.cs
++IHistoryTool.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\ihistorytool.cs
++IUndoableAction.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\iundoableaction.cs
++IUndoRedoManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\iundoredomanager.cs
++AddSol.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\addsol.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\addsol.png
++CameraSet.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\cameraset.png
++CommunicationSet.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\communicationset.png
++FullScreen.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\fullscreen.png
++GlobalVar.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\globalvar.png
++MCT.ico
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\mct.ico
++Open.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\open.png
++Redo.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\redo.png
++RunCycle.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\runcycle.png
++RunOnce.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\runonce.png
++Save.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\save.png
++SaveAll.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\saveall.png
++Stop.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\stop.png
++UIDesign.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\uidesign.png
++Undo.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\undo.png
++UserLogin.png
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\resources\icons\userlogin.png
++BlueTheme.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\bluetheme.xaml
++DarkTheme.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\darktheme.xaml
++LightTheme.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\lighttheme.xaml
++MenuBehavior.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\behaviors\menubehavior.cs
++MenuEx.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\controls\menuex.cs
++MenuItemEx.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\controls\menuitemex.cs
++CultureInfoNameConverter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\converters\cultureinfonameconverter.cs
++CommandMenuItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\models\commandmenuitem.cs
++MenuItemBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\models\menuitembase.cs
++MenuItemSeparator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\models\menuitemseparator.cs
++MenuModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\models\menumodel.cs
++StandardMenuItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\models\standardmenuitem.cs
++TextMenuItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\models\textmenuitem.cs
++Styles.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\resources\styles.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\resources\styles.xaml
++MainMenuSettingsViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\viewmodels\mainmenusettingsviewmodel.cs
++MainMenuViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\viewmodels\mainmenuviewmodel.cs
++MainMenuSettingsView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\views\mainmenusettingsview.xaml
++MainMenuView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\views\mainmenuview.xaml
++MainWindowView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainwindow\views\mainwindowview.xaml
++OpenSettingsCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\commands\opensettingscommanddefinition.cs
++OpenSettingsCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\commands\opensettingscommandhandler.cs
++SettingsViewModelSampleData.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\sampledata\settingsviewmodelsampledata.xaml
++SettingsEditorWrapper.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\viewmodels\settingseditorwrapper.cs
++SettingsPageViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\viewmodels\settingspageviewmodel.cs
++SettingsViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\viewmodels\settingsviewmodel.cs
++SettingsView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\views\settingsview.xaml
++CloseFileCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\closefilecommanddefinition.cs
++CloseFileCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\closefilecommandhandler.cs
++ExitCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\exitcommanddefinition.cs
++ExitCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\exitcommandhandler.cs
++NewFileCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\newfilecommandhandler.cs
++NewFileCommandListDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\newfilecommandlistdefinition.cs
++OpenFileCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\openfilecommanddefinition.cs
++OpenFileCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\openfilecommandhandler.cs
++SaveAllFilesCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\saveallfilescommanddefinition.cs
++SaveAllFilesCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\saveallfilescommandhandler.cs
++SaveFileAsCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\savefileascommanddefinition.cs
++SaveFileCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\savefilecommanddefinition.cs
++SwitchToDocumentCommandListDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\switchtodocumentcommandlistdefinition.cs
++SwitchToDocumentListCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\switchtodocumentlistcommandhandler.cs
++ViewFullscreenCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\viewfullscreencommanddefinition.cs
++ViewFullscreenCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\commands\viewfullscreencommandhandler.cs
++LayoutInitializer.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\controls\layoutinitializer.cs
++PanesStyleSelector.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\controls\panesstyleselector.cs
++PanesTemplateSelector.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\controls\panestemplateselector.cs
++NullableValueConverter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\converters\nullablevalueconverter.cs
++TruncateMiddleConverter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\converters\truncatemiddleconverter.cs
++UriToImageSourceConverter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\converters\uritoimagesourceconverter.cs
++ILayoutItemStatePersister.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\services\ilayoutitemstatepersister.cs
++LayoutItemStatePersister.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\services\layoutitemstatepersister.cs
++ShellViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\viewmodels\shellviewmodel.cs
++IShellView.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\views\ishellview.cs
++LayoutUtility.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\views\layoututility.cs
++ShellView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\views\shellview.xaml
++StatusBarItemViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\viewmodels\statusbaritemviewmodel.cs
++StatusBarViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\viewmodels\statusbarviewmodel.cs
++StatusBarView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\views\statusbarview.xaml
++CustomToggleButton.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\controls\customtogglebutton.cs
++MainToolBar.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\controls\maintoolbar.cs
++ToolBarBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\controls\toolbarbase.cs
++ToolBarTrayContainer.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\controls\toolbartraycontainer.cs
++ToolPaneToolBar.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\controls\toolpanetoolbar.cs
++CommandToolBarItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\models\commandtoolbaritem.cs
++ToolBarItemBase.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\models\toolbaritembase.cs
++ToolBarItemSeparator.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\models\toolbaritemseparator.cs
++ToolBarModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\models\toolbarmodel.cs
++ToolBarsViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\viewmodels\toolbarsviewmodel.cs
++IToolBarView.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\views\itoolbarview.cs
++ToolBarsView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\views\toolbarsview.xaml
++ViewToolboxCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\commands\viewtoolboxcommanddefinition.cs
++ViewToolboxCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\commands\viewtoolboxcommandhandler.cs
++DesignTimeToolboxViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\design\designtimetoolboxviewmodel.cs
++ToolboxItem.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\models\toolboxitem.cs
++IToolboxService.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\services\itoolboxservice.cs
++ToolboxService.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\services\toolboxservice.cs
++ToolboxItemViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\viewmodels\toolboxitemviewmodel.cs
++ToolboxViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\viewmodels\toolboxviewmodel.cs
++ToolboxView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\views\toolboxview.xaml
++RedoCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\commands\redocommanddefinition.cs
++UndoCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\commands\undocommanddefinition.cs
++ViewHistoryCommandDefinition.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\commands\viewhistorycommanddefinition.cs
++ViewHistoryCommandHandler.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\commands\viewhistorycommandhandler.cs
++DesignTimeHistoryViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\design\designtimehistoryviewmodel.cs
++UndoRedoManager.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\services\undoredomanager.cs
++HistoryItemType.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\viewmodels\historyitemtype.cs
++HistoryItemViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\viewmodels\historyitemviewmodel.cs
++HistoryViewModel.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\viewmodels\historyviewmodel.cs
++HistoryView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\views\historyview.xaml
++Button.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\button.xaml
++CheckBox.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\checkbox.xaml
++ColorCanvas.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\colorcanvas.xaml
++ComboBox.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\combobox.xaml
++Focus.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\focus.xaml
++Label.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\label.xaml
++Menu.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\menu.xaml
++Merged.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\merged.xaml
++ScrollBar.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\scrollbar.xaml
++Slider.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\slider.xaml
++TabControl.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\tabcontrol.xaml
++TextBox.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\textbox.xaml
++Toolbar.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\toolbar.xaml
++Tooltip.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\tooltip.xaml
++TreeView.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\treeview.xaml
++Window.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\window.xaml
++WindowCommands.xaml
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\windowcommands.xaml
++MainMenuSettingsView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\views\mainmenusettingsview.xaml.cs
++MainMenuView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainmenu\views\mainmenuview.xaml.cs
++MainWindowView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\mainwindow\views\mainwindowview.xaml.cs
++SettingsView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\settings\views\settingsview.xaml.cs
++ShellView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\shell\views\shellview.xaml.cs
++StatusBarView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\statusbar\views\statusbarview.xaml.cs
++ToolBarsView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbars\views\toolbarsview.xaml.cs
++ToolboxView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\toolbox\views\toolboxview.xaml.cs
++HistoryView.xaml.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\modules\undoredo\views\historyview.xaml.cs
++TreeViewIndentConverter.cs
i:{************************************}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight\ui\themes\vs2013\controls\converters\treeviewindentconverter.cs
++CommunityToolkit.Mvvm
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++HandyControl
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++HslCommunication
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++Microsoft.Bcl.AsyncInterfaces
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++Microsoft.VisualBasic
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++Newtonsoft.Json
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Buffers
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.ComponentModel.Annotations
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Memory
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Numerics
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Numerics.Vectors
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Runtime.CompilerServices.Unsafe
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++System.Threading.Tasks.Extensions
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:
++Assets
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\
++Attatch
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\attatch\
++Button
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\button\
++PropertyGrid
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\
++Editors
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\
++PropertyEditorBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\propertyeditorbase.cs
++PropertyGrid.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\propertygrid.cs
++PropertyItem.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\propertyitem.cs
++PropertyItemsControl.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\propertyitemscontrol.cs
++PropertyResolver.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\propertyresolver.cs
++SearchBar.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\searchbar.cs
++Converter
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\
++Bool2ColorConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\bool2colorconverter.cs
++Bool2GreenColorConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\bool2greencolorconverter.cs
++Bool2LimeConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\bool2limeconverter.cs
++Bool2VisibilityConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\bool2visibilityconverter.cs
++Bool2VisibilityHiddenConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\bool2visibilityhiddenconverter.cs
++BoolToBrushConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\booltobrushconverter.cs
++BoolToInverseConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\booltoinverseconverter.cs
++BoolToLimeBrushConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\booltolimebrushconverter.cs
++BoolToRedBrushConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\booltoredbrushconverter.cs
++BrushRoundConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\brushroundconverter.cs
++EnumArrayToVisibilityConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\enumarraytovisibilityconverter.cs
++EnumConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\enumconverter.cs
++EnumToVisibilityConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\enumtovisibilityconverter.cs
++EnumToVisibilityConverter_2.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\enumtovisibilityconverter_2.cs
++ExpanderToBooleanConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\expandertobooleanconverter.cs
++IntToWobbleTypeConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\inttowobbletypeconverter.cs
++InvertBool2VisibilityConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\invertbool2visibilityconverter.cs
++InvertBoolConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\invertboolconverter.cs
++MediaColorToDrawingColorConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\mediacolortodrawingcolorconverter.cs
++NullableToVisibilityConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\nullabletovisibilityconverter.cs
++Object2IntOrDoubleConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\object2intordoubleconverter.cs
++ObjectToStringConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\objecttostringconverter.cs
++PathToImageConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\pathtoimageconverter.cs
++RowToIndexConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\rowtoindexconverter.cs
++StatusConverter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\converter\statusconverter.cs
++Data
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\data\
++Extension
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\extension\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\extension\
++Fonts
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\fonts\
++Images
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\
++Interactivity
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\interactivity\
++Location
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\location\
++Algorithms
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\algorithms\
++DeviceManager
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\
++OpenDeviceManagerCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\commands\opendevicemanagercommanddefinition.cs
++OpenDeviceManagerCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\commands\opendevicemanagercommandhandler.cs
++DeviceCategoryGroup.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\models\devicecategorygroup.cs
++DeviceManager.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\models\devicemanager.cs
++IDeviceManager.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\models\idevicemanager.cs
++DeviceManagerViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\viewmodels\devicemanagerviewmodel.cs
++DeviceTemplate.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\viewmodels\devicetemplate.cs
++DeviceManagerView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\views\devicemanagerview.xaml
++DeviceManagerView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\devicemanager\views\devicemanagerview.xaml.cs
++FlowManager
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\
++FlowTreeViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\viewmodels\flowtreeviewmodel.cs
++PropertyPanelViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\viewmodels\propertypanelviewmodel.cs
++RunManagerViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\viewmodels\runmanagerviewmodel.cs
++FlowTreeView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\views\flowtreeview.xaml
++PropertyPanelView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\views\propertypanelview.xaml
++RunManagerView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\views\runmanagerview.xaml
++Output
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\
++PosManager
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\
++RenderControl
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\
++ToolBar
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\toolbar\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\
++ToolBox
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\
++ViewToolBoxCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\commands\viewtoolboxcommanddefinition.cs
++ViewToolBoxCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\commands\viewtoolboxcommandhandler.cs
++ToolBoxViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\viewmodels\toolboxviewmodel.cs
++ToolBoxView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\views\toolboxview.xaml
++ToolOutput
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\
++Modules.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\modules.cs
++Styles
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\
++ButtonGroupStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\buttongroupstyle.xaml
++ButtonStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\buttonstyle.xaml
++Converter.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\converter.xaml
++Icon.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\icon.xaml
++MenuStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\menustyle.xaml
++PropertyGridStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\propertygridstyle.xaml
++RadioButtonStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\radiobuttonstyle.xaml
++TabControlStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\tabcontrolstyle.xaml
++TextBoxStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\textboxstyle.xaml
++TreeViewStyle.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\treeviewstyle.xaml
++Tools
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\tools\
++Collection.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\collection.xaml
++Attributes
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\attributes\
++PropertyAtrribute.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\attributes\propertyatrribute.cs
++ToolImageNameAttribute.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\attributes\toolimagenameattribute.cs
++bin
++Common
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\common\
++Container
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\container\
++Helper
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\
++AsyncObservableCollection.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\asyncobservablecollection.cs
++CloneObject.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\cloneobject.cs
++CommandBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\commandbase.cs
++EventTriggerAction.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\eventtriggeraction.cs
++FilePaths.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\filepaths.cs
++ImageTool.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\imagetool.cs
++ItemsControlHelp.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\itemscontrolhelp.cs
++MD5Provider.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\md5provider.cs
++NotifyPropertyBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\notifypropertybase.cs
++SerializeHelp.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\serializehelp.cs
++WPFCursorTool.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\wpfcursortool.cs
++WPFElementTool.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\wpfelementtool.cs
++XmlHelper.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\xmlhelper.cs
++Log
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\log\
++RightControl
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\rightcontrol\
++EComInfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\ecominfo.cs
++Communacation
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\
++Config
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\config\
++Defines
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\defines\
++CameraType.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\defines\cameratype.cs
++RotateAngle.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\defines\rotateangle.cs
++Devices
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\
++Camera
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\camera\
++CameraBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\camera\camerabase.cs
++CameraBaseEx.cs
++CameraInfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\camera\camerainfo.cs
++CameraSetView.xaml
++CameraSetView.xaml.cs
++CameraSetViewModel.cs
++DaHeng.cs
++Hik.cs
++ICamera.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\camera\icamera.cs
++ImageData.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\camera\imagedata.cs
++StatusCamera.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\camera\statuscamera.cs
++Communication
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\
++CommunicationBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\communicationbase.cs
++CommunicationSetView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\communicationsetview.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\views\communicationsetview.xaml
++CommunicationSetViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\communicationsetviewmodel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\viewmodels\communicationsetviewmodel.cs
++DMTcpClient.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\dmtcpclient.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\dmtcpclient.cs
++DMTcpServer.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\dmtcpserver.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\dmtcpserver.cs
++DMUdpClient.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\dmudpclient.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\dmudpclient.cs
++MySerialPort.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\serialport\myserialport.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\myserialport.cs
++ConfigBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\configbase.cs
++ConfigBaseEx.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\configbaseex.cs
++ConfigDevice.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\configdevice.cs
++DeviceBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\devicebase.cs
++DeviceStatus.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\devicestatus.cs
++DeviceTreeNode.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\devicetreenode.cs
++DeviceType.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\devicetype.cs
++IDevice.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\idevice.cs
++IDeviceConfiguration.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\ideviceconfiguration.cs
++StatusBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\statusbase.cs
++Enums
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\enums\
++Events
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\
++Interfaces
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\
++Motion
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\
++AxisControlViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\viewmodels\axiscontrolviewmodel.cs
++AxisControlView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\views\axiscontrolview.xaml
++AxisBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\axisbase.cs
++AxisStatus.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\axisstatus.cs
++CardBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\cardbase.cs
++ICard.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\icard.cs
++IOBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\iobase.cs
++MotionHelper.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\motionhelper.cs
++UnitStatus.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\unitstatus.cs
++obj
++ROIs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\
++HRoi.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\hroi.cs
++HText.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\htext.cs
++ROI.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roi.cs
++ROICaliper.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roicaliper.cs
++ROICaliper1D.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roicaliper1d.cs
++ROICircle.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roicircle.cs
++ROICircleCaliper.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roicirclecaliper.cs
++ROICoordinates.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roicoordinates.cs
++ROIInfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roiinfo.cs
++ROILine.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roiline.cs
++ROIRectangle.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roirectangle.cs
++ROIRectangle2.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roirectangle2.cs
++ROIRectCaliper.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roirectcaliper.cs
++ROIText.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\rois\roitext.cs
++Scrip
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\scrip\
++EngineService.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\engineservice.cs
++FileIconService.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\fileiconservice.cs
++FileSystemFactory.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\filesystemfactory.cs
++FileSystemService.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\filesystemservice.cs
++PluginService.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\pluginservice.cs
++Project.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\project.cs
++ProjectFlowTree.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\projectflowtree.cs
++ResourceHelper.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\resourcehelper.cs
++SolManager.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\solmanager.cs
++Solution.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\solution.cs
++SolutionInfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\services\solutioninfo.cs
++EditRemarksViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\viewmodels\editremarksviewmodel.cs
++HardwareConfigViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\viewmodels\hardwareconfigviewmodel.cs
++LoadingViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\viewmodels\loadingviewmodel.cs
++MessageBoxViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\viewmodels\messageboxviewmodel.cs
++VarLinkViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\viewmodels\varlinkviewmodel.cs
++ChangePwdView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\changepwdview.xaml
++EditRemarksView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\editremarksview.xaml
++HardwareConfigView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\hardwareconfigview.xaml
++LoadingView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\loadingview.xaml
++LoginView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\loginview.xaml
++MessageBoxView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\messageboxview.xaml
++VarLinkView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\varlinkview.xaml
++TitleElement.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\attatch\titleelement.cs
++VisualElement.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\attatch\visualelement.cs
++ButtonGroup.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\button\buttongroup.cs
++DatePropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\datepropertyeditor.cs
++DateTimePropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\datetimepropertyeditor.cs
++EnumPropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\enumpropertyeditor.cs
++HorizontalAlignmentPropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\horizontalalignmentpropertyeditor.cs
++ImagePropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\imagepropertyeditor.cs
++NumberPropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\numberpropertyeditor.cs
++PlainTextPropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\plaintextpropertyeditor.cs
++ReadOnlyTextPropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\readonlytextpropertyeditor.cs
++SwitchPropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\switchpropertyeditor.cs
++TimePropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\timepropertyeditor.cs
++VerticalAlignmentPropertyEditor.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\controls\propertygrid\editors\verticalalignmentpropertyeditor.cs
++ResourceToken.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\data\resourcetoken.cs
++ValueBoxes.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\data\valueboxes.cs
++EnumerableExtension.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\extension\enumerableextension.cs
++UIElementExtension.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\extension\uielementextension.cs
++digital_display.ttf
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\fonts\digital_display.ttf
++iconfont.ttf
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\fonts\iconfont.ttf
++Flow
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\
++Solution
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\
++Tool
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\tool\
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\
++Alarm.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\alarm.png
++Error.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\error.png
++Info.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\info.png
++Warn.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\warn.png
++ControlCommands.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\interactivity\controlcommands.cs
++Resource.en.resx
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\location\resource.en.resx
++Resource.resx
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\location\resource.resx
++Resource.zh.resx
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\location\resource.zh.resx
++VisionException.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\algorithms\visionexception.cs
++VisionLib.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\algorithms\visionlib.cs
++ViewFlowCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\commands\viewflowcommanddefinition.cs
++ViewFlowCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\commands\viewflowcommandhandler.cs
++ViewPropertyPanelCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\commands\viewpropertypanelcommanddefinition.cs
++ViewPropertyPanelCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\commands\viewpropertypanelcommandhandler.cs
++ViewRunManagerCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\commands\viewrunmanagercommanddefinition.cs
++ViewRunManagerCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\commands\viewrunmanagercommandhandler.cs
++FlowTreeView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\views\flowtreeview.xaml.cs
++PropertyPanelView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\views\propertypanelview.xaml.cs
++RunManagerView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\flowmanager\views\runmanagerview.xaml.cs
++Managers
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\managers\
++ToolBoxView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbox\views\toolboxview.xaml.cs
++ViewDeviceStatusCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\commands\viewdevicestatuscommanddefinition.cs
++ViewDeviceStatusCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\commands\viewdevicestatuscommandhandler.cs
++ViewToolOutputCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\commands\viewtooloutputcommanddefinition.cs
++ViewToolOutputCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\commands\viewtooloutputcommandhandler.cs
++ToolOutputModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\models\tooloutputmodel.cs
++DeviceStatusViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\viewmodels\devicestatusviewmodel.cs
++ToolOutputViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\viewmodels\tooloutputviewmodel.cs
++DeviceStatusView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\views\devicestatusview.xaml
++ToolOutputView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\views\tooloutputview.xaml
++Chip.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\styles\models\chip.cs
++Release
++x64
++Logger.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\log\logger.cs
++IsEnableControl.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\rightcontrol\isenablecontrol.cs
++RightControl.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\rightcontrol\rightcontrol.cs
++SerialPort
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\serialport\
++Socket
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\
++EComManageer.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\ecommanageer.cs
++Ecommunacation.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\ecommunacation.cs
++RImage.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\config\rimage.cs
++CommunicationSetView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\devices\communication\communicationsetview.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\views\communicationsetview.xaml.cs
++EnumType.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\enums\enumtype.cs
++UserType.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\enums\usertype.cs
++AddCameraEvent.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\addcameraevent.cs
++CurrentUserChangedEvent.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\currentuserchangedevent.cs
++FunctionEventArgs.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\functioneventargs.cs
++HardwareChangedEvent.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\hardwarechangedevent.cs
++ModuleOutChangedEvent.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\moduleoutchangedevent.cs
++OpenVarLinkViewEvent.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\openvarlinkviewevent.cs
++SoftwareExitEvent.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\softwareexitevent.cs
++VarChangedEvent.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\events\varchangedevent.cs
++ObservableCollectionExtension.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\extension\observablecollectionextension.cs
++IFileSystemFactory.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\ifilesystemfactory.cs
++IFileSystemService.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\ifilesystemservice.cs
++IFlow.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\iflow.cs
++IProcess.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\iprocess.cs
++IRenderView.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\irenderview.cs
++IRenderViewManager.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\irenderviewmanager.cs
++IToolUnit.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\interfaces\itoolunit.cs
++Sol
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\
++AlarmSummaryModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\alarmsummarymodel.cs
++CommonMethods.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\commonmethods.cs
++DragDropModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\dragdropmodel.cs
++Fit.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\fit.cs
++Gen.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\gen.cs
++IHierarchical.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\ihierarchical.cs
++Line.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\line.cs
++LinkVarModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\linkvarmodel.cs
++LogModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\logmodel.cs
++MachineModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\machinemodel.cs
++MotionBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\motionbase.cs
++PluginsInfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\pluginsinfo.cs
++ProjectInfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\projectinfo.cs
++ToolList.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\toollist.cs
++ToolNode.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\toolnode.cs
++ToolUnit.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\toolunit.cs
++ToolViewBase.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\toolviewbase.cs
++TreeNode.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\treenode.cs
++UserModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\usermodel.cs
++VarModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\varmodel.cs
++VisionInfo.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\visioninfo.cs
++VisionParam.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\visionparam.cs
++VisionResult.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\visionresult.cs
++AxisControlView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\motion\views\axiscontrolview.xaml.cs
++Debug
++BoolScriptSupport.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\scrip\boolscriptsupport.cs
++BoolScriptTemplate.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\scrip\boolscripttemplate.cs
++ExpressionScriptSupport.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\scrip\expressionscriptsupport.cs
++ExpressionScriptTemplate.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\scrip\expressionscripttemplate.cs
++ScriptMethods.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\scrip\scriptmethods.cs
++ScriptProvider.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\scrip\scriptprovider.cs
++ChangePwdView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\changepwdview.xaml.cs
++EditRemarksView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\editremarksview.xaml.cs
++HardwareConfigView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\hardwareconfigview.xaml.cs
++LoadingView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\loadingview.xaml.cs
++LoginView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\loginview.xaml.cs
++MessageBoxView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\messageboxview.xaml.cs
++VarLinkView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\views\varlinkview.xaml.cs
++right.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\common\right.png
++btnAddProject.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\btnaddproject.png
++btnDeleteProject.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\btndeleteproject.png
++Default.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\default.png
++Folder.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\folder.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\folder.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\folder.png
++function.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\function.png
++tsbSettingProject.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\tsbsettingproject.png
++调用执行流程.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\调用执行流程.png
++方法.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\方法.png
++主流程.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\主流程.png
++子流程.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\flow\子流程.png
++DeleteSol.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\deletesol.png
++Drive.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\drive.png
++File.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\file.png
++Folder Open.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\folder open.png
++Hard Drive.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\hard drive.png
++Sol.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\sol.png
++UpdateSol.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\solution\updatesol.png
++Blob.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\blob.png
++BuildLC.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\buildlc.png
++BuildLl.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\buildll.png
++BuildPL.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\buildpl.png
++BuildPp.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\buildpp.png
++Coordinate.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\coordinate.png
++CoordinateMap.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\coordinatemap.png
++DataCheck.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\datacheck.png
++Delay.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\delay.png
++else.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\else.png
++elseif.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\elseif.png
++end.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\end.png
++FitCircle.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\fitcircle.png
++FitLine.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\fitline.png
++forbid.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\forbid.png
++GrabImage.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\grabimage.png
++If.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\if.png
++IfStart.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\ifstart.png
++Matching.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\matching.png
++MeasureCalib.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\measurecalib.png
++MeasureCircle.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\measurecircle.png
++MeasureLine.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\measureline.png
++MeasureLines.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\measurelines.png
++Measuring.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\measuring.png
++MetricToolkit.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\metrictoolkit.png
++NPointCal.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\npointcal.png
++ParallelBlock.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\parallelblock.png
++ProjectOutput.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\projectoutput.png
++QRCode.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\qrcode.png
++ReceiveStr.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\receivestr.png
++recycleEnd.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\recycleend.png
++recycleStart.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\recyclestart.png
++RobotControl.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\robotcontrol.png
++SaveImage.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\saveimage.png
++SendStr.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\sendstr.png
++SingleAxisMove.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\singleaxismove.png
++SplitString.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\splitstring.png
++SplitText.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\splittext.png
++StopWhile.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\stopwhile.png
++SystemTime.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\systemtime.png
++Text.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\text.png
++TimeSlice.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\timeslice.png
++VarDefine.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\vardefine.png
++VarSet.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\varset.png
++While.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\tool\while.png
++tsbOpenSolution.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\toolbar\tsbopensolution.png
++tsbSaveSolution.png
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\images\toolbar\tsbsavesolution.png
++ViewOutputCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\commands\viewoutputcommanddefinition.cs
++ViewOutputCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\commands\viewoutputcommandhandler.cs
++IOutput.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\models\ioutput.cs
++IOutputView.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\models\ioutputview.cs
++OutputWriter.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\models\outputwriter.cs
++LogViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\viewmodels\logviewmodel.cs
++OutputViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\viewmodels\outputviewmodel.cs
++LogView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\views\logview.xaml
++OutputView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\views\outputview.xaml
++OpenPosManagerCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\commands\openposmanagercommanddefinition.cs
++OpenPosManagerCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\commands\openposmanagercommandhandler.cs
++PosManagerViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\viewmodels\posmanagerviewmodel.cs
++PosManagerView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\views\posmanagerview.xaml
++ViewRenderControlCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\commands\viewrendercontrolcommanddefinition.cs
++ViewRenderControlCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\commands\viewrendercontrolcommandhandler.cs
++RenderManager.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\managers\rendermanager.cs
++VisionViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\viewmodels\visionviewmodel.cs
++RenderView.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\renderview.cs
++RenderViewWpf.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\renderviewwpf.xaml
++VisionView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\visionview.xaml
++OpenParamSettingCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\commands\openparamsettingcommanddefinition.cs
++OpenParamSettingCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\commands\openparamsettingcommandhandler.cs
++SysConfig.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\models\sysconfig.cs
++SettingSysViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\viewmodels\settingsysviewmodel.cs
++SettingSysView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\views\settingsysview.xaml
++AddSolutionCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\addsolutioncommanddefinition.cs
++AddSolutionCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\addsolutioncommandhandler.cs
++CameraSetCommandDefinition.cs
++CameraSetCommandHandler.cs
++CommunicationSetCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\communicationsetcommanddefinition.cs
++CommunicationSetCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\communicationsetcommandhandler.cs
++GlobalVarCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\globalvarcommanddefinition.cs
++GlobalVarCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\globalvarcommandhandler.cs
++RunCycleCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\runcyclecommanddefinition.cs
++RunCycleCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\runcyclecommandhandler.cs
++RunOnceCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\runoncecommanddefinition.cs
++RunOnceCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\runoncecommandhandler.cs
++SaveFileCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\savefilecommandhandler.cs
++StopCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\stopcommanddefinition.cs
++StopCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\stopcommandhandler.cs
++UIDesignCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\uidesigncommanddefinition.cs
++UIDesignCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\uidesigncommandhandler.cs
++UserLoginCommandDefinition.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\userlogincommanddefinition.cs
++UserLoginCommandHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\commands\userlogincommandhandler.cs
++CanvasSetViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\viewmodels\canvassetviewmodel.cs
++GlobalVarViewModel.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\viewmodels\globalvarviewmodel.cs
++CanvasSetView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\views\canvassetview.xaml
++GlobalVarView.xaml
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\views\globalvarview.xaml
++DeviceStatusView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\views\devicestatusview.xaml.cs
++ToolOutputView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\tooloutput\views\tooloutputview.xaml.cs
++IDataCell.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\idatacell.cs
++MsgCell.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\msgcell.cs
++MsgTypeCell.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\msgtypecell.cs
++ReceiveDataEventArgs.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\receivedataeventargs.cs
++ReceiveDataEventHandler.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\receivedataeventhandler.cs
++SerHelper.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\serhelper.cs
++SocketState.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\socketstate.cs
++UdpLibrary.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\socket\udplibrary.cs
++HexTool.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\communacation\tool\hextool.cs
++Drive.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\drive.cs
++DriveNode.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\drivenode.cs
++File.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\file.cs
++FileNode.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\filenode.cs
++FileSystemObject.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\filesystemobject.cs
++FileSystemObjectNode.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\filesystemobjectnode.cs
++Folder.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\folder.cs
++FolderNode.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\models\sol\foldernode.cs
++.NETFramework,Version=v4.7.2.AssemblyAttributes.cs
++GeneratedInternalTypeHelper.g.cs
++GeneratedInternalTypeHelper.g.i.cs
++LogView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\views\logview.xaml.cs
++OutputView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\output\views\outputview.xaml.cs
++PosManagerView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\posmanager\views\posmanagerview.xaml.cs
++RenderView.Designer.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\renderview.designer.cs
++RenderView.resx
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\renderview.resx
++RenderViewWpf.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\renderviewwpf.xaml.cs
++VisionView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\rendercontrol\views\visionview.xaml.cs
++SettingSysView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\settings\views\settingsysview.xaml.cs
++CanvasSetView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\views\canvassetview.xaml.cs
++GlobalVarView.xaml.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\assets\modules\toolbar\views\globalvarview.xaml.cs
++ChangePwdView.g.cs
++ChangePwdView.g.i.cs
++EditRemarksView.g.cs
++EditRemarksView.g.i.cs
++HardwareConfigView.g.cs
++HardwareConfigView.g.i.cs
++LoadingView.g.cs
++LoadingView.g.i.cs
++LoginView.g.cs
++LoginView.g.i.cs
++MessageBoxView.g.cs
++MessageBoxView.g.i.cs
++VarLinkView.g.cs
++VarLinkView.g.i.cs
++PropertyGridStyle.g.i.cs
++CameraSetView.g.cs
++CameraSetView.g.i.cs
++CommunicationSetView.g.cs
++CommunicationSetView.g.i.cs
++AxisControlView.g.cs
++AxisControlView.g.i.cs
++DeviceManagerView.g.cs
++DeviceManagerView.g.i.cs
++FlowTreeView.g.cs
++FlowTreeView.g.i.cs
++PropertyPanelView.g.cs
++PropertyPanelView.g.i.cs
++RunManagerView.g.cs
++RunManagerView.g.i.cs
++LogView.g.cs
++LogView.g.i.cs
++OutputView.g.cs
++OutputView.g.i.cs
++PosManagerView.g.cs
++PosManagerView.g.i.cs
++RenderViewWpf.g.cs
++RenderViewWpf.g.i.cs
++VisionView.g.cs
++VisionView.g.i.cs
++SettingSysView.g.cs
++SettingSysView.g.i.cs
++CanvasSetView.g.cs
++CanvasSetView.g.i.cs
++GlobalVarView.g.cs
++GlobalVarView.g.i.cs
++ToolBoxView.g.cs
++ToolBoxView.g.i.cs
++DeviceStatusView.g.cs
++DeviceStatusView.g.i.cs
++ToolOutputView.g.cs
++ToolOutputView.g.i.cs
++SingleInstance.cs
i:{08c42464-e6ea-4010-a329-f7d4c6706d22}:c:\users\<USER>\desktop\视觉平台优化\moonlight.platform_0506\src\moonlight.core\common\helper\singleinstance.cs
++01 视觉类
i:{00000000-0000-0000-0000-000000000000}:01 视觉类
++NewFolder1
++02 运动类
i:{00000000-0000-0000-0000-000000000000}:02 运动类
++00 设备类
i:{00000000-0000-0000-0000-000000000000}:00 设备类
++00 核心类
i:{00000000-0000-0000-0000-000000000000}:00 核心类
++09 流程类
i:{00000000-0000-0000-0000-000000000000}:09 流程类
