// Updated by XamlIntelliSenseFileGenerator 2025/1/9 10:46:02
#pragma checksum "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "8EA0C33832205AFD593BB33CE3792C2EF61D0B790EFD8DB0977ED3B319A7AC7B"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using AvalonDock;
using AvalonDock.Controls;
using AvalonDock.Converters;
using AvalonDock.Layout;
using AvalonDock.Themes;
using MahApps.Metro;
using MahApps.Metro.Accessibility;
using MahApps.Metro.Actions;
using MahApps.Metro.Automation.Peers;
using MahApps.Metro.Behaviors;
using MahApps.Metro.Controls;
using MahApps.Metro.Controls.Dialogs;
using MahApps.Metro.Converters;
using MahApps.Metro.Markup;
using MahApps.Metro.Theming;
using MahApps.Metro.ValueBoxes;
using MoonLight.UI.Modules.Shell.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MoonLight.UI.Modules.Shell.Views
{


    /// <summary>
    /// CameraSetView
    /// </summary>
    public partial class CameraSetView : MahApps.Metro.Controls.MetroWindow, System.Windows.Markup.IComponentConnector
    {

#line default
#line hidden


#line 40 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border bd;

#line default
#line hidden


#line 50 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbCameraType;

#line default
#line hidden


#line 55 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbCameraNo;

#line default
#line hidden


#line 70 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dg;

#line default
#line hidden


#line 150 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Forms.Integration.WindowsFormsHost winFormHost;

#line default
#line hidden


#line 161 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;

#line default
#line hidden


#line 162 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnConfirm;

#line default
#line hidden

        private bool _contentLoaded;

        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent()
        {
            if (_contentLoaded)
            {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MoonLight;component/ui/modules/shell/views/camerasetview.xaml", System.UriKind.Relative);

#line 1 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);

#line default
#line hidden
        }

        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target)
        {
            switch (connectionId)
            {
                case 1:
                    this.window = ((MoonLight.UI.Modules.Shell.Views.CameraSetView)(target));

#line 20 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
                    this.window.Loaded += new System.Windows.RoutedEventHandler(this.window_Loaded);

#line default
#line hidden
                    return;
                case 2:
                    this.bd = ((System.Windows.Controls.Border)(target));
                    return;
                case 3:
                    this.cmbCameraType = ((System.Windows.Controls.ComboBox)(target));

#line 51 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
                    this.cmbCameraType.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbCameraType_SelectionChanged);

#line default
#line hidden
                    return;
                case 4:
                    this.cmbCameraNo = ((System.Windows.Controls.ComboBox)(target));
                    return;
                case 5:
                    this.dg = ((System.Windows.Controls.DataGrid)(target));

#line 80 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
                    this.dg.SelectedCellsChanged += new System.Windows.Controls.SelectedCellsChangedEventHandler(this.dg_SelectedCellsChanged);

#line default
#line hidden
                    return;
                case 6:
                    this.winFormHost = ((System.Windows.Forms.Integration.WindowsFormsHost)(target));
                    return;
                case 7:
                    this.btnCancel = ((System.Windows.Controls.Button)(target));

#line 161 "..\..\..\..\..\..\UI\Modules\Shell\Views\CameraSetView.xaml"
                    this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);

#line default
#line hidden
                    return;
                case 8:
                    this.btnConfirm = ((System.Windows.Controls.Button)(target));
                    return;
            }
            this._contentLoaded = true;
        }

        internal MahApps.Metro.Controls.MetroWindow window;
    }
}

