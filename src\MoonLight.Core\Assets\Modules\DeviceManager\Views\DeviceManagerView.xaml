﻿<mah:MetroWindow
    x:Class="MoonLight.Core.Assets.Modules.DeviceManager.Views.DeviceManagerView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:behaviors ="clr-namespace:MoonLight.UI.Framework.Behaviors;assembly=MoonLight"
    xmlns:convertor="clr-namespace:MoonLight.Core.Assets.Converter"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:devices="clr-namespace:MoonLight.Core.Devices"
    xmlns:ex="clr-namespace:MoonLight.Core.Assets.Extension"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewmodels="clr-namespace:MoonLight.Core.Modules"
    mc:Ignorable="d"
    d:DataContext="{d:DesignInstance Type=viewmodels:DeviceManagerViewModel}"
    WindowStartupLocation="CenterScreen"  >

    <mah:MetroWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MoonLight.Core;component/Assets/Collection.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <convertor:Bool2VisibilityConverter x:Key="BoolToVisibilityConverter" />
            <DataTemplate x:Key="DeviceItemTemplate">
                <Grid Height="30">
                    <!-- 显示模式 -->
                    <TextBlock VerticalAlignment="Center" Text="{Binding Name}" />
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="CategoryHeaderTemplate">
                <TextBlock
        FontSize="14"
        FontWeight="Bold"
        Text="{Binding CategoryName}" />
            </DataTemplate>

            <Style x:Key="DeviceListItemStyle" TargetType="ListBoxItem">
                <Setter Property="Padding" Value="2" />
                <Style.Triggers>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" />
                        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}" />
                    </Trigger>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.ControlLightBrushKey}}" />
                    </Trigger>
                </Style.Triggers>
            </Style>



            <!-- 工具箱组样式 -->
            <Style x:Key="ToolboxGroupStyle" TargetType="Expander">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Margin" Value="0,5,0,0" />
                <Setter Property="Padding" Value="5" />
                <Setter Property="IsExpanded" Value="True" />
            </Style>

            <!-- 工具箱项样式 -->
            <Style x:Key="ToolboxItemStyle" TargetType="ListBoxItem">
                <Setter Property="Padding" Value="5,3" />
                <Setter Property="Margin" Value="2,1" />
                <Setter Property="HorizontalContentAlignment" Value="Left" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="Background" Value="Transparent" />
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="#EEE" />
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- 工具箱项模板 -->
            <DataTemplate x:Key="ToolboxItemTemplate">
                <StackPanel Orientation="Horizontal">
                    <Image
            Width="16"
            Height="16"
            Margin="0,0,5,0"
            Source="{Binding IconPath}" />
                    <TextBlock VerticalAlignment="Center" Text="{Binding Name}" />
                </StackPanel>
            </DataTemplate>



            <!-- 工具箱组模板 -->
            <DataTemplate x:Key="ToolboxGroupTemplate">
                <Expander Header="{Binding GroupName}" Style="{StaticResource ToolboxGroupStyle}">
                    <ListBox
            Background="Transparent"
            BorderThickness="0"
            ItemsSource="{Binding Items}"
            ItemTemplate="{StaticResource ToolboxItemTemplate}">
                        <ListBox.ItemContainerStyle>
                            <Style BasedOn="{StaticResource ToolboxItemStyle}" TargetType="ListBoxItem">
                                <EventSetter Event="PreviewMouseLeftButtonDown" Handler="ToolboxItem_PreviewMouseLeftButtonDown" />
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                </Expander>
            </DataTemplate>
        </ResourceDictionary>
        

    </mah:MetroWindow.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200" />
            <!-- 工具箱 -->
            <ColumnDefinition Width="5" />
            <!-- 分割线 -->
            <ColumnDefinition Width="250" />
            <!-- 设备列表 -->
            <ColumnDefinition Width="5" />
            <!-- 分割线 -->
            <ColumnDefinition Width="*" />
            <!-- 配置区域 -->
        </Grid.ColumnDefinitions>



        <!-- 工具箱 -->
        <Border
            Grid.Column="0"
            BorderBrush="LightGray"
            BorderThickness="0,0,1,0">
            <DockPanel>
                <TextBlock
                    DockPanel.Dock="Top"
                    FontWeight="Bold"
                    Padding="5"
                    Text="设备工具箱" />
                <Border
                    Grid.Column="0"
                    BorderBrush="LightGray"
                    BorderThickness="0,0,1,0">
                    <ScrollViewer>
                        <ItemsControl
                            Margin="5"
                            ItemsSource="{Binding ToolboxGroups}"
                            ItemTemplate="{StaticResource ToolboxGroupTemplate}" />
                    </ScrollViewer>
                </Border>
            </DockPanel>
        </Border>

        <GridSplitter
            Grid.Column="1"
            Width="5"
            HorizontalAlignment="Stretch" />

        <!-- 设备列表 -->
        <Border
            Grid.Column="2"
            BorderBrush="LightGray"
            BorderThickness="0,0,1,0">
            <DockPanel
                AllowDrop="True"
                DragOver="DeviceList_DragOver"
                Drop="DeviceList_Drop">
                <ScrollViewer DockPanel.Dock="Top">
                    <ItemsControl ItemsSource="{Binding DeviceGroups}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <ContentControl Content="{Binding}" ContentTemplate="{StaticResource CategoryHeaderTemplate}" />
                                    <ListBox
                                        Margin="20,5,0,15"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        ItemContainerStyle="{StaticResource DeviceListItemStyle}"
                                        ItemsSource="{Binding Devices}"
                                        ItemTemplate="{StaticResource DeviceItemTemplate}"
                                        PreviewMouseLeftButtonDown="DeviceName_MouseLeftButtonDown"
                                        SelectedItem="{Binding DataContext.SelectedDevice, RelativeSource={RelativeSource AncestorType=mah:MetroWindow}}">
                                        <ListBox.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem Click="MenuItem_Click"
                                                    Command="{Binding DataContext.RemoveDeviceCommand, RelativeSource={RelativeSource AncestorType={x:Type mah:MetroWindow}},Mode=OneWay, TargetNullValue={x:Null}}"
                                                    CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ContextMenu}, Path=PlacementTarget.SelectedItem}"
                                                    Header="删除" />
                                            </ContextMenu>
                                        </ListBox.ContextMenu>

                                    </ListBox>
                                </StackPanel>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </DockPanel>
        </Border>

        <GridSplitter
            Grid.Column="3"
            Width="5"
            HorizontalAlignment="Stretch" />

        <!-- 设备配置 -->
        <DockPanel Grid.Column="4">
            <StackPanel
                Margin="5"
                DockPanel.Dock="Top"
                Orientation="Horizontal">
                <Button
                    Margin="2"
                    Command="{Binding SaveConfigurationCommand}"
                    Content="保存"
                    Padding="5" />
                <Button
                    Margin="2"
                    Command="{Binding LoadConfigurationCommand}"
                    Content="加载"
                    Padding="5" />
            </StackPanel>

            <Border
                BorderBrush="LightGray"
                BorderThickness="0,1,0,0"
                DockPanel.Dock="Top"
                Padding="10">
                <ContentControl Content="{Binding CurrentConfigurationUI}" />
            </Border>
        </DockPanel>
    </Grid>
</mah:MetroWindow>