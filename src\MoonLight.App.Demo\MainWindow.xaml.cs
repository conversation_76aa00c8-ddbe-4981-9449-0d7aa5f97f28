﻿using MahApps.Metro.Controls;
using MoonLight.Core.Enums;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Services;
using MoonLight.UI.Framework;
using MoonLight.UI.Framework.Services;
using RenderControl.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MoonLight.App.Demo
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : MetroWindow
    {
        public MainWindow()
        {
            InitializeComponent();
            this.DataContext = IoC.Get<MainWindowViewModel>();
     
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            Application.Current.Invoke(() =>
            {
                var view = (IoC.Get<IWindowManager>()).CreateUserControlAsync(typeof(IShell));
                IoC.Get<IShell>().ShowFloatingWindowsInTaskbar = false;
                IoC.Get<IShell>().ToolBars.Visible = true;
                vision.Content = view.Result;

            });

        }



        private void OnTabSelected(object sender, RoutedEventArgs e)
        {
            var tab = sender as TabItem;
            if (tab != null)
            {
                if (tab.Header.ToString() == "Demo UI")
                {
                    var RenderView = IoC.Get<IRenderViewManager>().GetView(0);
                    var parent = VisualTreeHelper.GetParent((RenderView as DependencyObject));
                    if (parent == null) return;
                    (parent as Grid).Children.Remove(RenderView as ContentControl);
                    VisionView.Content = RenderView;
                }
                else if (tab.Header.ToString() == "Vision UI")
                {
                    VisionView.Content = null;
                    IoC.Get<IRenderViewManager>().SetVieMode(ViewMode.One);
                }
            }
        }

        private void HalconTestButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var testWindow = new HalconSerializationTestWindow();
                testWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开Halcon序列化测试窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
