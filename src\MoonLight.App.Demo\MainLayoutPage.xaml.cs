﻿using MoonLight.UI.Framework.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace MoonLight.App.Demo
{
    /// <summary>
    /// MainLayoutPage.xaml 的交互逻辑
    /// </summary>
    public partial class MainLayoutPage : Page
    {
        public MainLayoutPage()
        {
            InitializeComponent();
        }

        private void NavigateToPage1_Click(object sender, RoutedEventArgs e)
        {
            var windowManager = IoC.Get<IWindowManager>();
            //var view = (IoC.Get<IWindowManager>()).ShowWindowAsync(typeof(IShell));
            windowManager.ShowFrameAsync(typeof(IShell),null, ContentFrame);
            //ContentFrame.Navigate
        }

        private void NavigateToPage2_Click(object sender, RoutedEventArgs e)
        {

        }

        private void NavigateToPage3_Click(object sender, RoutedEventArgs e)
        {

        }
    }
}
