﻿using MahApps.Metro.Controls;
using MoonLight.Core.Assets.Extension;
using MoonLight.Core.Devices;
using MoonLight.Core.Modules;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using MoonLight.Core.Models;

namespace MoonLight.Core.Assets.Modules.DeviceManager.Views
{
    /// <summary>
    /// DeviceManagerView.xaml 的交互逻辑
    /// </summary>
    public partial class DeviceManagerView : MetroWindow
    {
        public DeviceManagerView()
        {
            InitializeComponent();
            DataContext = new DeviceManagerViewModel();
        }

        private void DeviceName_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 1 && sender is FrameworkElement element)
            {
                var listBoxItem = FindVisualChild<ListBoxItem>(element);
                if (listBoxItem != null)
                {
                    var vm = DataContext as DeviceManagerViewModel;
                    //listBoxItem.SetValue(ListBoxItemExtensions.IsEditingProperty, true);
                    //e.Handled = true;
                    vm.SelectedDevice = listBoxItem.DataContext as IDevice;

                    // 延迟设置焦点以确保文本框已显示
                    Dispatcher.BeginInvoke(new System.Action(() =>
                    {
                        var textBox = FindVisualChild<TextBox>(listBoxItem);
                        textBox?.Focus();
                        textBox?.SelectAll();
                    }), System.Windows.Threading.DispatcherPriority.Render);
                }
            }
        }



        private static T FindParent<T>(DependencyObject child) where T : DependencyObject
        {
            var parent = VisualTreeHelper.GetParent(child);
            while (parent != null && !(parent is T))
            {
                parent = VisualTreeHelper.GetParent(parent);
            }
            return parent as T;
        }
        private static T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                {
                    return result;
                }
                else
                {
                    var descendant = FindVisualChild<T>(child);
                    if (descendant != null) return descendant;
                }
            }
            return null;
        }
        private void DeviceItem_Click(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is DeviceManagerViewModel vm && sender is ContentPresenter presenter)
            {
                vm.SelectedDevice = presenter.Content as IDevice;
            }
        }
        private void ToolboxItem_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is ListBoxItem item && item.DataContext is DeviceTemplate template)
            {
                var viewModel = (DeviceManagerViewModel)DataContext;
                viewModel.StartDragCommand.Execute(template);

                DragDrop.DoDragDrop(item, template, DragDropEffects.Copy);
                viewModel.DraggedItem = null;
            }
        }

        private void DeviceList_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(DeviceTemplate)))
            {
                e.Effects = DragDropEffects.Copy;
                e.Handled = true;
            }
        }

        private void DeviceList_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(typeof(DeviceTemplate)))
            {
                var template = (DeviceTemplate)e.Data.GetData(typeof(DeviceTemplate));
                var viewModel = (DeviceManagerViewModel)DataContext;
                viewModel.DropCommand.Execute(template.Category);
                e.Handled = true;
            }
        }

        private void ListBox_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is DeviceManagerViewModel vm && sender is ContentPresenter presenter)
            {
                vm.SelectedDevice = presenter.Content as IDevice;
            }
        }

        private void MenuItem_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is DeviceManagerViewModel vm)
            {
                if (vm.SelectedDevice!=null)
                {

                    MoonLight.Core.Modules.DeviceManager.Instance.RemoveDevice(vm.SelectedDevice.Id) ;
                }
            }

        }
    }
}
