﻿using HalconDotNet;
using MoonLight.Core.Common.Helper;
using MoonLight.Core.Communacation;
using MoonLight.Core.Config;
using MoonLight.Core.Devices.Communication;
using MoonLight.Core.Devices;
using MoonLight.Core.Enums;
using MoonLight.Core.Events;
using MoonLight.Core.Extension;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Core.ROIs;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading;
using System.Windows;
using System.Xaml.Permissions;
using MoonLight.Core.Modules;

namespace MoonLight.Core.Services
{
    [Serializable]
    public class Project : NotifyPropertyBase, IDisposable
    {
        #region Prop
        // 显示控件
        [NonSerialized]
        private IRenderView renderView;

        // 流程栏
        [NonSerialized]
        private IProcess process;

        //运行模式
        public RunMode RunMode { set; get; } = RunMode.None;

        // 运行图像
        [field:NonSerialized()]
        public HImage DispImage {  set; get; }

        // 工具列表
        public ObservableCollection<ToolUnit> ToolList { get; set; } = new ObservableCollection<ToolUnit>();

        // 执行完流程的ROI列表
        [NonSerialized]
        public ConcurrentDictionary<string, HRoi> HRois = new ConcurrentDictionary<string, HRoi>();

        [NonSerialized]
        public ConcurrentDictionary<string, object> Variables = new ConcurrentDictionary<string, object>();

        // 流程输出结果
        [NonSerialized]
        public ConcurrentDictionary<string, string> Outputs = new ConcurrentDictionary<string, string>();

        //输出容器 每个工具 都把自己的输出放到该容器中 第一个KEY是工具名 第二个KEY是变量名
        [NonSerialized]
        public Dictionary<string, Dictionary<string, VarModel>> OutputMap = new Dictionary<string, Dictionary<string, VarModel>>();

        public ObservableCollection<VaribleList> VaribleLists { get; set; } = new ObservableCollection<VaribleList>();

        protected bool _disposed = false;

        // 正在执行的工具名称
        private string _ExeToolName;
        public string ExeToolName
        {
            get { return _ExeToolName; }
            set { Set(ref _ExeToolName, value); }
        }

        //显示的窗口
        private int _DispViewID = 0;
        public int DispViewID
        {
            get { return _DispViewID; }
            set { _DispViewID = value; renderView = IoC.Get<IRenderViewManager>().GetView(DispViewID); }
        }

        // 线程控制
        [NonSerialized]
        private Thread m_Thread;

        // 控制流程
        [NonSerialized]
        private ManualResetEvent _runEvent = new ManualResetEvent(false);

        [NonSerialized]
        public AutoResetEvent Breakpoint = new AutoResetEvent(false);

        [NonSerialized]
        public bool ContinueRunFlag = false;

        [NonSerialized]
        public bool BreakpointFlag = false;

        // 线程状态 true执行中 false阻塞中
        [NonSerialized]
        private bool _ThreadStatus = false;
        public bool ThreadStatus
        {
            get { return _ThreadStatus; }
            set { Set(ref _ThreadStatus, value); Solution.Ins.SetIsEnable(); }
        }

        // tool树节点容器 不需要序列化
        [NonSerialized]
        private Dictionary<string, ToolNameTreeNode> _ToolTreeNodeMap;
        public Dictionary<string, ToolNameTreeNode> ToolTreeNodeMap
        {
            get
            {
                if (_ToolTreeNodeMap == null)
                {
                    _ToolTreeNodeMap = new Dictionary<string, ToolNameTreeNode>();
                }
                return _ToolTreeNodeMap;
            }
            set { _ToolTreeNodeMap = value; }
        }

        // tool容器
        [NonSerialized]
        private Dictionary<string, ToolUnit> _ToolDic;
        public Dictionary<string, ToolUnit> ToolDic
        {
            get
            {
                if (_ToolDic == null)
                {
                    _ToolDic = new Dictionary<string, ToolUnit>();
                }
                return _ToolDic;
            }
            set { _ToolDic = value; }
        }

        // tool 树形容器 临时组装 不需要序列化 每次增加和删除后 都要重新组装
        [NonSerialized]
        private ToolNameTreeNode _BaseTreeNode = new ToolNameTreeNode("");
        public ToolNameTreeNode BaseTreeNode
        {
            get
            {
                if (_BaseTreeNode == null)
                {
                    _BaseTreeNode = new ToolNameTreeNode("");
                }
                return _BaseTreeNode;
            }
            set { _BaseTreeNode = value; }
        }

        //项目信息
        private ProjectInfo _ProjectInfo;
        public ProjectInfo ProjectInfo
        {
            get
            {
                if (_ProjectInfo == null)
                {
                    _ProjectInfo = new ProjectInfo();
                }
                return _ProjectInfo;
            }
            set { _ProjectInfo = value; }
        }

        [NonSerialized]
        private int _ConsumingTime = 0;
        public int ConsumingTime
        {
            get { return _ConsumingTime; }
            set { Set(ref _ConsumingTime, value); }
        }

        /// <summary>
        /// 流程输出，需要“流程输出”工具触发
        /// </summary>
        /// <param name="data"></param>
        public delegate void ProjectOutputChangedHandler(ConcurrentDictionary<string, string> data);
        public event ProjectOutputChangedHandler ProjectOutputChangedEvent;
        public void ProjectOutputChanged(ConcurrentDictionary<string, string> data)
        {
            ProjectOutputChangedEvent?.Invoke(data);
        }

        public event EventHandler OnProjectFinishedEvent;
        public void RegisterProjectFinishedEvent(System.EventHandler action)
        {
            OnProjectFinishedEvent -= action;
            OnProjectFinishedEvent += action;
        }

        #endregion

        #region Ctor
        public Project()
        {
            process = IoC.Get<IProcess>();
            renderView = IoC.Get<IRenderViewManager>().GetView(DispViewID);
            m_Thread = new Thread(Process);
            m_Thread.IsBackground = true;
            m_Thread.Start();
            ToolList.CollectionChanged += ToolList_CollectionChanged;
        }

        public void ToolList_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {

            //VaribleLists = Variables2List();
            //ToolDic.Clear();
            //foreach (var item in ToolList)
            //{
            //    ToolDic.Add(item.Name, item);
            //}
        }
        #endregion

        #region Method
        [OnDeserialized()] //反序列化之后

        internal void OnDeserializedMethod(StreamingContext context)
        {
            process = IoC.Get<IProcess>();
            renderView = IoC.Get<IRenderViewManager>().GetView(DispViewID);
            ProjectInfo.IconImage = ImageTool.GetProcessImageByName(ProjectInfo.ProjectType, ProjectInfo.ProjectRunMode);
            HRois = new ConcurrentDictionary<string, HRoi>();
            OutputMap = new Dictionary<string, Dictionary<string, VarModel>>();
            Outputs = new ConcurrentDictionary<string, string>();
            Variables = new ConcurrentDictionary<string, object>();
            Breakpoint = new AutoResetEvent(false);
            m_Thread = new Thread(Process);
            m_Thread.IsBackground = true;
            m_Thread.Start();
            //ToolList.CollectionChanged += ToolList_CollectionChanged;
            ToolList_CollectionChanged(null, null);
            _runEvent = new ManualResetEvent(false);
        }

        public void Start()
        {
            if (m_Thread == null)
            {
                m_Thread = new Thread(Process);
                m_Thread.IsBackground = true;
                m_Thread.Start();
            }

            List<ToolUnit> tools = new List<ToolUnit>();
            GetTools(ToolList.ToList(), ref tools);
            foreach (var item in tools)
            {
                item.CancelWait = false;
                item.FirstRunFlag = true;
            }

            if (ThreadStatus == true) return;
            foreach (var item in ToolList)
            {
                item.FirstRunFlag = true;
            }
            ThreadStatus = true;
            _runEvent.Set();
        }

        public void Stop()
        {
            ThreadStatus = false;
            var manager = IoC.Get<IDeviceManager>();// MoonLight.Modules.DeviceManager.Models.DeviceManager.Instance;
            if (manager != null)
            {
                var networkAndSerialDevices = manager.DeviceGroups
                    .Where(g => g.Category == DeviceCategory.NetworkDevice ||
                                g.Category == DeviceCategory.SerialDevice)
                    .SelectMany(g => g.Devices);
                foreach (var item in networkAndSerialDevices)
                {
                    try
                    {
                        if ((item as CommunicationBase).IsConnected)
                        {
                            (item as CommunicationBase).StopRecStrSignal();
                        }
                    }
                    catch { }
                }
            }
            List<ToolUnit> tools = new List<ToolUnit>();
            GetTools(ToolList.ToList(), ref tools);
            foreach (var item in tools)
            {
                item.CancelWait = true;
            }
            ContinueRunFlag = false;
            BreakpointFlag = false;
            Breakpoint.Set();
            _runEvent.Reset();
        }

  
        public string GetOutputDataByName(string name)
        {
            if(name == null)
            {
                return null;
            }
            if(Outputs.ContainsKey(name))
            {
                return Outputs[name];
            }
            else
            {
                return null;
            }
        }

        public bool GetThreadStatus()
        {
            return ThreadStatus;
        }


        public List<string> GetToolNameList()
        {
            return ToolList.Select(c => c.Name).ToList();
        }

        public void GetToolNameList(List<ToolUnit> list, ref List<string> result)
        {

            foreach (var item in list)
            {
                result.Add(item.Name);

                GetToolNameList(item.ToolList.ToList(), ref result);
            }
        }

        public ToolUnit GetToolByName(string name)
        {
            foreach (var item in ToolList)
            {
                if (item.Name == name)
                {
                    return item;
                }
                var tool = GetToolByName(item.ToolList.ToList(), name);
                if (tool != null)
                {
                    return tool;
                }
            }
            return null;
        }

        public ToolUnit GetToolByName(List<ToolUnit> list, string name)
        {
            foreach (var item in list)
            {
                if (item.Name == name)
                {
                    return item;
                }
                var tool = GetToolByName(item.ToolList.ToList(), name);
                if (tool != null)
                {
                    return tool;
                }
            }
            return null;
        }

        public void GetTools(List<ToolUnit> list, ref List<ToolUnit> tools)
        {
            foreach (var item in list)
            {
                tools.Add(item);

                GetTools(item.ToolList.ToList(), ref tools);
            }
        }

        public void SetTools(List<ToolUnit> tools)
        {
            ToolList.Clear();
            foreach(var item in tools)
            {
                if(item.Parent != null)
                {
                    
                }
                else
                {
                    ToolList.Add(item);
                }    
            }
        }

        public int GetToolIndexByName(List<ToolUnit> list, string name, ref int startIndex)
        {
            foreach (var item in list)
            {

                if (item.Name == name)
                {
                    return startIndex;
                }
                var index = GetToolIndexByName(item.ToolList.ToList(), name, ref startIndex);
                if (index != -1)
                {
                    return index;
                }
                startIndex++;
            }
            return -1;
        }

        public int GetToolIndexByName(string name)
        {
            return ToolList.FindIndex(c => c.Name == name);
        }
    
        public void RecoverToolObj(ToolUnit backToolObjBase)
        {
            var tool = GetToolByName(ToolList.ToList(), backToolObjBase.Name);
            tool = backToolObjBase;
            //int index = ToolList.FindIndex(c => c.Name == backToolObjBase.Name);
            //ToolList[index] = backToolObjBase;
        }

        public void AddOutputParam(ToolUnit tool, string varName, string varType, object obj)
        {
            if (!OutputMap.ContainsKey(tool.Name))
            {
                OutputMap[tool.Name] = new Dictionary<string, VarModel>();
            }
            Dictionary<string, VarModel> dic = OutputMap[tool.Name];
            if (!dic.ContainsKey(varName))
            {
                dic.Add(varName, new VarModel() { ToolUnit = tool, DataType = varType, Name = varName, Value = obj });
            }
            else
            {
                if (obj is RImage)
                {
                    dic[varName].Value = (RImage)obj;
                }
                else if (obj is HImage)
                {
                    dic[varName].Value = (HImage)obj;
                }
                else if (obj is HRegion)
                {
                    dic[varName].Value = (HRegion)obj;
                }
                else
                {
                    dic[varName].Value = obj;
                }
            }
            string key = $"{this.ProjectInfo.ProcessName}.{tool.Name}.{varName}";
            Variables.AddOrUpdate(key, obj, (oldkey, oldvalue) => oldvalue);

        }
   
        public VarModel GetParamByName(string linkName)
        {
            if (string.IsNullOrEmpty(linkName)) return null;
            string[] arr = linkName.Split('.');



            if (arr.Length == 2)
            {
                string toolName = arr[0].Substring(1);
                string varName = arr[1];
                if (toolName == "全局变量")
                {
                    var data = Solution.Ins.SysVar.Where(o => o.Name == varName).FirstOrDefault();
                    return data;
                }
                else
                {
                    if (OutputMap.ContainsKey(toolName))
                    {
                        Dictionary<string, VarModel> dic = OutputMap[toolName];
                        if (dic.ContainsKey(varName))
                        {
                            return dic[varName];
                        }
                    }
                }
            }
            return null;
        }

        public bool SetParamByName(string linkName, VarModel data)
        {
            if (string.IsNullOrEmpty(linkName)) return false;
            string[] arr = linkName.Split('.');
            if (arr.Length == 2)
            {
                string toolName = arr[0].Substring(1);
                string varName = arr[1];
                if (toolName == "全局变量")
                {
                    for (int i = 0; i < Solution.Ins.SysVar.Count; i++)
                    {
                        if (Solution.Ins.SysVar[i].Name == varName)
                        {
                            Solution.Ins.SysVar[i].Value = data.Value;
                            return true;
                        }
                    }
                    return false;
                }
                else
                {
                    if (OutputMap.ContainsKey(toolName))
                    {
                        Dictionary<string, VarModel> dic = OutputMap[toolName];

                        if (dic.ContainsKey(varName))
                        {
                            //return dic[varName];
                            dic[varName].Value = data.Value;
                        }
                    }
                }
            }
            return false;
        }

        private void Process()
        {
            while (true)
            {
                if (_runEvent == null) _runEvent = new ManualResetEvent(false);
                var event_run = _runEvent.WaitOne(0);
                if (event_run)
                {
                    Execute();
                  
                    if (RunMode == RunMode.RunOnce)
                    {
                        ThreadStatus = false;
                        _runEvent.Reset();
                    }
                }
                Thread.Sleep(10);
            }
        }

        public void ExcuteOneToolByName(string toolName)
        {
            ToolUnit tool = GetToolByName(ToolList.ToList(), toolName);
            if (tool == null) return;
            tool.InternalRun();
        }

        public void CloseToolByName(string toolName)
        {
            var tool = ToolList.Where(o => o.Name == toolName).FirstOrDefault();
            if (tool == null) return;
            tool.CloseView();
        }

        public void ExcuteMultiToolByName(string toolName)
        {
            ThreadStatus = true;
            Execute(toolName);
            ThreadStatus = false;
        }

        private void ExecuteTool(ToolUnit tool)
        {
            if (tool.IsEnableBreakPoint)
            {
                Breakpoint.Reset();
                BreakpointFlag = true;
                ContinueRunFlag = false;
            }

            if (BreakpointFlag && !ContinueRunFlag)
            {
                Breakpoint.WaitOne();
            }

            tool.InternalRun();
            PublishToolEventIfCurrentProject(tool);
        }

        private void PublishToolEventIfCurrentProject(ToolUnit tool)
        {
            if (!Solution.Ins.QuickMode && this.Equals(Solution.Ins.CurrentProject))
            {
                EventMgrLib.EventMgr.Ins.GetEvent<ToolOutChangedEvent>().Publish();
            }
        }

        [NonSerialized]
        Stopwatch sw = null;
        // 执行 未修改！！！！！！！！
        public void Execute(string toolName = "")
        {

            try
            {
                if (sw == null)
                    sw = new Stopwatch();
                sw.Restart();

                int index = 0;
                if (!string.IsNullOrEmpty(toolName))
                {
                    index = ToolList.FindIndex(item => item.Name.Equals(toolName));
                }
                
                for (; index < ToolList.Count; index++)
                {
                    var event_run = _runEvent.WaitOne(0);
                    if (!event_run)
                    {
                        sw.Stop();
                        CommonMethods.UISync(() =>
                        {
                            ConsumingTime = (int)(sw.ElapsedMilliseconds);
                        });
                        return;
                    }

                    ToolList[index].ViewerId = DispViewID;
                    ExecuteTool(ToolList[index]);
                    ExeToolName = ToolList[index].Name;
                }
                sw.Stop();
                OnProjectFinishedEvent?.Invoke(this.ProjectInfo.ProjectName, null); //通知project运行完成

                CommonMethods.UISync(() =>
                {
                    ConsumingTime = (int)(sw.ElapsedMilliseconds);
                });
            }
            catch (Exception ex)
            {


            }
        }


        #endregion


        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this); // 避免重复调用析构
            GC.Collect();
        }

        public void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    foreach (var item in ToolList)
                    {
                        item.Dispose();
                    }

                    ToolList.Clear();
                    HRois.Clear();
                    Variables.Clear();
                    Outputs.Clear();
                    OutputMap.Clear();

                    ToolList = null;
                    HRois = null;
                    Variables = null;
                    Outputs = null;
                    OutputMap = null;
                }

                _disposed = true;
            }
        }


    }
    public class ToolNameTreeNode // 用于项目执行顺序控制
    {
        public ToolNameTreeNode Parent = null;
        public string Name;
        public List<string> ChildList = new List<string>();

        public ToolNameTreeNode(string name)
        {
            Name = name;
        }
    };
}
