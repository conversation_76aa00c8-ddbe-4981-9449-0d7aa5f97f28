﻿using MoonLight.Core.Common.Log;
using MoonLight.Core.Enums;
using MoonLight.Core.Models;
using MoonLight.Modules.Output.ViewModels;
using System;
using System.Windows.Controls;
using System.Windows.Media;

namespace MoonLight.Modules.Output.Views
{
    /// <summary>
    /// LogView.xaml 的交互逻辑
    /// </summary>
    public partial class LogView : UserControl
    {
        public LogView()
        {
            InitializeComponent();
            this.DataContext = LogViewModel.Ins;
            Logger.OnAddLogEvent += Logger_OnAddLogEvent;
        }

        private void Logger_OnAddLogEvent(object sender, EventArgs e)
        {
            while (Logger.LogInfos.Count > 0)
            {
                LogModel logModel = new LogModel();
                if (Logger.LogInfos.TryDequeue(out logModel))
                {
                    if (LogViewModel.Ins.DisplayLogCollection.Count >= MaxCountDisplayLogCollection)
                    {
                        LogViewModel.Ins.DisplayLogCollection.RemoveAt(0);
                    }
                    if (LogViewModel.Ins.AllLogCollection.Count >= MaxCountAllLogCollection)
                    {
                        LogViewModel.Ins.AllLogCollection.RemoveAt(0);
                    }
                    if (LogViewModel.Ins.InfoCollection.Count >= MaxCountInfoCollection)
                    {
                        LogViewModel.Ins.InfoCollection.RemoveAt(0);
                    }
                    if (LogViewModel.Ins.WarnCollection.Count >= MaxCountWarnCollection)
                    {
                        LogViewModel.Ins.WarnCollection.RemoveAt(0);
                    }
                    if (LogViewModel.Ins.ErrorCollection.Count >= MaxCountErrorCollection)
                    {
                        LogViewModel.Ins.ErrorCollection.RemoveAt(0);
                    }
                    if (LogViewModel.Ins.AlarmCollection.Count >= MaxCountAlarmCollection)
                    {
                        LogViewModel.Ins.AlarmCollection.RemoveAt(0);
                    }
                    switch (logModel.LogType)
                    {
                        case MsgType.Success:
                        case MsgType.Info:
                            logModel.LogColor = Brushes.Lime;
                            LogViewModel.Ins.InfoCollection.Add(logModel);
                            if (LogViewModel.Ins.InfoFilter)
                            {
                                LogViewModel.Ins.DisplayLogCollection.Add(logModel);
                            }
                            break;
                        case MsgType.Warn:
                            logModel.LogColor = Brushes.Yellow;
                            LogViewModel.Ins.WarnCollection.Add(logModel);
                            if (LogViewModel.Ins.WarnFilter)
                            {
                                LogViewModel.Ins.DisplayLogCollection.Add(logModel);
                            }
                            break;
                        case MsgType.Error:
                            logModel.LogColor = Brushes.Red;
                            LogViewModel.Ins.ErrorCollection.Add(logModel);
                            if (LogViewModel.Ins.ErrorFilter)
                            {
                                LogViewModel.Ins.DisplayLogCollection.Add(logModel);
                            }
                            break;
                        case MsgType.Alarm:
                            logModel.LogColor = Brushes.Red;
                            LogViewModel.Ins.AlarmCollection.Add(logModel);
                            if (LogViewModel.Ins.AlarmFilter)
                            {
                                LogViewModel.Ins.DisplayLogCollection.Add(logModel);
                            }
                            CommonMethods.Mach.AlmFlag = true;
                            break;
                    }
                    if (!LogViewModel.Ins.InfoFilter && !LogViewModel.Ins.WarnFilter && !LogViewModel.Ins.ErrorFilter && !LogViewModel.Ins.AlarmFilter)
                    {
                        CommonMethods.UIAsync(() =>
                        {
                            LogViewModel.Ins.DisplayLogCollection.Add(logModel);
                            dg.ScrollIntoView(logModel);
                        });

                    }
                    LogViewModel.Ins.AllLogCollection.Add(logModel);
                    LogViewModel.Ins.InfoCount = LogViewModel.Ins.InfoCollection.Count;
                    LogViewModel.Ins.WarnCount = LogViewModel.Ins.WarnCollection.Count;
                    LogViewModel.Ins.ErrorCount = LogViewModel.Ins.ErrorCollection.Count;
                    LogViewModel.Ins.AlarmCount = LogViewModel.Ins.AlarmCollection.Count;
                }
            }
        }

        #region Prop
        private const int MaxCountDisplayLogCollection = 1999;
        private const int MaxCountAllLogCollection = 1999;
        private const int MaxCountInfoCollection = 1999;
        private const int MaxCountWarnCollection = 1999;
        private const int MaxCountErrorCollection = 1999;
        private const int MaxCountAlarmCollection = 1999;

        #endregion


    }
}
