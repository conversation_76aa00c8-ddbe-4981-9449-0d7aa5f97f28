﻿using HalconDotNet;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MoonLight.Core.Common.Log;
using MoonLight.Core.Enums;

namespace MoonLight.Core.Common.Helper
{
    public static class JsonSerializerHelper
    {
        private static readonly JsonSerializerSettings settings;

        static JsonSerializerHelper()
        {
            settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter>
          {
              new HObjectConverter(),
              new HTupleConverter(),
              new HShapeModelConverter(),
              new HRegionConverter(),
              new HImageConverter()
          },
                // Optional: For handling derived types if you have class hierarchies
                // TypeNameHandling = TypeNameHandling.Auto,
                Formatting = Formatting.Indented, // For readable JSON
                NullValueHandling = NullValueHandling.Include // or Ignore, as needed
            };
        }

        /// <summary>
        /// Serializes an object (which may contain Halcon variables) to a JSON string.
        /// </summary>
        public static string Serialize(object obj)
        {
            if (obj == null) return null;
            return JsonConvert.SerializeObject(obj, settings);
        }

        /// <summary>
        /// Deserializes a JSON string to an object of type T (which may contain Halcon variables).
        /// </summary>
        public static T Deserialize<T>(string json)
        {
            if (string.IsNullOrEmpty(json)) return default(T);
            return JsonConvert.DeserializeObject<T>(json, settings);
        }

        // Specific methods if you only want to serialize/deserialize standalone Halcon types
        public static string SerializeHalconObject(HObject hObject)
        {
            return JsonConvert.SerializeObject(hObject, settings);
        }

        public static HObject DeserializeHalconObject(string json)
        {
            return JsonConvert.DeserializeObject<HObject>(json, settings);
        }

        public static string SerializeHalconTuple(HTuple hTuple)
        {
            return JsonConvert.SerializeObject(hTuple, settings);
        }

        public static HTuple DeserializeHalconTuple(string json)
        {
            return JsonConvert.DeserializeObject<HTuple>(json, settings);
        }
    }







    public class HObjectConverter : JsonConverter<HObject>
    {
        public override void WriteJson(JsonWriter writer, HObject value, JsonSerializer serializer)
        {
            if (value == null || !value.IsInitialized())
            {
                writer.WriteNull();
                return;
            }

            try
            {
                // Serialize HObject to byte array
                HSerializedItem serializedItem = value.SerializeObject();
                // Convert byte array to Base64 string
                // Note: HSerializedItem can be directly cast to byte[] if needed,
                // but using its CopyBytes method is safer.
                // However, for simplicity with Newtonsoft, we'll serialize the HSerializedItem itself
                // if it's directly serializable by Newtonsoft (it's not simple).
                // So, we extract the bytes.
                // A more direct way to get bytes from HSerializedItem is not obvious,
                // so we might need to write it to a memory stream and read back,
                // or use an intermediate HTuple if SerializeObject returns a handle.

                // Let's assume SerializeObject gives us something we can work with.
                // The typical way is to get a pointer and size, or it returns an HTuple handle.
                // For robust serialization, we should use HALCON's mechanism to get raw bytes.
                // A common pattern is to write it to a tuple, then get the tuple's byte representation.

                // Simpler approach: HSerializedItem itself is a wrapper.
                // We need to get the actual byte data.
                // HSerializedItem doesn't directly expose a byte[].
                // Let's use a temporary file or memory stream approach if direct bytes are hard.
                // Or, more practically, HALCON's `write_object` can write to memory.

                // The most straightforward way with HSerializedItem:
                // It's a bit convoluted, but HSerializedItem is designed to be passed to DeserializeObject.
                // To get it into JSON, we need its byte representation.
                // HSerializedItem itself is not directly convertible to byte[].
                // Let's use a trick: create a tuple from the serialized item, then serialize the tuple.
                // This is indirect.

                // A more direct (but potentially less safe if not handled carefully) way:
                // HSerializedItem item = value.SerializeObject();
                // HTuple pointerTuple = item.GetHandle(); // This gets the internal handle
                // IntPtr pointer = pointerTuple[0].IP;
                // int size = pointerTuple[1].I;
                // byte[] data = new byte[size];
                // System.Runtime.InteropServices.Marshal.Copy(pointer, data, 0, size);
                // item.Dispose(); // IMPORTANT!

                // SAFER & RECOMMENDED HALCON WAY for byte array:
                HSerializedItem item = value.SerializeObject();
                HTuple tupleRepresentation = item.ConvertToTuple(); // Converts to a tuple containing the data
                item.Dispose(); // Dispose the HSerializedItem

                // Now, serialize this tuple representation.
                // This tuple contains the actual data needed for deserialization.
                // We can serialize this HTuple using our HTupleConverter.
                // Or, if we want raw bytes:
                // HTuple rawBytesTuple = tupleRepresentation.TupleSerialize(); // This is for HTuple itself
                // This is getting complex. Let's simplify.

                // The most robust way to get bytes for an HObject:
                // 1. Serialize HObject to HSerializedItem
                // 2. Create an HTuple from this HSerializedItem
                // 3. Serialize this HTuple (which now contains the HObject data) using HTuple.SerializeTuple()

                // Let's assume HObject.SerializeObject() gives us an HSerializedItem,
                // and we want to store the *content* of this item.
                // The HSerializedItem is a handle. To make it transportable, we need its data.

                // Simplest conceptual model: HObject -> byte[] -> Base64
                // How to get byte[] from HObject reliably?
                // Use Halcon's serialization mechanism
                HSerializedItem serializedItem = value.SerializeObject();

                // Convert HSerializedItem to HTuple for serialization
                HTuple serializedTuple = serializedItem.ConvertToTuple();
                serializedItem.Dispose();

                // Serialize the tuple to bytes
                byte[] data = serializedTuple.SerializeTuple();
                writer.WriteValue(Convert.ToBase64String(data));
            }
            catch (Exception ex)
            {
                // Log error or handle
                writer.WriteNull(); // Or throw, depending on desired behavior
                Console.WriteLine($"Error serializing HObject: {ex.Message}");
            }
        }

        public override HObject ReadJson(JsonReader reader, Type objectType, HObject existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return null;
            }

            try
            {
                string base64Data = (string)reader.Value;
                if (string.IsNullOrEmpty(base64Data))
                    return null;

                byte[] data = Convert.FromBase64String(base64Data);

                // Deserialize the HTuple first
                HTuple tempTuple = new HTuple();
                tempTuple.DeserializeTuple(data);

                // Convert HTuple back to HSerializedItem
                HSerializedItem serializedItem = new HSerializedItem();
                serializedItem.ConvertFromTuple(tempTuple);

                // Deserialize the HObject
                HObject obj = new HObject();
                obj.DeserializeObject(serializedItem);
                serializedItem.Dispose(); // Dispose the HSerializedItem
                return obj;
            }
            catch (Exception ex)
            {
                // Log error or handle
                Console.WriteLine($"Error deserializing HObject: {ex.Message}");
                return null; // Or throw
            }
        }
    }




    public class HTupleConverter : JsonConverter<HTuple>
    {
        public override void WriteJson(JsonWriter writer, HTuple value, JsonSerializer serializer)
        {
            if (value == null) // HTuple can be null
            {
                writer.WriteNull();
                return;
            }

            try
            {
                // Serialize HTuple to byte array
                byte[] data = value.SerializeTuple();
                // Convert byte array to Base64 string
                writer.WriteValue(Convert.ToBase64String(data));
            }
            catch (Exception ex)
            {
                writer.WriteNull();
                Console.WriteLine($"Error serializing HTuple: {ex.Message}");
            }
        }

        public override HTuple ReadJson(JsonReader reader, Type objectType, HTuple existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return null;
            }

            try
            {
                string base64Data = (string)reader.Value;
                if (string.IsNullOrEmpty(base64Data))
                    return null;

                byte[] data = Convert.FromBase64String(base64Data);

                HTuple tuple = new HTuple();
                tuple.DeserializeTuple(data);
                return tuple;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deserializing HTuple: {ex.Message}");
                return null;
            }
        }

    }




    public class HShapeModelConverter : JsonConverter<HShapeModel>
    {
        public override void WriteJson(JsonWriter writer, HShapeModel value, JsonSerializer serializer)
        {
            if (value == null || !value.IsInitialized()) // HShapeModel is a struct-like class, check IsInitialized
            {
                writer.WriteNull();
                return;
            }

            try
            {
                HOperatorSet.SerializeShapeModel(value, out HSerializedItem serializedItem);
                // Similar to HObjectConverter, we need to get bytes from HSerializedItem
                HTuple tempTuple = serializedItem.ConvertToTuple(); // Convert HSerializedItem to tuple
                byte[] data = tempTuple.SerializeTuple();
                serializedItem.Dispose();
                writer.WriteValue(Convert.ToBase64String(data));
            }
            catch (Exception ex)
            {
                writer.WriteNull();
                Console.WriteLine($"Error serializing HShapeModel: {ex.Message}");
            }
        }

        public override HShapeModel ReadJson(JsonReader reader, Type objectType, HShapeModel existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return new HShapeModel(); // Or null if HShapeModel could be nullable in your context
            }

            try
            {
                string base64Data = (string)reader.Value;
                if (string.IsNullOrEmpty(base64Data))
                    return new HShapeModel();

                byte[] data = Convert.FromBase64String(base64Data);
                HTuple tempTuple = new HTuple();
                tempTuple.DeserializeTuple(data);

                // Convert HTuple back to HSerializedItem
                HSerializedItem serializedItem = new HSerializedItem();
                serializedItem.ConvertFromTuple(tempTuple);

                HOperatorSet.DeserializeShapeModel(serializedItem, out HShapeModel model);
                serializedItem.Dispose();
                return model;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deserializing HShapeModel: {ex.Message}");
                return new HShapeModel(); // Or throw
            }
        }
    }

    public class HRegionConverter : JsonConverter<HRegion>
    {
        public override void WriteJson(JsonWriter writer, HRegion value, JsonSerializer serializer)
        {
            if (value == null || !value.IsInitialized())
            {
                writer.WriteNull();
                return;
            }

            try
            {
                // Serialize HRegion using HObject serialization
                HObject regionAsObject = new HObject(value);
                HSerializedItem serializedItem = regionAsObject.SerializeObject();

                HTuple serializedTuple = serializedItem.ConvertToTuple();
                serializedItem.Dispose();

                byte[] data = serializedTuple.SerializeTuple();
                writer.WriteValue(Convert.ToBase64String(data));
            }
            catch (Exception ex)
            {
                writer.WriteNull();
                Console.WriteLine($"Error serializing HRegion: {ex.Message}");
            }
        }

        public override HRegion ReadJson(JsonReader reader, Type objectType, HRegion existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return new HRegion();
            }

            try
            {
                string base64Data = (string)reader.Value;
                if (string.IsNullOrEmpty(base64Data))
                    return new HRegion();

                byte[] data = Convert.FromBase64String(base64Data);
                HTuple tempTuple = new HTuple();
                tempTuple.DeserializeTuple(data);

                HSerializedItem serializedItem = new HSerializedItem();
                serializedItem.ConvertFromTuple(tempTuple);

                HObject obj = new HObject();
                obj.DeserializeObject(serializedItem);
                serializedItem.Dispose();

                // Convert HObject back to HRegion
                return new HRegion(obj);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deserializing HRegion: {ex.Message}");
                return new HRegion();
            }
        }
    }

    public class HImageConverter : JsonConverter<HImage>
    {
        public override void WriteJson(JsonWriter writer, HImage value, JsonSerializer serializer)
        {
            if (value == null || !value.IsInitialized())
            {
                writer.WriteNull();
                return;
            }

            try
            {
                // Serialize HImage using HObject serialization
                HObject imageAsObject = new HObject(value);
                HSerializedItem serializedItem = imageAsObject.SerializeObject();

                HTuple serializedTuple = serializedItem.ConvertToTuple();
                serializedItem.Dispose();

                byte[] data = serializedTuple.SerializeTuple();
                writer.WriteValue(Convert.ToBase64String(data));
            }
            catch (Exception ex)
            {
                writer.WriteNull();
                Console.WriteLine($"Error serializing HImage: {ex.Message}");
            }
        }

        public override HImage ReadJson(JsonReader reader, Type objectType, HImage existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return new HImage();
            }

            try
            {
                string base64Data = (string)reader.Value;
                if (string.IsNullOrEmpty(base64Data))
                    return new HImage();

                byte[] data = Convert.FromBase64String(base64Data);
                HTuple tempTuple = new HTuple();
                tempTuple.DeserializeTuple(data);

                HSerializedItem serializedItem = new HSerializedItem();
                serializedItem.ConvertFromTuple(tempTuple);

                HObject obj = new HObject();
                obj.DeserializeObject(serializedItem);
                serializedItem.Dispose();

                // Convert HObject back to HImage
                return new HImage(obj);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deserializing HImage: {ex.Message}");
                return new HImage();
            }
        }
    }

}
