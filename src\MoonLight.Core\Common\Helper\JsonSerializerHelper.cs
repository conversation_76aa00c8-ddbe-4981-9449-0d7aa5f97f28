﻿using HalconDotNet;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MoonLight.Core.Common.Log;
using MoonLight.Core.Enums;

namespace MoonLight.Core.Common.Helper
{
    /// <summary>
    /// JSON序列化帮助类，专门支持Halcon对象的序列化和反序列化
    /// </summary>
    public static class JsonSerializerHelper
    {
        #region 配置选项

        /// <summary>
        /// Halcon序列化配置选项
        /// </summary>
        public class HalconSerializationOptions
        {
            /// <summary>
            /// 是否启用压缩（对于大型图像数据）
            /// </summary>
            public bool EnableCompression { get; set; } = true;

            /// <summary>
            /// 是否格式化JSON输出
            /// </summary>
            public bool FormatJson { get; set; } = false;

            /// <summary>
            /// 图像序列化格式
            /// </summary>
            public string ImageFormat { get; set; } = "png";

            /// <summary>
            /// 最大图像尺寸限制（像素）
            /// </summary>
            public int MaxImageSize { get; set; } = 4096 * 4096;

            /// <summary>
            /// 是否包含类型信息
            /// </summary>
            public bool IncludeTypeInfo { get; set; } = true;
        }

        /// <summary>
        /// 默认序列化配置
        /// </summary>
        public static HalconSerializationOptions DefaultOptions { get; } = new HalconSerializationOptions();

        #endregion

        #region 核心序列化方法

        /// <summary>
        /// 序列化Halcon对象为JSON字符串
        /// </summary>
        /// <typeparam name="T">Halcon对象类型</typeparam>
        /// <param name="halconObject">要序列化的Halcon对象</param>
        /// <param name="options">序列化选项</param>
        /// <returns>JSON字符串</returns>
        public static string SerializeHalconObject<T>(T halconObject, HalconSerializationOptions options = null) where T : class
        {
            try
            {
                if (halconObject == null)
                    return null;

                options = options ?? DefaultOptions;

                var settings = CreateJsonSettings(options);
                return JsonConvert.SerializeObject(halconObject, settings);
            }
            catch (Exception ex)
            {
                Logger.AddLog($"序列化Halcon对象失败: {ex.Message}", MsgType.Error);
                throw new InvalidOperationException($"序列化Halcon对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从JSON字符串反序列化Halcon对象
        /// </summary>
        /// <typeparam name="T">Halcon对象类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <param name="options">序列化选项</param>
        /// <returns>反序列化的Halcon对象</returns>
        public static T DeserializeHalconObject<T>(string json, HalconSerializationOptions options = null) where T : class
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return null;

                options = options ?? DefaultOptions;

                var settings = CreateJsonSettings(options);
                return JsonConvert.DeserializeObject<T>(json, settings);
            }
            catch (Exception ex)
            {
                Logger.AddLog($"反序列化Halcon对象失败: {ex.Message}", MsgType.Error);
                throw new InvalidOperationException($"反序列化Halcon对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量序列化多个Halcon对象
        /// </summary>
        /// <param name="halconObjects">Halcon对象集合</param>
        /// <param name="options">序列化选项</param>
        /// <returns>JSON字符串</returns>
        public static string SerializeHalconObjects(IEnumerable<object> halconObjects, HalconSerializationOptions options = null)
        {
            try
            {
                if (halconObjects == null)
                    return null;

                options = options ?? DefaultOptions;

                var settings = CreateJsonSettings(options);
                return JsonConvert.SerializeObject(halconObjects, settings);
            }
            catch (Exception ex)
            {
                Logger.AddLog($"批量序列化Halcon对象失败: {ex.Message}", MsgType.Error);
                throw new InvalidOperationException($"批量序列化Halcon对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量反序列化多个Halcon对象
        /// </summary>
        /// <typeparam name="T">Halcon对象类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <param name="options">序列化选项</param>
        /// <returns>反序列化的Halcon对象集合</returns>
        public static IEnumerable<T> DeserializeHalconObjects<T>(string json, HalconSerializationOptions options = null) where T : class
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return null;

                options = options ?? DefaultOptions;

                var settings = CreateJsonSettings(options);
                return JsonConvert.DeserializeObject<IEnumerable<T>>(json, settings);
            }
            catch (Exception ex)
            {
                Logger.AddLog($"批量反序列化Halcon对象失败: {ex.Message}", MsgType.Error);
                throw new InvalidOperationException($"批量反序列化Halcon对象失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 文件操作方法

        /// <summary>
        /// 将Halcon对象序列化并保存到文件
        /// </summary>
        /// <typeparam name="T">Halcon对象类型</typeparam>
        /// <param name="halconObject">要序列化的Halcon对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">序列化选项</param>
        public static void SerializeToFile<T>(T halconObject, string filePath, HalconSerializationOptions options = null) where T : class
        {
            try
            {
                var json = SerializeHalconObject(halconObject, options);
                File.WriteAllText(filePath, json, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Logger.AddLog($"序列化Halcon对象到文件失败: {ex.Message}", MsgType.Error);
                throw new InvalidOperationException($"序列化Halcon对象到文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从文件反序列化Halcon对象
        /// </summary>
        /// <typeparam name="T">Halcon对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">序列化选项</param>
        /// <returns>反序列化的Halcon对象</returns>
        public static T DeserializeFromFile<T>(string filePath, HalconSerializationOptions options = null) where T : class
        {
            try
            {
                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                var json = File.ReadAllText(filePath, Encoding.UTF8);
                return DeserializeHalconObject<T>(json, options);
            }
            catch (Exception ex)
            {
                Logger.AddLog($"从文件反序列化Halcon对象失败: {ex.Message}", MsgType.Error);
                throw new InvalidOperationException($"从文件反序列化Halcon对象失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 创建JSON序列化设置
        /// </summary>
        /// <param name="options">序列化选项</param>
        /// <returns>JSON序列化设置</returns>
        private static JsonSerializerSettings CreateJsonSettings(HalconSerializationOptions options)
        {
            var settings = new JsonSerializerSettings
            {
                Formatting = options.FormatJson ? Formatting.Indented : Formatting.None,
                NullValueHandling = NullValueHandling.Ignore,
                DefaultValueHandling = DefaultValueHandling.Include,
                TypeNameHandling = options.IncludeTypeInfo ? TypeNameHandling.Auto : TypeNameHandling.None,
                Converters = new List<JsonConverter>
                {
                    new HObjectConverter(options),
                    new HTupleConverter(options),
                    new HRegionConverter(options),
                    new HXLDConverter(options),
                    new HShapeModelConverter(options),
                    new HImageConverter(options)
                }
            };

            return settings;
        }

        #endregion
    }

    #region Halcon对象自定义转换器

    /// <summary>
    /// HObject转换器基类
    /// </summary>
    public abstract class HalconConverterBase : JsonConverter
    {
        protected readonly JsonSerializerHelper.HalconSerializationOptions Options;

        protected HalconConverterBase(JsonSerializerHelper.HalconSerializationOptions options)
        {
            Options = options ?? JsonSerializerHelper.DefaultOptions;
        }

        protected void LogError(string message, Exception ex = null)
        {
            var fullMessage = ex != null ? $"{message}: {ex.Message}" : message;
            Logger.AddLog(fullMessage, MsgType.Error);
        }
    }

    /// <summary>
    /// HObject转换器
    /// </summary>
    public class HObjectConverter : HalconConverterBase
    {
        public HObjectConverter(JsonSerializerHelper.HalconSerializationOptions options) : base(options) { }

        public override bool CanConvert(Type objectType)
        {
            return typeof(HObject).IsAssignableFrom(objectType);
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            try
            {
                if (value == null)
                {
                    writer.WriteNull();
                    return;
                }

                var hObject = (HObject)value;
                writer.WriteStartObject();

                writer.WritePropertyName("$type");
                writer.WriteValue("HObject");

                writer.WritePropertyName("IsValid");
                writer.WriteValue(hObject.IsInitialized());

                if (hObject.IsInitialized())
                {
                    // 序列化HObject的关键信息
                    writer.WritePropertyName("Key");
                    writer.WriteValue(hObject.Key.ToString());

                    // 可以添加更多HObject特定的属性
                }

                writer.WriteEndObject();
            }
            catch (Exception ex)
            {
                LogError("序列化HObject失败", ex);
                throw;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            try
            {
                if (reader.TokenType == JsonToken.Null)
                    return null;

                var jObject = JObject.Load(reader);
                var isValid = jObject["IsValid"]?.Value<bool>() ?? false;

                if (!isValid)
                    return new HObject();

                // 这里需要根据具体的HObject类型进行反序列化
                // 由于HObject是抽象基类，实际应用中需要具体的子类转换器
                return new HObject();
            }
            catch (Exception ex)
            {
                LogError("反序列化HObject失败", ex);
                throw;
            }
        }
    }

    /// <summary>
    /// HTuple转换器
    /// </summary>
    public class HTupleConverter : HalconConverterBase
    {
        public HTupleConverter(JsonSerializerHelper.HalconSerializationOptions options) : base(options) { }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(HTuple);
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            try
            {
                if (value == null)
                {
                    writer.WriteNull();
                    return;
                }

                var hTuple = (HTuple)value;
                writer.WriteStartObject();

                writer.WritePropertyName("$type");
                writer.WriteValue("HTuple");

                writer.WritePropertyName("Length");
                writer.WriteValue(hTuple.Length);

                if (hTuple.Length > 0)
                {
                    writer.WritePropertyName("Type");
                    writer.WriteValue(hTuple.Type.ToString());

                    writer.WritePropertyName("Values");
                    writer.WriteStartArray();

                    for (int i = 0; i < hTuple.Length; i++)
                    {
                        switch (hTuple.Type)
                        {
                            case HTupleType.INTEGER:
                                writer.WriteValue(hTuple[i].I);
                                break;
                            case HTupleType.DOUBLE:
                                writer.WriteValue(hTuple[i].D);
                                break;
                            case HTupleType.STRING:
                                writer.WriteValue(hTuple[i].S);
                                break;
                            default:
                                writer.WriteValue(hTuple[i].ToString());
                                break;
                        }
                    }

                    writer.WriteEndArray();
                }

                writer.WriteEndObject();
            }
            catch (Exception ex)
            {
                LogError("序列化HTuple失败", ex);
                throw;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            try
            {
                if (reader.TokenType == JsonToken.Null)
                    return new HTuple();

                var jObject = JObject.Load(reader);
                var length = jObject["Length"]?.Value<int>() ?? 0;

                if (length == 0)
                    return new HTuple();

                var typeStr = jObject["Type"]?.Value<string>();
                var valuesArray = jObject["Values"] as JArray;

                if (valuesArray == null)
                    return new HTuple();

                var hTuple = new HTuple();

                if (Enum.TryParse<HTupleType>(typeStr, out var tupleType))
                {
                    switch (tupleType)
                    {
                        case HTupleType.INTEGER:
                            var intValues = valuesArray.Select(v => v.Value<int>()).ToArray();
                            hTuple = new HTuple(intValues);
                            break;
                        case HTupleType.DOUBLE:
                            var doubleValues = valuesArray.Select(v => v.Value<double>()).ToArray();
                            hTuple = new HTuple(doubleValues);
                            break;
                        case HTupleType.STRING:
                            var stringValues = valuesArray.Select(v => v.Value<string>()).ToArray();
                            hTuple = new HTuple(stringValues);
                            break;
                    }
                }

                return hTuple;
            }
            catch (Exception ex)
            {
                LogError("反序列化HTuple失败", ex);
                throw;
            }
        }
    }

    /// <summary>
    /// HRegion转换器
    /// </summary>
    public class HRegionConverter : HalconConverterBase
    {
        public HRegionConverter(JsonSerializerHelper.HalconSerializationOptions options) : base(options) { }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(HRegion);
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            try
            {
                if (value == null)
                {
                    writer.WriteNull();
                    return;
                }

                var hRegion = (HRegion)value;
                writer.WriteStartObject();

                writer.WritePropertyName("$type");
                writer.WriteValue("HRegion");

                // 获取区域的基本信息
                HTuple area, row, column;
                HOperatorSet.AreaCenter(hRegion, out area, out row, out column);

                writer.WritePropertyName("Area");
                writer.WriteValue(area.D);

                writer.WritePropertyName("CenterRow");
                writer.WriteValue(row.D);

                writer.WritePropertyName("CenterColumn");
                writer.WriteValue(column.D);

                // 获取区域的边界框
                HTuple row1, col1, row2, col2;
                hRegion.SmallestRectangle1(out row1, out col1, out row2, out col2);

                writer.WritePropertyName("BoundingBox");
                writer.WriteStartObject();
                writer.WritePropertyName("Row1");
                writer.WriteValue(row1.D);
                writer.WritePropertyName("Col1");
                writer.WriteValue(col1.D);
                writer.WritePropertyName("Row2");
                writer.WriteValue(row2.D);
                writer.WritePropertyName("Col2");
                writer.WriteValue(col2.D);
                writer.WriteEndObject();

                // 序列化区域数据（使用临时文件）
                var tempFile = Path.GetTempFileName();
                try
                {
                    hRegion.WriteRegion(tempFile);
                    var regionData = File.ReadAllBytes(tempFile);
                    writer.WritePropertyName("RegionData");
                    writer.WriteValue(Convert.ToBase64String(regionData));
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }

                writer.WriteEndObject();
            }
            catch (Exception ex)
            {
                LogError("序列化HRegion失败", ex);
                throw;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            try
            {
                if (reader.TokenType == JsonToken.Null)
                    return new HRegion();

                var jObject = JObject.Load(reader);
                var regionDataStr = jObject["RegionData"]?.Value<string>();

                if (string.IsNullOrEmpty(regionDataStr))
                    return new HRegion();

                var regionData = Convert.FromBase64String(regionDataStr);
                var tempFile = Path.GetTempFileName();

                try
                {
                    File.WriteAllBytes(tempFile, regionData);
                    var hRegion = new HRegion();
                    hRegion.ReadRegion(tempFile);
                    return hRegion;
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }
            }
            catch (Exception ex)
            {
                LogError("反序列化HRegion失败", ex);
                return new HRegion();
            }
        }
    }

    /// <summary>
    /// HXLD转换器
    /// </summary>
    public class HXLDConverter : HalconConverterBase
    {
        public HXLDConverter(JsonSerializerHelper.HalconSerializationOptions options) : base(options) { }

        public override bool CanConvert(Type objectType)
        {
            return typeof(HXLD).IsAssignableFrom(objectType);
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            try
            {
                if (value == null)
                {
                    writer.WriteNull();
                    return;
                }

                var hXLD = (HXLD)value;
                writer.WriteStartObject();

                writer.WritePropertyName("$type");
                writer.WriteValue("HXLD");

                // 序列化XLD数据（使用临时文件）
                var tempFile = Path.GetTempFileName();
                try
                {
                    // 使用正确的Halcon方法写入XLD数据
                    HOperatorSet.WriteContourXldDxf(hXLD, tempFile);
                    var xldData = File.ReadAllBytes(tempFile);
                    writer.WritePropertyName("XLDData");
                    writer.WriteValue(Convert.ToBase64String(xldData));
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }

                writer.WriteEndObject();
            }
            catch (Exception ex)
            {
                LogError("序列化HXLD失败", ex);
                throw;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            try
            {
                if (reader.TokenType == JsonToken.Null)
                    return new HXLD();

                var jObject = JObject.Load(reader);
                var xldDataStr = jObject["XLDData"]?.Value<string>();

                if (string.IsNullOrEmpty(xldDataStr))
                    return new HXLD();

                var xldData = Convert.FromBase64String(xldDataStr);
                var tempFile = Path.GetTempFileName();

                try
                {
                    File.WriteAllBytes(tempFile, xldData);
                    // 使用正确的Halcon方法读取XLD数据
                    HTuple hXLD;
                    HOperatorSet.ReadContourXldDxf(out hXLD, tempFile);
                    return new HXLD(hXLD);
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }
            }
            catch (Exception ex)
            {
                LogError("反序列化HXLD失败", ex);
                return new HXLD();
            }
        }
    }

    /// <summary>
    /// HShapeModel转换器
    /// </summary>
    public class HShapeModelConverter : HalconConverterBase
    {
        public HShapeModelConverter(JsonSerializerHelper.HalconSerializationOptions options) : base(options) { }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(HShapeModel);
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            try
            {
                if (value == null)
                {
                    writer.WriteNull();
                    return;
                }

                var hShapeModel = (HShapeModel)value;
                writer.WriteStartObject();

                writer.WritePropertyName("$type");
                writer.WriteValue("HShapeModel");

                // 序列化形状模型数据（使用临时文件）
                var tempFile = Path.GetTempFileName();
                try
                {
                    hShapeModel.WriteShapeModel(tempFile);
                    var modelData = File.ReadAllBytes(tempFile);
                    writer.WritePropertyName("ModelData");
                    writer.WriteValue(Convert.ToBase64String(modelData));
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }

                writer.WriteEndObject();
            }
            catch (Exception ex)
            {
                LogError("序列化HShapeModel失败", ex);
                throw;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            try
            {
                if (reader.TokenType == JsonToken.Null)
                    return new HShapeModel();

                var jObject = JObject.Load(reader);
                var modelDataStr = jObject["ModelData"]?.Value<string>();

                if (string.IsNullOrEmpty(modelDataStr))
                    return new HShapeModel();

                var modelData = Convert.FromBase64String(modelDataStr);
                var tempFile = Path.GetTempFileName();

                try
                {
                    File.WriteAllBytes(tempFile, modelData);
                    var hShapeModel = new HShapeModel();
                    hShapeModel.ReadShapeModel(tempFile);
                    return hShapeModel;
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }
            }
            catch (Exception ex)
            {
                LogError("反序列化HShapeModel失败", ex);
                return new HShapeModel();
            }
        }
    }

    /// <summary>
    /// HImage转换器
    /// </summary>
    public class HImageConverter : HalconConverterBase
    {
        public HImageConverter(JsonSerializerHelper.HalconSerializationOptions options) : base(options) { }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(HImage);
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            try
            {
                if (value == null)
                {
                    writer.WriteNull();
                    return;
                }

                var hImage = (HImage)value;
                writer.WriteStartObject();

                writer.WritePropertyName("$type");
                writer.WriteValue("HImage");

                // 获取图像基本信息
                HTuple width, height;
                hImage.GetImageSize(out width, out height);
                HTuple channels = hImage.CountChannels();

                writer.WritePropertyName("Width");
                writer.WriteValue(width.I);

                writer.WritePropertyName("Height");
                writer.WriteValue(height.I);

                writer.WritePropertyName("Channels");
                writer.WriteValue(channels.I);

                // 检查图像尺寸限制
                var totalPixels = width.I * height.I;
                if (totalPixels > Options.MaxImageSize)
                {
                    writer.WritePropertyName("ImageData");
                    writer.WriteValue("图像过大，已跳过序列化");
                    writer.WriteEndObject();
                    return;
                }

                // 序列化图像数据
                var tempFile = Path.GetTempFileName() + "." + Options.ImageFormat;
                try
                {
                    switch (Options.ImageFormat.ToLower())
                    {
                        case "png":
                            hImage.WriteImage("png", 0, tempFile);
                            break;
                        case "jpg":
                        case "jpeg":
                            hImage.WriteImage("jpeg", 0, tempFile);
                            break;
                        case "bmp":
                            hImage.WriteImage("bmp", 0, tempFile);
                            break;
                        default:
                            hImage.WriteImage("png", 0, tempFile);
                            break;
                    }

                    var imageData = File.ReadAllBytes(tempFile);
                    writer.WritePropertyName("ImageData");
                    writer.WriteValue(Convert.ToBase64String(imageData));

                    writer.WritePropertyName("ImageFormat");
                    writer.WriteValue(Options.ImageFormat);
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }

                writer.WriteEndObject();
            }
            catch (Exception ex)
            {
                LogError("序列化HImage失败", ex);
                throw;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            try
            {
                if (reader.TokenType == JsonToken.Null)
                    return new HImage();

                var jObject = JObject.Load(reader);
                var imageDataStr = jObject["ImageData"]?.Value<string>();

                if (string.IsNullOrEmpty(imageDataStr) || imageDataStr == "图像过大，已跳过序列化")
                    return new HImage();

                var imageData = Convert.FromBase64String(imageDataStr);
                var imageFormat = jObject["ImageFormat"]?.Value<string>() ?? "png";
                var tempFile = Path.GetTempFileName() + "." + imageFormat;

                try
                {
                    File.WriteAllBytes(tempFile, imageData);
                    var hImage = new HImage();
                    hImage.ReadImage(tempFile);
                    return hImage;
                }
                finally
                {
                    if (File.Exists(tempFile))
                        File.Delete(tempFile);
                }
            }
            catch (Exception ex)
            {
                LogError("反序列化HImage失败", ex);
                return new HImage();
            }
        }
    }

    #endregion
}
