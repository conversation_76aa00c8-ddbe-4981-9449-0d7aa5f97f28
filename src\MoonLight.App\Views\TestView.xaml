﻿<UserControl x:Class="MoonLight.App.Views.TestView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:model="clr-namespace:MoonLight.Core.Models;assembly=MoonLight.Core"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:MoonLight.App.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/MoonLight.Core;component/Assets/Collection.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <b:Interaction.Triggers>
        <b:EventTrigger EventName="Loaded">
            <b:InvokeCommandAction Command="{Binding LoadedCommand}"/>
        </b:EventTrigger>
    </b:Interaction.Triggers>
    <Grid Background="{DynamicResource MahApps.Brushes.ThemeBackground}">
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="auto"/>
            <RowDefinition/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="350"/>
            <ColumnDefinition Width="350"/>
            <ColumnDefinition/>
        </Grid.ColumnDefinitions>
        <GroupBox Header="方案操作接口">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="5">
                    <TextBlock Text="方案路径：" Margin="5"/>
                    <TextBox Text="{Binding SelectedSolPath}" Width="150" />
                    <Button Content="加载方案" Width="90" Margin="5 0" Command="{Binding LoadPathCommand}"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="5" IsEnabled="{Binding IsEnableSelectedSol}">
                    <TextBlock Text="当前方案：" Margin="5"/>
                    <TextBlock Text="{Binding SelectedSol.Name}" Margin="5"/>
                    <Button Content="保存方案" Width="90" Margin="118 0 0 0" Command="{Binding SaveSolCommand}"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>
        <GroupBox Header="流程操作接口" Grid.Row="1">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="5" IsEnabled="{Binding IsEnableSelectedSol}">
                    <TextBlock Text="选择流程：" Margin="5"/>
                    <ComboBox Width="150" ItemsSource="{Binding Projects}" SelectedItem="{Binding SelectedProject}" DisplayMemberPath="ProjectInfo.ProcessName"/>
                    <Button Content="删除流程" Width="90" Margin="5 0" Command="{Binding DeleteProjectCommand}"/>
                </StackPanel>
                <UniformGrid Columns="2" IsEnabled="{Binding IsEnableSelectedSol}">
                    <Button Content="显示模板匹配" Margin="5" Width="100" Command="{Binding MatchCommand}"/>
                    <Button Content="显示直线工具" Margin="5" Width="100" Command="{Binding FindLineCommand}"/>
                    <Button Content="显示直线工具1" Margin="5" Width="100" Command="{Binding FindLine1Command}"/>
                    <Button Content="执行工具" Margin="5" Width="100" Command="{Binding ExecuteCommand}"/>
                </UniformGrid>
            </StackPanel>
        </GroupBox>
        <GroupBox Header="流程控制接口" Grid.Row="2">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="15 5">
                    <TextBlock Text="执行时间：" FontSize="18"/>
                    <TextBlock Margin="3 0" FontSize="18">
                       <Run Text="{Binding SelectedProject.ConsumingTime}"/>
                       <Run Text="ms"/>
                    </TextBlock>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="15 5">
                    <TextBlock Text="执行结果：" FontSize="18" Margin="0 5"/>
                    <StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="角点X：" FontSize="16"/>
                            <TextBlock Text="{Binding CrossX}" FontSize="16"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="角点Y：" FontSize="16"/>
                            <TextBlock Text="{Binding CrossY}" FontSize="16"/>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
                <StackPanel Orientation="Horizontal" Margin="5" IsEnabled="{Binding IsEnableSelectedSol}">
                    <Button Content="执行一次" Width="90" Command="{Binding RunOnceCommand}"/>
                    <Button Content="循环执行" Width="90" Margin="5 0" Command="{Binding StartRunCommand}"/>
                    <Button Content="停止执行" Width="90" Command="{Binding StopRunCommand}"/>
                </StackPanel>
            </StackPanel>
        </GroupBox>
        <!--<ContentControl ml:View.Model="{Binding Shell}" />-->
        <GroupBox Header="属性栏" Grid.Column="1" Grid.RowSpan="3" Margin="1 0" IsEnabled="{Binding IsEnabled}">
            <ContentControl Content="{Binding PropertyControl}" Margin="-3 0 -3 0"/>
            
        </GroupBox>
        <ContentControl Grid.Column="2" Grid.RowSpan="3" Content="{Binding VisionControl}"/>
    </Grid>
</UserControl>
