﻿<Application x:Class="MoonLight.Test.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:MoonLight.App" 
             xmlns:ml="clr-namespace:MoonLight.UI;assembly=MoonLight">
    
    
    
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary>
                    <ml:AppBootstrapper x:Key="bootstrapper" />
                </ResourceDictionary>

                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Fonts.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Themes/Dark.Blue.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/MoonLight.Core;component/Assets/Collection.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
    
    
    
    
</Application>
