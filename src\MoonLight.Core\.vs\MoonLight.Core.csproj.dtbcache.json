{"RootPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core", "ProjectFileName": "MoonLight.Core.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Assets\\Controls\\Attatch\\TitleElement.cs"}, {"SourceFile": "Assets\\Controls\\Attatch\\VisualElement.cs"}, {"SourceFile": "Assets\\Controls\\Button\\ButtonGroup.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\DatePropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\DateTimePropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\EnumPropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\HorizontalAlignmentPropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\ImagePropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\NumberPropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\PlainTextPropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\ReadOnlyTextPropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\SwitchPropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\TimePropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\Editors\\VerticalAlignmentPropertyEditor.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\PropertyEditorBase.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\PropertyGrid.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\PropertyItem.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\PropertyItemsControl.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\PropertyResolver.cs"}, {"SourceFile": "Assets\\Controls\\PropertyGrid\\SearchBar.cs"}, {"SourceFile": "Assets\\Converter\\Bool2ColorConverter.cs"}, {"SourceFile": "Assets\\Converter\\Bool2GreenColorConverter.cs"}, {"SourceFile": "Assets\\Converter\\Bool2LimeConverter.cs"}, {"SourceFile": "Assets\\Converter\\Bool2VisibilityConverter.cs"}, {"SourceFile": "Assets\\Converter\\Bool2VisibilityHiddenConverter.cs"}, {"SourceFile": "Assets\\Converter\\BoolToBrushConverter.cs"}, {"SourceFile": "Assets\\Converter\\BoolToInverseConverter.cs"}, {"SourceFile": "Assets\\Converter\\BoolToLimeBrushConverter.cs"}, {"SourceFile": "Assets\\Converter\\BoolToRedBrushConverter.cs"}, {"SourceFile": "Assets\\Converter\\BrushRoundConverter.cs"}, {"SourceFile": "Assets\\Converter\\EnumArrayToVisibilityConverter.cs"}, {"SourceFile": "Assets\\Converter\\EnumConverter.cs"}, {"SourceFile": "Assets\\Converter\\EnumToVisibilityConverter.cs"}, {"SourceFile": "Assets\\Converter\\EnumToVisibilityConverter_2.cs"}, {"SourceFile": "Assets\\Converter\\ExpanderToBooleanConverter.cs"}, {"SourceFile": "Assets\\Converter\\IntToWobbleTypeConverter.cs"}, {"SourceFile": "Assets\\Converter\\InvertBool2VisibilityConverter.cs"}, {"SourceFile": "Assets\\Converter\\InvertBoolConverter.cs"}, {"SourceFile": "Assets\\Converter\\MediaColorToDrawingColorConverter.cs"}, {"SourceFile": "Assets\\Converter\\NullableToVisibilityConverter.cs"}, {"SourceFile": "Assets\\Converter\\Object2IntOrDoubleConverter.cs"}, {"SourceFile": "Assets\\Converter\\ObjectToStringConverter.cs"}, {"SourceFile": "Assets\\Converter\\PathToImageConverter.cs"}, {"SourceFile": "Assets\\Converter\\RowToIndexConverter.cs"}, {"SourceFile": "Assets\\Converter\\StatusConverter.cs"}, {"SourceFile": "Assets\\Data\\ResourceToken.cs"}, {"SourceFile": "Assets\\Data\\ValueBoxes.cs"}, {"SourceFile": "Assets\\Extension\\EnumerableExtension.cs"}, {"SourceFile": "Assets\\Extension\\UIElementExtension.cs"}, {"SourceFile": "Assets\\Interactivity\\ControlCommands.cs"}, {"SourceFile": "Assets\\Location\\Resources.Designer.cs"}, {"SourceFile": "Assets\\Modules\\Algorithms\\VisionException.cs"}, {"SourceFile": "Assets\\Modules\\Algorithms\\VisionLib.cs"}, {"SourceFile": "Assets\\Modules\\DeviceManager\\Models\\DeviceCategoryGroup.cs"}, {"SourceFile": "Assets\\Modules\\DeviceManager\\Models\\IDeviceManager.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Commands\\ViewFlowCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Commands\\ViewFlowCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Commands\\ViewPropertyPanelCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Commands\\ViewPropertyPanelCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Commands\\ViewRunManagerCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Commands\\ViewRunManagerCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\ViewModels\\FlowTreeViewModel.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\ViewModels\\PropertyPanelViewModel.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\ViewModels\\RunManagerViewModel.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Views\\FlowTreeView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Views\\PropertyPanelView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\FlowManager\\Views\\RunManagerView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\MenuDefinitions.cs"}, {"SourceFile": "Assets\\Modules\\Modules.cs"}, {"SourceFile": "Assets\\Modules\\Output\\Commands\\ViewOutputCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\Output\\Commands\\ViewOutputCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\Output\\Models\\IOutput.cs"}, {"SourceFile": "Assets\\Modules\\Output\\Models\\IOutputView.cs"}, {"SourceFile": "Assets\\Modules\\Output\\Models\\OutputWriter.cs"}, {"SourceFile": "Assets\\Modules\\Output\\ViewModels\\LogViewModel.cs"}, {"SourceFile": "Assets\\Modules\\Output\\ViewModels\\OutputViewModel.cs"}, {"SourceFile": "Assets\\Modules\\Output\\Views\\LogView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\Output\\Views\\OutputView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\PosManager\\Commands\\OpenPosManagerCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\PosManager\\Commands\\OpenPosManagerCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\PosManager\\ViewModels\\PosManagerViewModel.cs"}, {"SourceFile": "Assets\\Modules\\PosManager\\Views\\PosManagerView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\Commands\\ViewRenderControlCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\Commands\\ViewRenderControlCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\Managers\\RenderManager.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\ViewModels\\VisionViewModel.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\Views\\RenderView.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\Views\\RenderView.Designer.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\Views\\RenderViewWpf.xaml.cs"}, {"SourceFile": "Assets\\Modules\\RenderControl\\Views\\VisionView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\Settings\\Commands\\OpenParamSettingCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\Settings\\Commands\\OpenParamSettingCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\Settings\\Models\\SysConfig.cs"}, {"SourceFile": "Assets\\Modules\\Settings\\ViewModels\\SettingSysViewModel.cs"}, {"SourceFile": "Assets\\Modules\\Settings\\Views\\SettingSysView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\AddSolutionCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\AddSolutionCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\CommunicationSetCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\CommunicationSetCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\GlobalVarCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\GlobalVarCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\RunCycleCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\RunCycleCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\RunOnceCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\RunOnceCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\SaveFileCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\StopCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\StopCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\UIDesignCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\UIDesignCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\UserLoginCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Commands\\UserLoginCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\ToolBarDefinitions.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\ViewModels\\CanvasSetViewModel.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\ViewModels\\CommunicationSetViewModel.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\ViewModels\\GlobalVarViewModel.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Views\\CanvasSetView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Views\\CommunicationSetView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\ToolBar\\Views\\GlobalVarView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolBox\\Commands\\ViewToolBoxCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolBox\\ViewModels\\ToolBoxViewModel.cs"}, {"SourceFile": "Assets\\Modules\\ToolBox\\Views\\ToolBoxView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\Commands\\ViewDeviceStatusCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\Commands\\ViewDeviceStatusCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\Commands\\ViewToolOutputCommandDefinition.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\Commands\\ViewToolOutputCommandHandler.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\Models\\ToolOutputModel.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\ViewModels\\DeviceStatusViewModel.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\ViewModels\\ToolOutputViewModel.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\Views\\DeviceStatusView.xaml.cs"}, {"SourceFile": "Assets\\Modules\\ToolOutput\\Views\\ToolOutputView.xaml.cs"}, {"SourceFile": "Assets\\Styles\\Models\\Chip.cs"}, {"SourceFile": "Attributes\\PropertyAtrribute.cs"}, {"SourceFile": "Attributes\\ToolImageNameAttribute.cs"}, {"SourceFile": "Common\\EComInfo.cs"}, {"SourceFile": "Common\\Helper\\AsyncObservableCollection.cs"}, {"SourceFile": "Common\\Helper\\CloneObject.cs"}, {"SourceFile": "Common\\Helper\\CommandBase.cs"}, {"SourceFile": "Common\\Helper\\DataConvert.cs"}, {"SourceFile": "Common\\Helper\\EventTriggerAction.cs"}, {"SourceFile": "Common\\Helper\\FilePaths.cs"}, {"SourceFile": "Common\\Helper\\ImageTool.cs"}, {"SourceFile": "Common\\Helper\\ItemsControlHelp.cs"}, {"SourceFile": "Common\\Helper\\MD5Provider.cs"}, {"SourceFile": "Common\\Helper\\NotifyPropertyBase.cs"}, {"SourceFile": "Common\\Helper\\SerializeHelp.cs"}, {"SourceFile": "Common\\Helper\\SingleInstance.cs"}, {"SourceFile": "Common\\Helper\\StringHelper.cs"}, {"SourceFile": "Common\\Helper\\WPFCursorTool.cs"}, {"SourceFile": "Common\\Helper\\WPFElementTool.cs"}, {"SourceFile": "Common\\Helper\\XmlHelper.cs"}, {"SourceFile": "Common\\Log\\Logger.cs"}, {"SourceFile": "Common\\RightControl\\IsEnableControl.cs"}, {"SourceFile": "Common\\RightControl\\RightControl.cs"}, {"SourceFile": "Communacation\\EComManageer.cs"}, {"SourceFile": "Communacation\\Ecommunacation.cs"}, {"SourceFile": "Communacation\\SerialPort\\MySerialPort.cs"}, {"SourceFile": "Communacation\\Socket\\DMTcpClient.cs"}, {"SourceFile": "Communacation\\Socket\\DMTcpServer.cs"}, {"SourceFile": "Communacation\\Socket\\DMUdpClient.cs"}, {"SourceFile": "Communacation\\Socket\\IDataCell.cs"}, {"SourceFile": "Communacation\\Socket\\MsgCell.cs"}, {"SourceFile": "Communacation\\Socket\\MsgTypeCell.cs"}, {"SourceFile": "Communacation\\Socket\\ReceiveDataEventArgs.cs"}, {"SourceFile": "Communacation\\Socket\\ReceiveDataEventHandler.cs"}, {"SourceFile": "Communacation\\Socket\\SerHelper.cs"}, {"SourceFile": "Communacation\\Socket\\SocketState.cs"}, {"SourceFile": "Communacation\\Socket\\UdpLibrary.cs"}, {"SourceFile": "Communacation\\Tool\\HexTool.cs"}, {"SourceFile": "Defines\\CameraType.cs"}, {"SourceFile": "Defines\\CardType.cs"}, {"SourceFile": "Defines\\RotateAngle.cs"}, {"SourceFile": "Defines\\StepType.cs"}, {"SourceFile": "Defines\\VelType.cs"}, {"SourceFile": "Devices\\Camera\\CameraBase.cs"}, {"SourceFile": "Devices\\Camera\\CameraInfo.cs"}, {"SourceFile": "Devices\\Camera\\ICamera.cs"}, {"SourceFile": "Devices\\Camera\\ImageData.cs"}, {"SourceFile": "Devices\\Camera\\StatusCamera.cs"}, {"SourceFile": "Devices\\Communication\\CommunicationBase.cs"}, {"SourceFile": "Devices\\Communication\\CommunicationSetView.xaml.cs"}, {"SourceFile": "Devices\\Communication\\CommunicationSetViewModel.cs"}, {"SourceFile": "Devices\\Communication\\DMTcpClient.cs"}, {"SourceFile": "Devices\\Communication\\DMTcpServer.cs"}, {"SourceFile": "Devices\\Communication\\DMUdpClient.cs"}, {"SourceFile": "Devices\\Communication\\MySerialPort.cs"}, {"SourceFile": "Devices\\ConfigBase.cs"}, {"SourceFile": "Devices\\ConfigBaseEx.cs"}, {"SourceFile": "Devices\\ConfigDevice.cs"}, {"SourceFile": "Devices\\DeviceBase.cs"}, {"SourceFile": "Devices\\DeviceStatus.cs"}, {"SourceFile": "Devices\\DeviceTreeNode.cs"}, {"SourceFile": "Devices\\DeviceType.cs"}, {"SourceFile": "Devices\\IDevice.cs"}, {"SourceFile": "Devices\\IDeviceConfiguration.cs"}, {"SourceFile": "Devices\\Motion\\IAxis.cs"}, {"SourceFile": "Devices\\Motion\\IOBaseEx.cs"}, {"SourceFile": "Devices\\Motion\\LimitBase.cs"}, {"SourceFile": "Devices\\Motion\\StatusAxis.cs"}, {"SourceFile": "Devices\\Motion\\StatusCard.cs"}, {"SourceFile": "Devices\\Motion\\StatusPmac.cs"}, {"SourceFile": "Devices\\Motion\\ViewModels\\CardViewModel.cs"}, {"SourceFile": "Devices\\Motion\\Views\\CardView.xaml.cs"}, {"SourceFile": "Devices\\StatusBase.cs"}, {"SourceFile": "Enums\\EnumType.cs"}, {"SourceFile": "Enums\\UserType.cs"}, {"SourceFile": "Events\\AddCameraEvent.cs"}, {"SourceFile": "Events\\CurrentUserChangedEvent.cs"}, {"SourceFile": "Events\\FunctionEventArgs.cs"}, {"SourceFile": "Events\\HardwareChangedEvent.cs"}, {"SourceFile": "Events\\ModuleOutChangedEvent.cs"}, {"SourceFile": "Events\\OpenVarLinkViewEvent.cs"}, {"SourceFile": "Events\\SoftwareExitEvent.cs"}, {"SourceFile": "Events\\VarChangedEvent.cs"}, {"SourceFile": "Extension\\ObservableCollectionExtension.cs"}, {"SourceFile": "Interfaces\\IFileSystemFactory.cs"}, {"SourceFile": "Interfaces\\IFileSystemService.cs"}, {"SourceFile": "Interfaces\\IFlow.cs"}, {"SourceFile": "Interfaces\\IProcess.cs"}, {"SourceFile": "Interfaces\\IRenderView.cs"}, {"SourceFile": "Interfaces\\IRenderViewManager.cs"}, {"SourceFile": "Interfaces\\IToolUnit.cs"}, {"SourceFile": "Models\\AlarmSummaryModel.cs"}, {"SourceFile": "Models\\CommonMethods.cs"}, {"SourceFile": "Models\\DragDropModel.cs"}, {"SourceFile": "Models\\Fit.cs"}, {"SourceFile": "Models\\Gen.cs"}, {"SourceFile": "Models\\IHierarchical.cs"}, {"SourceFile": "Models\\Line.cs"}, {"SourceFile": "Models\\LinkVarModel.cs"}, {"SourceFile": "Models\\LogModel.cs"}, {"SourceFile": "Models\\MachineModel.cs"}, {"SourceFile": "Models\\ToolViewBase.cs"}, {"SourceFile": "Models\\MotionBase.cs"}, {"SourceFile": "Models\\PluginsInfo.cs"}, {"SourceFile": "Models\\ProjectInfo.cs"}, {"SourceFile": "Models\\Sol\\Drive.cs"}, {"SourceFile": "Models\\Sol\\DriveNode.cs"}, {"SourceFile": "Models\\Sol\\File.cs"}, {"SourceFile": "Models\\Sol\\FileNode.cs"}, {"SourceFile": "Models\\Sol\\FileSystemObject.cs"}, {"SourceFile": "Models\\Sol\\FileSystemObjectNode.cs"}, {"SourceFile": "Models\\Sol\\Folder.cs"}, {"SourceFile": "Models\\Sol\\FolderNode.cs"}, {"SourceFile": "Models\\ToolList.cs"}, {"SourceFile": "Models\\ToolNode.cs"}, {"SourceFile": "Models\\ToolUnit.cs"}, {"SourceFile": "Models\\TreeNode.cs"}, {"SourceFile": "Models\\UserModel.cs"}, {"SourceFile": "Models\\VarModel.cs"}, {"SourceFile": "Models\\VisionInfo.cs"}, {"SourceFile": "Models\\VisionParam.cs"}, {"SourceFile": "Models\\VisionResult.cs"}, {"SourceFile": "Devices\\Motion\\AxisBase.cs"}, {"SourceFile": "Devices\\Motion\\AxisStatus.cs"}, {"SourceFile": "Devices\\Motion\\CardBase.cs"}, {"SourceFile": "Devices\\Motion\\ICard.cs"}, {"SourceFile": "Devices\\Motion\\IOBase.cs"}, {"SourceFile": "Devices\\Motion\\MotionHelper.cs"}, {"SourceFile": "Devices\\Motion\\UnitStatus.cs"}, {"SourceFile": "Devices\\Motion\\ViewModels\\AxisControlViewModel.cs"}, {"SourceFile": "Devices\\Motion\\Views\\AxisControlView.xaml.cs"}, {"SourceFile": "Modules\\PosManager\\IPosManager.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "ROIs\\HRoi.cs"}, {"SourceFile": "ROIs\\HText.cs"}, {"SourceFile": "ROIs\\ROI.cs"}, {"SourceFile": "ROIs\\ROICaliper.cs"}, {"SourceFile": "ROIs\\ROICaliper1D.cs"}, {"SourceFile": "ROIs\\ROICircle.cs"}, {"SourceFile": "ROIs\\ROICircleCaliper.cs"}, {"SourceFile": "ROIs\\ROICoordinates.cs"}, {"SourceFile": "ROIs\\ROIInfo.cs"}, {"SourceFile": "ROIs\\ROILine.cs"}, {"SourceFile": "ROIs\\ROIRectangle.cs"}, {"SourceFile": "ROIs\\ROIRectangle2.cs"}, {"SourceFile": "ROIs\\ROIRectCaliper.cs"}, {"SourceFile": "ROIs\\ROIText.cs"}, {"SourceFile": "Scrip\\BoolScriptSupport.cs"}, {"SourceFile": "Scrip\\BoolScriptTemplate.cs"}, {"SourceFile": "Scrip\\ExpressionScriptSupport.cs"}, {"SourceFile": "Scrip\\ExpressionScriptTemplate.cs"}, {"SourceFile": "Scrip\\ScriptMethods.cs"}, {"SourceFile": "Scrip\\ScriptProvider.cs"}, {"SourceFile": "Services\\EngineService.cs"}, {"SourceFile": "Services\\FileIconService.cs"}, {"SourceFile": "Services\\FileSystemFactory.cs"}, {"SourceFile": "Services\\FileSystemService.cs"}, {"SourceFile": "Services\\PluginService.cs"}, {"SourceFile": "Services\\Project.cs"}, {"SourceFile": "Services\\ProjectFlowTree.cs"}, {"SourceFile": "Services\\ResourceHelper.cs"}, {"SourceFile": "Services\\SolManager.cs"}, {"SourceFile": "Services\\Solution.cs"}, {"SourceFile": "Config\\RImage.cs"}, {"SourceFile": "Services\\SolutionInfo.cs"}, {"SourceFile": "ViewModels\\EditRemarksViewModel.cs"}, {"SourceFile": "ViewModels\\HardwareConfigViewModel.cs"}, {"SourceFile": "ViewModels\\LoadingViewModel.cs"}, {"SourceFile": "ViewModels\\MessageBoxViewModel.cs"}, {"SourceFile": "ViewModels\\VarLinkViewModel.cs"}, {"SourceFile": "Views\\ChangePwdView.xaml.cs"}, {"SourceFile": "Views\\EditRemarksView.xaml.cs"}, {"SourceFile": "Views\\HardwareConfigView.xaml.cs"}, {"SourceFile": "Views\\LoadingView.xaml.cs"}, {"SourceFile": "Views\\LoginView.xaml.cs"}, {"SourceFile": "Views\\MessageBoxView.xaml.cs"}, {"SourceFile": "Views\\VarLinkView.xaml.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\FlowManager\\Views\\FlowTreeView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\FlowManager\\Views\\PropertyPanelView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\FlowManager\\Views\\RunManagerView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\Output\\Views\\LogView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\Output\\Views\\OutputView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\PosManager\\Views\\PosManagerView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\RenderControl\\Views\\RenderViewWpf.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\RenderControl\\Views\\VisionView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\Settings\\Views\\SettingSysView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\ToolBar\\Views\\CanvasSetView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\ToolBar\\Views\\CommunicationSetView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\ToolBar\\Views\\GlobalVarView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\ToolBox\\Views\\ToolBoxView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\ToolOutput\\Views\\DeviceStatusView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Assets\\Modules\\ToolOutput\\Views\\ToolOutputView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Devices\\Communication\\CommunicationSetView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Devices\\Motion\\Views\\AxisControlView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Devices\\Motion\\Views\\CardView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Views\\ChangePwdView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Views\\EditRemarksView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Views\\HardwareConfigView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Views\\LoadingView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Views\\LoginView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Views\\MessageBoxView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\Views\\VarLinkView.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\MoonLight.Core\\obj\\Debug\\GeneratedInternalTypeHelper.g.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\CommunityToolkit.Mvvm.8.4.0\\lib\\netstandard2.0\\CommunityToolkit.Mvvm.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\EventMgrLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\GxIAPINET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\halcondotnetxl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\HandyControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\HslCommunication.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\ICSharpCode.CodeCompletion.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MahApps.Metro.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\Microsoft.Bcl.AsyncInterfaces.8.0.0\\lib\\net462\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.VisualBasic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\Microsoft.Xaml.Behaviors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MoonLight.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MoonLight.dll"}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MoonLight.Modules.DeviceManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MoonLight.Modules.DeviceManager.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MvCameraControl.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\System.Buffers.4.6.0\\lib\\net462\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\System.ComponentModel.Annotations.5.0.0\\lib\\net461\\System.ComponentModel.Annotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\System.Memory.4.6.0\\lib\\net462\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\System.Numerics.Vectors.4.6.0\\lib\\net462\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\System.Runtime.CompilerServices.Unsafe.6.1.0\\lib\\net462\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\src\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsFormsIntegration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\视觉平台优化\\MoonLight.Platform_V2_20150514\\bin\\MoonLight.Core.dll", "OutputItemRelativePath": "MoonLight.Core.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}