﻿using MoonLight.UI.Framework.Commands;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MoonLight.Core.Assets.Modules.PosManager.Commands
{
    [CommandDefinition]
    public class OpenPosManagerCommandDefinition : CommandDefinition
    {
        public const string CommandName = "Tools.PosManager";

        public override string Name
        {
            get { return CommandName; }
        }

        public override string Text
        {
            get { return "点位管理器"; }
        }

        public override string ToolTip
        {
            get { return "点位管理器"; }
        }
    }
}
