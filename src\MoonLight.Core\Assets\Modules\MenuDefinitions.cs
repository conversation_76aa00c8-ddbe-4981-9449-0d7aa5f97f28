﻿using MoonLight.Modules.FlowManager.Commands;
using MoonLight.Modules.Output.Commands;
using MoonLight.Modules.RenderControl.Commands;
using MoonLight.UI.Framework.Menus;
using MoonLight.Modules.ToolBox.Commands;
using System.ComponentModel.Composition;
using MoonLight.Modules.ToolOutput.Commands;
using MoonLight.Core.Assets.Modules.Settings.Commands;

namespace MoonLight.Core.Assets.Modules
{
    public static class MenuDefinitions
    {
        //[Export]
        //public static readonly MenuItemDefinition OpenDeviceManagerMenuItem = new CommandMenuItemDefinition<OpenDeviceManagerCommandDefinition>(
        //    MoonLight.UI.Modules.MainMenu.MenuDefinitions.ToolsOptionsMenuGroup, 0);

        [Export]
        public static readonly MenuItemDefinition OpenParamSettingMenuItem = new CommandMenuItemDefinition<OpenParamSettingCommandDefinition>(
           MoonLight.UI.Modules.MainMenu.MenuDefinitions.ToolsOptionsMenuGroup, 1);



        [Export]
        public static readonly MenuItemDefinition ViewOutputMenuItem = new CommandMenuItemDefinition<ViewOutputCommandDefinition>(
            UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 1);

        [Export]
        public static MenuItemDefinition ViewToolBoxItem = new CommandMenuItemDefinition<ViewToolBoxCommandDefinition>(
            UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 2);

        [Export]
        public static MenuItemDefinition ViewFlowMenuItem = new CommandMenuItemDefinition<ViewFlowCommandDefinition>(
            UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 3);

        [Export]
        public static MenuItemDefinition ViewRenderControlItem = new CommandMenuItemDefinition<ViewRenderControlCommandDefinition>(
            UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 4);

        [Export]
        public static MenuItemDefinition ViewRunManagerMenuItem = new CommandMenuItemDefinition<ViewRunManagerCommandDefinition>(
          UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 5);

        [Export]
        public static MenuItemDefinition ViewPropertyPanelItem = new CommandMenuItemDefinition<ViewPropertyPanelCommandDefinition>(
            UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 6);

        [Export]
        public static MenuItemDefinition ViewToolOutputItem = new CommandMenuItemDefinition<ViewToolOutputCommandDefinition>(
            UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 7);

        [Export]
        public static MenuItemDefinition ViewDeviceStatusItem = new CommandMenuItemDefinition<ViewDeviceStatusCommandDefinition>(
            UI.Modules.MainMenu.MenuDefinitions.ViewToolsMenuGroup, 8);

    }
}
