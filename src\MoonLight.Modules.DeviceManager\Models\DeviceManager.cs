using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows;
using System.Xml.Serialization;
using MoonLight.Core.Devices;
using MoonLight.Core.Devices.Camera;
using Newtonsoft.Json;
using System.ComponentModel.Composition;
using System.Xml.Linq;
using MoonLight.Core.Motion;
using MvCamCtrl.NET;
using MoonLight.Core.Common.Helper;
using System.Reflection;
using Newtonsoft.Json.Converters;
using MoonLight.Core.Modules;

namespace MoonLight.Modules.DeviceManager.Models
{
    [Export(typeof(IDeviceManager))]     
    public class DeviceManager : SingleInstance<DeviceManager>, IDeviceManager
    {
        private readonly Dictionary<string, IDevice> _devices = new Dictionary<string, IDevice>();
        private const string ConfigFileName = "DeviceConfig.xml";
        private IDevice _selectedDevice;
        private FrameworkElement _currentConfigurationUI;

        public ObservableCollection<DeviceCategoryGroup> DeviceGroups { get; private set; } = new ObservableCollection<DeviceCategoryGroup>();

        public IDevice SelectedDevice
        {
            get => _selectedDevice;
            set
            {
                if (_selectedDevice != value)
                {
                    _selectedDevice = value;
                    UpdateConfigurationUI();
                }
            }
        }

        public FrameworkElement CurrentConfigurationUI
        {
            get => _currentConfigurationUI;
            set
            {
                _currentConfigurationUI = value;
                ConfigurationUIChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        public event EventHandler ConfigurationUIChanged;

        public DeviceManager()
        {
            // 初始化设备分类组
            foreach (DeviceCategory category in Enum.GetValues(typeof(DeviceCategory)))
            {
                DeviceGroups.Add(new DeviceCategoryGroup(category));
            }
        }

        public override bool Init()
        {
             
            return true;
        }


        public List<CameraBase> GetCamDevices()
        {
            List<CameraBase> values = new List<CameraBase>();
            var list = DeviceGroups.ToList();
            var devs = list.First(x => x.Category == DeviceCategory.Camera).Devices;
            foreach (var item in devs)
            {
                values.Add(item as CameraBase);        
            }
            return values;  
        }

        public List<CardBase> GetCardDevices()
        {
            List<CardBase> values = new List<CardBase>();
            var list = DeviceGroups.ToList();
            var devs = list.First(x => x.Category == DeviceCategory.MotionController).Devices;
            foreach (var item in devs)
            {
                values.Add(item as CardBase);
            }
            return values;
        }


        private void UpdateConfigurationUI()
        {
            if (SelectedDevice is IDevice device)
            {
                CurrentConfigurationUI = device.GetConfigurationView();
            }
            else if(SelectedDevice == null)
            {
                CurrentConfigurationUI = new TextBlock
                {
                    Text = "当前设备无配置文件",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
            }
        }

        public IDevice GetDeviceById(string id)
        {
            return _devices.TryGetValue(id, out var device) ? device : null;
        }

        public IDevice GetDeviceByName(string name)
        {
            if (name == null) return null;
            return _devices.First(x=>x.Value.Name ==  name).Value;        

        }

        public void AddDevice(IDevice device)
        {
            if (device == null) throw new ArgumentNullException(nameof(device));

            // 确保名称唯一
            if (string.IsNullOrEmpty(device.Name))
            {
                device.Name = GetDefaultDeviceName(device.Category);
            }

            device.Name = GetUniqueDeviceName(device.Name);

            _devices[device.Id] = device;

            var group = DeviceGroups.FirstOrDefault(g => g.Category == device.Category);
            group?.Devices.Add(device);

            SelectedDevice = device;
        }

        private string GetDefaultDeviceName(DeviceCategory category)
        {
            return category switch
            {
                DeviceCategory.Camera => "工业相机",
                DeviceCategory.BarcodeReader => "条码阅读器",
                DeviceCategory.NetworkDevice => "网络设备",
                DeviceCategory.SerialDevice => "串口设备",
                DeviceCategory.MotionController => "运动控制卡",
                _ => "新设备"
            };
        }

        private string GetUniqueDeviceName(string baseName)
        {
            int counter = 1;
            string newName = baseName;

            while (_devices.Values.Any(d => d.Name == newName))
            {
                newName = $"{baseName}_{counter++}";
            }

            return newName;
        }

        public void RemoveDevice(string deviceId)
        {
            if (!_devices.ContainsKey(deviceId)) return;

            var device = _devices[deviceId];
            var group = DeviceGroups.FirstOrDefault(g => g.Category == device.Category);

            if (group != null)
            {
                group.Devices.Remove(device);
            }
            _devices.Remove(deviceId);

            // 如果删除的是当前选中的设备，清空选择
            if (SelectedDevice?.Id == deviceId)
            {
                SelectedDevice = null;
            }
        }
 
        public void SaveDeviceConfiguration()
        {
            // 更新当前设备的配置
            if (SelectedDevice is IDeviceConfigurationUI configurableDevice)
            {
                configurableDevice.UpdateConfiguration();
            }


            JsonSerializerSettings settings = new JsonSerializerSettings()
            {
                TypeNameHandling = TypeNameHandling.All,
                PreserveReferencesHandling = PreserveReferencesHandling.All,
                TypeNameAssemblyFormatHandling = TypeNameAssemblyFormatHandling.Full
            };
      
            string json = JsonConvert.SerializeObject(DeviceGroups, settings);
            File.WriteAllText("deviceGroups.json", json);


        }

        public void LoadDeviceConfiguration()
        {
            try
            {
                if (!File.Exists("deviceGroups.json")) return;
                JsonSerializerSettings settings = new JsonSerializerSettings()
                {
                    TypeNameHandling = TypeNameHandling.All,
                    PreserveReferencesHandling = PreserveReferencesHandling.Objects,
                    TypeNameAssemblyFormatHandling = TypeNameAssemblyFormatHandling.Full,
                   
                };


                string jsonFromFile = File.ReadAllText("deviceGroups.json");
                var deserializedGroups = JsonConvert.DeserializeObject<ObservableCollection<DeviceCategoryGroup>>(jsonFromFile, settings);

                // 清除现有设备
                _devices.Clear();
                foreach (var group in DeviceGroups)
                {
                    group.Devices.Clear();
                }

                // 重建设备
                foreach (var deviceConfig in deserializedGroups)
                {

                    foreach (var item in deviceConfig.Devices)
                    {
                        AddDevice(item);
                    }
                }
            }
            catch(Exception ex)
            {

            }

        }

        private IDevice CreateDeviceByCategory(DeviceCategory category)
        {
            return category switch
            {
                DeviceCategory.Camera => new CameraBase(),
                DeviceCategory.BarcodeReader => new CameraBase(),
                DeviceCategory.NetworkDevice => new CameraBase(),
                DeviceCategory.SerialDevice => throw new NotImplementedException(),
                DeviceCategory.MotionController => throw new NotImplementedException(),
                _ => throw new ArgumentOutOfRangeException()
            };
        }

 

        [Serializable]
        public class DeviceManagerConfig
        {
            public List<DeviceConfig> Devices { get; set; }
        }

        [Serializable]
        public class DeviceConfig
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public DeviceCategory Category { get; set; }
            public bool IsEnabled { get; set; }
            public Dictionary<string, object> Configuration { get; set; }
        }

       
    }
}
