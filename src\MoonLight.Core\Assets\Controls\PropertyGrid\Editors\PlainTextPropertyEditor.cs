﻿using System.Windows;
using System.Windows.Controls;

namespace MoonLight.Core.Assets.Controls
{
    public class PlainTextPropertyEditor : PropertyEditorBase
    {
        public override FrameworkElement CreateElement(PropertyItem propertyItem)
        {
            TextBox textBox = new TextBox()
            {
                IsReadOnly = propertyItem.IsReadOnly,
                Width = 180,
                HorizontalAlignment = HorizontalAlignment.Stretch, // 使其可拉伸以适应父控件  
            };
            textBox.Style = (Style)Application.Current.Resources["MahApps.Styles.TextBox"];

            return textBox;
        }
        

        public override DependencyProperty GetDependencyProperty() => System.Windows.Controls.TextBox.TextProperty;
    }
}
