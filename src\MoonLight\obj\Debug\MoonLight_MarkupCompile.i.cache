﻿MoonLight


library
C#
.cs
C:\Users\<USER>\Desktop\视觉平台优化\MoonLight.Platform_0506\src\MoonLight\obj\Debug\
MoonLight
none
false


33-852976742

3141647435186
28-32227234
UI\Modules\MainMenu\Resources\Styles.xaml;UI\Modules\MainMenu\Views\MainMenuSettingsView.xaml;UI\Modules\MainMenu\Views\MainMenuView.xaml;UI\Modules\MainWindow\Views\MainWindowView.xaml;UI\Modules\Settings\SampleData\SettingsViewModelSampleData.xaml;UI\Modules\Settings\Views\SettingsView.xaml;UI\Modules\Shell\Views\ShellView.xaml;UI\Modules\StatusBar\Views\StatusBarView.xaml;UI\Modules\ToolBars\Resources\Styles.xaml;UI\Modules\ToolBars\Views\ToolBarsView.xaml;UI\Modules\Toolbox\Views\ToolboxView.xaml;UI\Modules\UndoRedo\Views\HistoryView.xaml;UI\Themes\Generic.xaml;UI\Themes\VS2013\BlueTheme.xaml;UI\Themes\VS2013\Controls\Button.xaml;UI\Themes\VS2013\Controls\CheckBox.xaml;UI\Themes\VS2013\Controls\ColorCanvas.xaml;UI\Themes\VS2013\Controls\ComboBox.xaml;UI\Themes\VS2013\Controls\Focus.xaml;UI\Themes\VS2013\Controls\Label.xaml;UI\Themes\VS2013\Controls\Menu.xaml;UI\Themes\VS2013\Controls\Merged.xaml;UI\Themes\VS2013\Controls\ScrollBar.xaml;UI\Themes\VS2013\Controls\Slider.xaml;UI\Themes\VS2013\Controls\TabControl.xaml;UI\Themes\VS2013\Controls\TextBox.xaml;UI\Themes\VS2013\Controls\Toolbar.xaml;UI\Themes\VS2013\Controls\Tooltip.xaml;UI\Themes\VS2013\Controls\TreeView.xaml;UI\Themes\VS2013\Controls\Window.xaml;UI\Themes\VS2013\Controls\WindowCommands.xaml;UI\Themes\VS2013\DarkTheme.xaml;UI\Themes\VS2013\LightTheme.xaml;

True

