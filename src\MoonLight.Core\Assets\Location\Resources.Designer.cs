﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace MoonLight.Core.Location {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("VM.Start.Localization.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 关于 的本地化字符串。
        /// </summary>
        internal static string About {
            get {
                return ResourceManager.GetString("About", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 添加 的本地化字符串。
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 管理员 的本地化字符串。
        /// </summary>
        internal static string Administrator {
            get {
                return ResourceManager.GetString("Administrator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 管理员登录系统。 的本地化字符串。
        /// </summary>
        internal static string AdministratorLoginSystem {
            get {
                return ResourceManager.GetString("AdministratorLoginSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报警 的本地化字符串。
        /// </summary>
        internal static string Alarm {
            get {
                return ResourceManager.GetString("Alarm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 条码报警 的本地化字符串。
        /// </summary>
        internal static string AlmBarcode {
            get {
                return ResourceManager.GetString("AlmBarcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率报警 的本地化字符串。
        /// </summary>
        internal static string AlmPower {
            get {
                return ResourceManager.GetString("AlmPower", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 应用 的本地化字符串。
        /// </summary>
        internal static string Application {
            get {
                return ResourceManager.GetString("Application", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 扫码输入 的本地化字符串。
        /// </summary>
        internal static string BarcodeInput {
            get {
                return ResourceManager.GetString("BarcodeInput", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 扫码枪参数 的本地化字符串。
        /// </summary>
        internal static string BarcodeParam {
            get {
                return ResourceManager.GetString("BarcodeParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 蜂鸣器 的本地化字符串。
        /// </summary>
        internal static string Buzzer {
            get {
                return ResourceManager.GetString("Buzzer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 相机 的本地化字符串。
        /// </summary>
        internal static string Camera {
            get {
                return ResourceManager.GetString("Camera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 相机设置 的本地化字符串。
        /// </summary>
        internal static string CameraSet {
            get {
                return ResourceManager.GetString("CameraSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 取 消 的本地化字符串。
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 中文 的本地化字符串。
        /// </summary>
        internal static string Chinese {
            get {
                return ResourceManager.GetString("Chinese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 清除报警 的本地化字符串。
        /// </summary>
        internal static string ClearAlarm {
            get {
                return ResourceManager.GetString("ClearAlarm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率下限 的本地化字符串。
        /// </summary>
        internal static string ColorSetView_PowerDownLimit {
            get {
                return ResourceManager.GetString("ColorSetView_PowerDownLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前功率 的本地化字符串。
        /// </summary>
        internal static string ColorSetView_PowerPV {
            get {
                return ResourceManager.GetString("ColorSetView_PowerPV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率上限 的本地化字符串。
        /// </summary>
        internal static string ColorSetView_PowerUpLimit {
            get {
                return ResourceManager.GetString("ColorSetView_PowerUpLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 通讯设置 的本地化字符串。
        /// </summary>
        internal static string CommunicationSet {
            get {
                return ResourceManager.GetString("CommunicationSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确 认 的本地化字符串。
        /// </summary>
        internal static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确认重置吗？ 的本地化字符串。
        /// </summary>
        internal static string ConfirmReset {
            get {
                return ResourceManager.GetString("ConfirmReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前文档: 的本地化字符串。
        /// </summary>
        internal static string CurrentFile {
            get {
                return ResourceManager.GetString("CurrentFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前加工对象: 的本地化字符串。
        /// </summary>
        internal static string CurrentProcessingObject {
            get {
                return ResourceManager.GetString("CurrentProcessingObject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前配方: 的本地化字符串。
        /// </summary>
        internal static string CurrentRecipe {
            get {
                return ResourceManager.GetString("CurrentRecipe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前解决方案 的本地化字符串。
        /// </summary>
        internal static string CurrentSolution {
            get {
                return ResourceManager.GetString("CurrentSolution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前用户: 的本地化字符串。
        /// </summary>
        internal static string CurrentUser {
            get {
                return ResourceManager.GetString("CurrentUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 曲线颜色设置 的本地化字符串。
        /// </summary>
        internal static string CurveColorSetting {
            get {
                return ResourceManager.GetString("CurveColorSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 曲线刷新时X轴区间 的本地化字符串。
        /// </summary>
        internal static string CurveUpdateRangeX {
            get {
                return ResourceManager.GetString("CurveUpdateRangeX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 气缸配置 的本地化字符串。
        /// </summary>
        internal static string CylConfig {
            get {
                return ResourceManager.GetString("CylConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 开发者 的本地化字符串。
        /// </summary>
        internal static string Developer {
            get {
                return ResourceManager.GetString("Developer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 开发者登录系统。 的本地化字符串。
        /// </summary>
        internal static string DeveloperLoginSystem {
            get {
                return ResourceManager.GetString("DeveloperLoginSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设备报警 的本地化字符串。
        /// </summary>
        internal static string DeviceAlarm {
            get {
                return ResourceManager.GetString("DeviceAlarm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设备参数 的本地化字符串。
        /// </summary>
        internal static string DeviceParam {
            get {
                return ResourceManager.GetString("DeviceParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 启动软件时自动打开相机 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_AutoOpenCamera {
            get {
                return ResourceManager.GetString("DeviceParam_AutoOpenCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 相机 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_Camera {
            get {
                return ResourceManager.GetString("DeviceParam_Camera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前工艺 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_CurrentRecipe {
            get {
                return ResourceManager.GetString("DeviceParam_CurrentRecipe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设备参数 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_DeviceParam {
            get {
                return ResourceManager.GetString("DeviceParam_DeviceParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 其他 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_Else {
            get {
                return ResourceManager.GetString("DeviceParam_Else", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 常用参数 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_GeneralParam {
            get {
                return ResourceManager.GetString("DeviceParam_GeneralParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 视频图像左右翻转 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_IsColumnMirror {
            get {
                return ResourceManager.GetString("DeviceParam_IsColumnMirror", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 视频图像上下翻转 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_IsRowMirror {
            get {
                return ResourceManager.GetString("DeviceParam_IsRowMirror", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 新增配方 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_new_RoutedCommandRecipe {
            get {
                return ResourceManager.GetString("DeviceParam_new RoutedCommandRecipe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 路径... 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_Path {
            get {
                return ResourceManager.GetString("DeviceParam_Path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 工艺参数 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_ProcessParam {
            get {
                return ResourceManager.GetString("DeviceParam_ProcessParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 工艺 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_Recipe {
            get {
                return ResourceManager.GetString("DeviceParam_Recipe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 保存加工结果 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_SaveManufactureResult {
            get {
                return ResourceManager.GetString("DeviceParam_SaveManufactureResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 使用相机 的本地化字符串。
        /// </summary>
        internal static string DeviceParam_UseCamera {
            get {
                return ResourceManager.GetString("DeviceParam_UseCamera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 显示 的本地化字符串。
        /// </summary>
        internal static string Display {
            get {
                return ResourceManager.GetString("Display", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [数据曲线] 的本地化字符串。
        /// </summary>
        internal static string Dock_Curve {
            get {
                return ResourceManager.GetString("Dock_Curve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [数据栏] 的本地化字符串。
        /// </summary>
        internal static string Dock_Data {
            get {
                return ResourceManager.GetString("Dock_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [设备状态] 的本地化字符串。
        /// </summary>
        internal static string Dock_DeviceState {
            get {
                return ResourceManager.GetString("Dock_DeviceState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [日志信息] 的本地化字符串。
        /// </summary>
        internal static string Dock_Log {
            get {
                return ResourceManager.GetString("Dock_Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [加工参数] 的本地化字符串。
        /// </summary>
        internal static string Dock_ManufactureParam {
            get {
                return ResourceManager.GetString("Dock_ManufactureParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [模块输出] 的本地化字符串。
        /// </summary>
        internal static string Dock_ModuleOut {
            get {
                return ResourceManager.GetString("Dock_ModuleOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [流程栏] 的本地化字符串。
        /// </summary>
        internal static string Dock_Process {
            get {
                return ResourceManager.GetString("Dock_Process", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [生产信息] 的本地化字符串。
        /// </summary>
        internal static string Dock_ProductionInfo {
            get {
                return ResourceManager.GetString("Dock_ProductionInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [模板对象] 的本地化字符串。
        /// </summary>
        internal static string Dock_TemplateObject {
            get {
                return ResourceManager.GetString("Dock_TemplateObject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [工具栏] 的本地化字符串。
        /// </summary>
        internal static string Dock_Tool {
            get {
                return ResourceManager.GetString("Dock_Tool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [视觉图像] 的本地化字符串。
        /// </summary>
        internal static string Dock_Vision {
            get {
                return ResourceManager.GetString("Dock_Vision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 编辑 的本地化字符串。
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 急停 的本地化字符串。
        /// </summary>
        internal static string Emergency {
            get {
                return ResourceManager.GetString("Emergency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 错误 的本地化字符串。
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 文件(_F) 的本地化字符串。
        /// </summary>
        internal static string File {
            get {
                return ResourceManager.GetString("File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 文件名 的本地化字符串。
        /// </summary>
        internal static string FileName {
            get {
                return ResourceManager.GetString("FileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 完成信号 的本地化字符串。
        /// </summary>
        internal static string FinishSignal {
            get {
                return ResourceManager.GetString("FinishSignal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 全局变量 的本地化字符串。
        /// </summary>
        internal static string GlobalVar {
            get {
                return ResourceManager.GetString("GlobalVar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 硬件配置 的本地化字符串。
        /// </summary>
        internal static string HardwareConfig {
            get {
                return ResourceManager.GetString("HardwareConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 帮助 的本地化字符串。
        /// </summary>
        internal static string Help {
            get {
                return ResourceManager.GetString("Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 帮助文档不存在！ 的本地化字符串。
        /// </summary>
        internal static string HelpDecomentNotFind {
            get {
                return ResourceManager.GetString("HelpDecomentNotFind", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 回零 的本地化字符串。
        /// </summary>
        internal static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 信息 的本地化字符串。
        /// </summary>
        internal static string Info {
            get {
                return ResourceManager.GetString("Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 启动输入 的本地化字符串。
        /// </summary>
        internal static string InputStart {
            get {
                return ResourceManager.GetString("InputStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 IO 的本地化字符串。
        /// </summary>
        internal static string IO {
            get {
                return ResourceManager.GetString("IO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 IO参数 的本地化字符串。
        /// </summary>
        internal static string IOParam {
            get {
                return ResourceManager.GetString("IOParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 使能检查条码 的本地化字符串。
        /// </summary>
        internal static string IsCheckBarcode {
            get {
                return ResourceManager.GetString("IsCheckBarcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 使能检查条码长度 的本地化字符串。
        /// </summary>
        internal static string IsCheckBarcodeLength {
            get {
                return ResourceManager.GetString("IsCheckBarcodeLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 使能功率报警 的本地化字符串。
        /// </summary>
        internal static string IsEnablePowerAlm {
            get {
                return ResourceManager.GetString("IsEnablePowerAlm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 旋转模式 的本地化字符串。
        /// </summary>
        internal static string IsRotateMode {
            get {
                return ResourceManager.GetString("IsRotateMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 绿灯 的本地化字符串。
        /// </summary>
        internal static string LampGreen {
            get {
                return ResourceManager.GetString("LampGreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 红灯 的本地化字符串。
        /// </summary>
        internal static string LampRed {
            get {
                return ResourceManager.GetString("LampRed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 黄灯 的本地化字符串。
        /// </summary>
        internal static string LampYellow {
            get {
                return ResourceManager.GetString("LampYellow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 语言 的本地化字符串。
        /// </summary>
        internal static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 激光器报警 的本地化字符串。
        /// </summary>
        internal static string LaserAlm {
            get {
                return ResourceManager.GetString("LaserAlm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 激光打点 的本地化字符串。
        /// </summary>
        internal static string LaserDebug {
            get {
                return ResourceManager.GetString("LaserDebug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 激光器就绪 的本地化字符串。
        /// </summary>
        internal static string LaserReady {
            get {
                return ResourceManager.GetString("LaserReady", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 激光器警告 的本地化字符串。
        /// </summary>
        internal static string LaserWarn {
            get {
                return ResourceManager.GetString("LaserWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 长度 的本地化字符串。
        /// </summary>
        internal static string Length {
            get {
                return ResourceManager.GetString("Length", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 日志 的本地化字符串。
        /// </summary>
        internal static string Log {
            get {
                return ResourceManager.GetString("Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 登录账号： 的本地化字符串。
        /// </summary>
        internal static string Login_Account {
            get {
                return ResourceManager.GetString("Login_Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 修改密码 的本地化字符串。
        /// </summary>
        internal static string Login_ChangePwd {
            get {
                return ResourceManager.GetString("Login_ChangePwd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 登 录 的本地化字符串。
        /// </summary>
        internal static string Login_Login {
            get {
                return ResourceManager.GetString("Login_Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 注 销 的本地化字符串。
        /// </summary>
        internal static string Login_Logout {
            get {
                return ResourceManager.GetString("Login_Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 登录密码： 的本地化字符串。
        /// </summary>
        internal static string Login_Password {
            get {
                return ResourceManager.GetString("Login_Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用 户 登 录 的本地化字符串。
        /// </summary>
        internal static string Login_UserLogin {
            get {
                return ResourceManager.GetString("Login_UserLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 滚到最后 的本地化字符串。
        /// </summary>
        internal static string LogView_Bottom {
            get {
                return ResourceManager.GetString("LogView_Bottom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 日志内容 的本地化字符串。
        /// </summary>
        internal static string LogView_Content {
            get {
                return ResourceManager.GetString("LogView_Content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 时间 的本地化字符串。
        /// </summary>
        internal static string LogView_Time {
            get {
                return ResourceManager.GetString("LogView_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 滚到开始 的本地化字符串。
        /// </summary>
        internal static string LogView_Top {
            get {
                return ResourceManager.GetString("LogView_Top", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 类型 的本地化字符串。
        /// </summary>
        internal static string LogView_Type {
            get {
                return ResourceManager.GetString("LogView_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加速距离(mm) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_AccDist {
            get {
                return ResourceManager.GetString("ManufactureParam_AccDist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 电流(A) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_Current {
            get {
                return ResourceManager.GetString("ManufactureParam_Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 使能加速模式 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_EnableACCMode {
            get {
                return ResourceManager.GetString("ManufactureParam_EnableACCMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 末点补偿 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_EndComp {
            get {
                return ResourceManager.GetString("ManufactureParam_EndComp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 结束延时(us) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_EndTC {
            get {
                return ResourceManager.GetString("ManufactureParam_EndTC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 频率(Hz) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_Frequency {
            get {
                return ResourceManager.GetString("ManufactureParam_Frequency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 跳转长度极限(mm) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_JumpLengthLimit {
            get {
                return ResourceManager.GetString("ManufactureParam_JumpLengthLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 跳转速度(mm/s) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_JumpSpeed {
            get {
                return ResourceManager.GetString("ManufactureParam_JumpSpeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 激光关闭延时(us) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_LaserOffTC {
            get {
                return ResourceManager.GetString("ManufactureParam_LaserOffTC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加工次数 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_MarkLoop {
            get {
                return ResourceManager.GetString("ManufactureParam_MarkLoop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 标刻速度(mm/s) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_MarkSpeed {
            get {
                return ResourceManager.GetString("ManufactureParam_MarkSpeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 最大跳转延时(us) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_MaxJumpDelayTCUs {
            get {
                return ResourceManager.GetString("ManufactureParam_MaxJumpDelayTCUs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 最小跳转延时(us) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_MinJumpDelayTCUs {
            get {
                return ResourceManager.GetString("ManufactureParam_MinJumpDelayTCUs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 笔号 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_PenID {
            get {
                return ResourceManager.GetString("ManufactureParam_PenID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打点时间(ms) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_PointTimeMs {
            get {
                return ResourceManager.GetString("ManufactureParam_PointTimeMs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 拐角延时(us) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_PolyTC {
            get {
                return ResourceManager.GetString("ManufactureParam_PolyTC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率(%) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_PowerRatio {
            get {
                return ResourceManager.GetString("ManufactureParam_PowerRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 脉冲点数 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_PulseNum {
            get {
                return ResourceManager.GetString("ManufactureParam_PulseNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 脉冲点模式 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_PulsePointMode {
            get {
                return ResourceManager.GetString("ManufactureParam_PulsePointMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Q脉冲宽度 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_QPulseWidth {
            get {
                return ResourceManager.GetString("ManufactureParam_QPulseWidth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 SPI连续模式 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_SpiContinueMode {
            get {
                return ResourceManager.GetString("ManufactureParam_SpiContinueMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 SPI波形选择 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_SpiWave {
            get {
                return ResourceManager.GetString("ManufactureParam_SpiWave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 开始延时(us) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_StartTC {
            get {
                return ResourceManager.GetString("ManufactureParam_StartTC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 抖动直径(mm) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_WobbleDiameter {
            get {
                return ResourceManager.GetString("ManufactureParam_WobbleDiameter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 抖动直径2(mm) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_WobbleDiameterB {
            get {
                return ResourceManager.GetString("ManufactureParam_WobbleDiameterB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 抖动距离(mm) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_WobbleDist {
            get {
                return ResourceManager.GetString("ManufactureParam_WobbleDist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 抖动模式 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_WobbleMode {
            get {
                return ResourceManager.GetString("ManufactureParam_WobbleMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 抖动速度(mm/s) 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_WobbleSpeed {
            get {
                return ResourceManager.GetString("ManufactureParam_WobbleSpeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 抖动类型 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_WobbleType {
            get {
                return ResourceManager.GetString("ManufactureParam_WobbleType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 YAG优化填充模式 的本地化字符串。
        /// </summary>
        internal static string ManufactureParam_YagMarkMode {
            get {
                return ResourceManager.GetString("ManufactureParam_YagMarkMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加工参数设置成功。 的本地化字符串。
        /// </summary>
        internal static string ManufactureParamSetSucceeded {
            get {
                return ResourceManager.GetString("ManufactureParamSetSucceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关于(_A) 的本地化字符串。
        /// </summary>
        internal static string Menu_About {
            get {
                return ResourceManager.GetString("Menu_About", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 相机 的本地化字符串。
        /// </summary>
        internal static string Menu_Camera {
            get {
                return ResourceManager.GetString("Menu_Camera", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 数据曲线 的本地化字符串。
        /// </summary>
        internal static string Menu_Curve {
            get {
                return ResourceManager.GetString("Menu_Curve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设备参数(_D) 的本地化字符串。
        /// </summary>
        internal static string Menu_DeviceParam {
            get {
                return ResourceManager.GetString("Menu_DeviceParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 编辑(_E) 的本地化字符串。
        /// </summary>
        internal static string Menu_Edit {
            get {
                return ResourceManager.GetString("Menu_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 帮助(_H) 的本地化字符串。
        /// </summary>
        internal static string Menu_Help {
            get {
                return ResourceManager.GetString("Menu_Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 帮助文档(_H) 的本地化字符串。
        /// </summary>
        internal static string Menu_HelpDocument {
            get {
                return ResourceManager.GetString("Menu_HelpDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 激活(_L) 的本地化字符串。
        /// </summary>
        internal static string Menu_License {
            get {
                return ResourceManager.GetString("Menu_License", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 恢复默认布局(_D) 的本地化字符串。
        /// </summary>
        internal static string Menu_LoadDefaultLayout {
            get {
                return ResourceManager.GetString("Menu_LoadDefaultLayout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加载布局(_L) 的本地化字符串。
        /// </summary>
        internal static string Menu_LoadLayout {
            get {
                return ResourceManager.GetString("Menu_LoadLayout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 日志信息 的本地化字符串。
        /// </summary>
        internal static string Menu_Log {
            get {
                return ResourceManager.GetString("Menu_Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 加工参数 的本地化字符串。
        /// </summary>
        internal static string Menu_ManufatureParam {
            get {
                return ResourceManager.GetString("Menu_ManufatureParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开(_O) 的本地化字符串。
        /// </summary>
        internal static string Menu_Open {
            get {
                return ResourceManager.GetString("Menu_Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 载入文件(_F) 的本地化字符串。
        /// </summary>
        internal static string Menu_OpenFile {
            get {
                return ResourceManager.GetString("Menu_OpenFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 参数(_P) 的本地化字符串。
        /// </summary>
        internal static string Menu_Param {
            get {
                return ResourceManager.GetString("Menu_Param", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 生产信息 的本地化字符串。
        /// </summary>
        internal static string Menu_ProductionInfo {
            get {
                return ResourceManager.GetString("Menu_ProductionInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报表查询(_R) 的本地化字符串。
        /// </summary>
        internal static string Menu_ReportQuery {
            get {
                return ResourceManager.GetString("Menu_ReportQuery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 保存(_S) 的本地化字符串。
        /// </summary>
        internal static string Menu_Save {
            get {
                return ResourceManager.GetString("Menu_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 保存文件(_L) 的本地化字符串。
        /// </summary>
        internal static string Menu_SaveFile {
            get {
                return ResourceManager.GetString("Menu_SaveFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 保存布局(_S) 的本地化字符串。
        /// </summary>
        internal static string Menu_SaveLayout {
            get {
                return ResourceManager.GetString("Menu_SaveLayout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 截屏(_C) 的本地化字符串。
        /// </summary>
        internal static string Menu_ScreenCapture {
            get {
                return ResourceManager.GetString("Menu_ScreenCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统配置(_C) 的本地化字符串。
        /// </summary>
        internal static string Menu_SystemConfig {
            get {
                return ResourceManager.GetString("Menu_SystemConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统参数(_S) 的本地化字符串。
        /// </summary>
        internal static string Menu_SystemParam {
            get {
                return ResourceManager.GetString("Menu_SystemParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 模板对象 的本地化字符串。
        /// </summary>
        internal static string Menu_TemplateObject {
            get {
                return ResourceManager.GetString("Menu_TemplateObject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 工具(_T) 的本地化字符串。
        /// </summary>
        internal static string Menu_Tools {
            get {
                return ResourceManager.GetString("Menu_Tools", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 建议及反馈(_F) 的本地化字符串。
        /// </summary>
        internal static string Menu_UserFeedback {
            get {
                return ResourceManager.GetString("Menu_UserFeedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 视图(_V) 的本地化字符串。
        /// </summary>
        internal static string Menu_View {
            get {
                return ResourceManager.GetString("Menu_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 轴卡设置 的本地化字符串。
        /// </summary>
        internal static string MotionSet {
            get {
                return ResourceManager.GetString("MotionSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 新建方案 的本地化字符串。
        /// </summary>
        internal static string NewSolution {
            get {
                return ResourceManager.GetString("NewSolution", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 对象名 的本地化字符串。
        /// </summary>
        internal static string ObjectName {
            get {
                return ResourceManager.GetString("ObjectName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开 的本地化字符串。
        /// </summary>
        internal static string Open {
            get {
                return ResourceManager.GetString("Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 打开文档 的本地化字符串。
        /// </summary>
        internal static string OpenFile {
            get {
                return ResourceManager.GetString("OpenFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 操作员 的本地化字符串。
        /// </summary>
        internal static string Operator {
            get {
                return ResourceManager.GetString("Operator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 操作员登录系统。 的本地化字符串。
        /// </summary>
        internal static string OperatorLoginSystem {
            get {
                return ResourceManager.GetString("OperatorLoginSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 其它 的本地化字符串。
        /// </summary>
        internal static string Other {
            get {
                return ResourceManager.GetString("Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 完成输出 的本地化字符串。
        /// </summary>
        internal static string OutputFinish {
            get {
                return ResourceManager.GetString("OutputFinish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 软件就绪 的本地化字符串。
        /// </summary>
        internal static string PC_Ready {
            get {
                return ResourceManager.GetString("PC_Ready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请先登录! 的本地化字符串。
        /// </summary>
        internal static string PleaseLoginFirst {
            get {
                return ResourceManager.GetString("PleaseLoginFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 位置 的本地化字符串。
        /// </summary>
        internal static string Position {
            get {
                return ResourceManager.GetString("Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率(W) 的本地化字符串。
        /// </summary>
        internal static string Power {
            get {
                return ResourceManager.GetString("Power", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率下限 的本地化字符串。
        /// </summary>
        internal static string PowerDownLimit {
            get {
                return ResourceManager.GetString("PowerDownLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率参数 的本地化字符串。
        /// </summary>
        internal static string PowerParam {
            get {
                return ResourceManager.GetString("PowerParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 当前功率 的本地化字符串。
        /// </summary>
        internal static string PowerPV {
            get {
                return ResourceManager.GetString("PowerPV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率上限 的本地化字符串。
        /// </summary>
        internal static string PowerUpLimit {
            get {
                return ResourceManager.GetString("PowerUpLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 产能统计 的本地化字符串。
        /// </summary>
        internal static string ProductionInfo_CapacityOfStatistical {
            get {
                return ResourceManager.GetString("ProductionInfo_CapacityOfStatistical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 不合格数量 的本地化字符串。
        /// </summary>
        internal static string ProductionInfo_NGNum {
            get {
                return ResourceManager.GetString("ProductionInfo_NGNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 合格数量 的本地化字符串。
        /// </summary>
        internal static string ProductionInfo_OKNum {
            get {
                return ResourceManager.GetString("ProductionInfo_OKNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 总数 的本地化字符串。
        /// </summary>
        internal static string ProductionInfo_TotalNum {
            get {
                return ResourceManager.GetString("ProductionInfo_TotalNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 焊接时间 的本地化字符串。
        /// </summary>
        internal static string ProductionInfo_WeldTime {
            get {
                return ResourceManager.GetString("ProductionInfo_WeldTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 良率 的本地化字符串。
        /// </summary>
        internal static string ProductionInfo_Yield {
            get {
                return ResourceManager.GetString("ProductionInfo_Yield", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 项目信息设置 的本地化字符串。
        /// </summary>
        internal static string ProjectInfoSet {
            get {
                return ResourceManager.GetString("ProjectInfoSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 急速模式 的本地化字符串。
        /// </summary>
        internal static string QuickMode {
            get {
                return ResourceManager.GetString("QuickMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 退出 的本地化字符串。
        /// </summary>
        internal static string Quit {
            get {
                return ResourceManager.GetString("Quit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确认退出系统吗？ 的本地化字符串。
        /// </summary>
        internal static string QuitSystem {
            get {
                return ResourceManager.GetString("QuitSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 配方 的本地化字符串。
        /// </summary>
        internal static string Recipe {
            get {
                return ResourceManager.GetString("Recipe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 红光 的本地化字符串。
        /// </summary>
        internal static string RedLight {
            get {
                return ResourceManager.GetString("RedLight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 移除 的本地化字符串。
        /// </summary>
        internal static string Remove {
            get {
                return ResourceManager.GetString("Remove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 全部移除 的本地化字符串。
        /// </summary>
        internal static string RemoveAll {
            get {
                return ResourceManager.GetString("RemoveAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 报表查询 的本地化字符串。
        /// </summary>
        internal static string ReportQuery {
            get {
                return ResourceManager.GetString("ReportQuery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 复位按钮 的本地化字符串。
        /// </summary>
        internal static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 不合格信号 的本地化字符串。
        /// </summary>
        internal static string Result_NG {
            get {
                return ResourceManager.GetString("Result_NG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 合格信号 的本地化字符串。
        /// </summary>
        internal static string Result_OK {
            get {
                return ResourceManager.GetString("Result_OK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 循环运行 的本地化字符串。
        /// </summary>
        internal static string RunCycle {
            get {
                return ResourceManager.GetString("RunCycle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 运行一次 的本地化字符串。
        /// </summary>
        internal static string RunOnce {
            get {
                return ResourceManager.GetString("RunOnce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 保存 的本地化字符串。
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 保存文档 的本地化字符串。
        /// </summary>
        internal static string SaveFile {
            get {
                return ResourceManager.GetString("SaveFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 截屏 的本地化字符串。
        /// </summary>
        internal static string ScreenCapture {
            get {
                return ResourceManager.GetString("ScreenCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 查看 帮助 的本地化字符串。
        /// </summary>
        internal static string SearchHelp {
            get {
                return ResourceManager.GetString("SearchHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 伺服 的本地化字符串。
        /// </summary>
        internal static string ServoDebug {
            get {
                return ResourceManager.GetString("ServoDebug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置 的本地化字符串。
        /// </summary>
        internal static string Set {
            get {
                return ResourceManager.GetString("Set", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 设置 的本地化字符串。
        /// </summary>
        internal static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 尺寸 的本地化字符串。
        /// </summary>
        internal static string Size {
            get {
                return ResourceManager.GetString("Size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 软件正在退出中，请稍等！ 的本地化字符串。
        /// </summary>
        internal static string SoftwareIsExiting {
            get {
                return ResourceManager.GetString("SoftwareIsExiting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 软件启动成功。 的本地化字符串。
        /// </summary>
        internal static string SoftwareStartupSucceeded {
            get {
                return ResourceManager.GetString("SoftwareStartupSucceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 启动时自动加载解决方案 的本地化字符串。
        /// </summary>
        internal static string SoluctionAutoLoad {
            get {
                return ResourceManager.GetString("SoluctionAutoLoad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 启动后自动开始执行 的本地化字符串。
        /// </summary>
        internal static string SoluctionAutoRun {
            get {
                return ResourceManager.GetString("SoluctionAutoRun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 项目路径： 的本地化字符串。
        /// </summary>
        internal static string SoluctionPath {
            get {
                return ResourceManager.GetString("SoluctionPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 方案列表 的本地化字符串。
        /// </summary>
        internal static string SolutionList {
            get {
                return ResourceManager.GetString("SolutionList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 启动 的本地化字符串。
        /// </summary>
        internal static string Start {
            get {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 自启动设置 的本地化字符串。
        /// </summary>
        internal static string StartupSetting {
            get {
                return ResourceManager.GetString("StartupSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 停止 的本地化字符串。
        /// </summary>
        internal static string Stop {
            get {
                return ResourceManager.GetString("Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统 的本地化字符串。
        /// </summary>
        internal static string System {
            get {
                return ResourceManager.GetString("System", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统配置 的本地化字符串。
        /// </summary>
        internal static string SystemConfig {
            get {
                return ResourceManager.GetString("SystemConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 软件启动时自动加载已保存的布局 的本地化字符串。
        /// </summary>
        internal static string SystemParam_AutoLoadLayout {
            get {
                return ResourceManager.GetString("SystemParam_AutoLoadLayout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 基础 的本地化字符串。
        /// </summary>
        internal static string SystemParam_Basis {
            get {
                return ResourceManager.GetString("SystemParam_Basis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 公司名称 的本地化字符串。
        /// </summary>
        internal static string SystemParam_CompanyName {
            get {
                return ResourceManager.GetString("SystemParam_CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 语言 的本地化字符串。
        /// </summary>
        internal static string SystemParam_Language {
            get {
                return ResourceManager.GetString("SystemParam_Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 路径... 的本地化字符串。
        /// </summary>
        internal static string SystemParam_Path {
            get {
                return ResourceManager.GetString("SystemParam_Path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 项目名称 的本地化字符串。
        /// </summary>
        internal static string SystemParam_ProjectName {
            get {
                return ResourceManager.GetString("SystemParam_ProjectName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 软件图标 的本地化字符串。
        /// </summary>
        internal static string SystemParam_SoftwareIcon {
            get {
                return ResourceManager.GetString("SystemParam_SoftwareIcon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 软件自启动 的本地化字符串。
        /// </summary>
        internal static string SystemParam_SoftwareSelfStarting {
            get {
                return ResourceManager.GetString("SystemParam_SoftwareSelfStarting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 软件版本 的本地化字符串。
        /// </summary>
        internal static string SystemParam_SoftwareVersion {
            get {
                return ResourceManager.GetString("SystemParam_SoftwareVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统参数 的本地化字符串。
        /// </summary>
        internal static string SystemParam_SystemParam {
            get {
                return ResourceManager.GetString("SystemParam_SystemParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 时间 的本地化字符串。
        /// </summary>
        internal static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 双手启动1 的本地化字符串。
        /// </summary>
        internal static string TwoHandsStart1 {
            get {
                return ResourceManager.GetString("TwoHandsStart1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 双手启动2 的本地化字符串。
        /// </summary>
        internal static string TwoHandsStart2 {
            get {
                return ResourceManager.GetString("TwoHandsStart2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 UI设计器 的本地化字符串。
        /// </summary>
        internal static string UIDesign {
            get {
                return ResourceManager.GetString("UIDesign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 超限报警设置 的本地化字符串。
        /// </summary>
        internal static string UltralimitSetting {
            get {
                return ResourceManager.GetString("UltralimitSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 超限检测时间 的本地化字符串。
        /// </summary>
        internal static string UltralimitTime {
            get {
                return ResourceManager.GetString("UltralimitTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 塌陷(mm) 的本地化字符串。
        /// </summary>
        internal static string UltralimitView_Collapse {
            get {
                return ResourceManager.GetString("UltralimitView_Collapse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率 的本地化字符串。
        /// </summary>
        internal static string UltralimitView_HeaderPower {
            get {
                return ResourceManager.GetString("UltralimitView_HeaderPower", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 功率(W) 的本地化字符串。
        /// </summary>
        internal static string UltralimitView_Power {
            get {
                return ResourceManager.GetString("UltralimitView_Power", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 压力(N) 的本地化字符串。
        /// </summary>
        internal static string UltralimitView_Pressure {
            get {
                return ResourceManager.GetString("UltralimitView_Pressure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 温度(℃) 的本地化字符串。
        /// </summary>
        internal static string UltralimitView_Temperature {
            get {
                return ResourceManager.GetString("UltralimitView_Temperature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 时间(ms) 的本地化字符串。
        /// </summary>
        internal static string UltralimitView_Time {
            get {
                return ResourceManager.GetString("UltralimitView_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用户登陆 的本地化字符串。
        /// </summary>
        internal static string UserLogin {
            get {
                return ResourceManager.GetString("UserLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 警告 的本地化字符串。
        /// </summary>
        internal static string Warn {
            get {
                return ResourceManager.GetString("Warn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 水箱报警 的本地化字符串。
        /// </summary>
        internal static string WaterTankAlm {
            get {
                return ResourceManager.GetString("WaterTankAlm", resourceCulture);
            }
        }
    }
}
