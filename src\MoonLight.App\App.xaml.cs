﻿using System;
using System.Collections.Generic;
using System.Configuration.Assemblies;
using System.IO;
using System.Reflection;
using System.Windows;
using MoonLight.App.Views;
using MoonLight.Core.Common.Helper;
using MoonLight.Core.Services;
using MoonLight.UI.Modules.Shell;

namespace MoonLight.Test
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private static readonly Dictionary<string, Assembly> _loadedAssemblies = new Dictionary<string, Assembly>();

        public App()
        {
            // 注册 AssemblyResolve 事件
            AppDomain.CurrentDomain.AssemblyResolve += CurrentDomain_AssemblyResolve;
            GlobalVariable.CurView = new TestView();
            
        }

        private static Assembly CurrentDomain_AssemblyResolve(object sender, ResolveEventArgs args)
        {
            string assemblyName = new AssemblyName(args.Name).Name;

            // 检查缓存中是否已加载
            if (_loadedAssemblies.TryGetValue(assemblyName, out Assembly loadedAssembly))
            {
                return loadedAssembly;
            }

            // 是不是当前文件夹下的程序集
            string basePath = FilePaths.ThirdPartyLibsPath;
            string path = Path.Combine(basePath, assemblyName + ".dll");
            if(File.Exists(path))
            {
                // 加载程序集并缓存
                var assembly = Assembly.LoadFrom(path);
                _loadedAssemblies[assemblyName] = assembly;
                return assembly;
            }


            // 遍历所有子文件夹
            foreach (string directory in Directory.GetDirectories(basePath))
            {
                string assemblyPath = Path.Combine(directory, assemblyName + ".dll");

                if (File.Exists(assemblyPath))
                {
                    // 加载程序集并缓存
                    var assembly = Assembly.LoadFrom(assemblyPath);
                    _loadedAssemblies[assemblyName] = assembly;
                    return assembly;
                }
            }

            //判断是否是插件库
            string pluginPath = Path.Combine(FilePaths.PluginsPath, assemblyName + ".dll");
            if(File.Exists(pluginPath))
            {
                // 加载程序集并缓存
                var assembly = Assembly.LoadFrom(pluginPath);
                _loadedAssemblies[assemblyName] = assembly;
                return assembly;
            }


            // 如果未找到程序集，返回 null
            return null;
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            //初始化插件
            PluginService.InitPlugin();
            
        }
    }
}
