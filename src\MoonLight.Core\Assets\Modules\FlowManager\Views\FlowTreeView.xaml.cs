﻿using MoonLight.Core.Common.Helper;
using MoonLight.Core.Enums;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Core.Services;
using MoonLight.Core.Views;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.Composition;
using System.Text.RegularExpressions;
using System.Windows.Automation.Peers;
using System.Windows;
using System;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Linq;
using MoonLight.Core.Extension;
using EventMgrLib;

namespace MoonLight.Modules.FlowManager.Views
{
    /// <summary>
    /// FlowTreeView.xaml 的交互逻辑
    /// </summary>
    [Export]
    public partial class FlowTreeView : UserControl, IFlow
    {
        public FlowTreeView()
        {
            InitializeComponent();
            Solution.SolutionChangedEvent += Ins_SolutionChangedEvent;
        }

        private void Ins_SolutionChangedEvent(object sender, EventArgs e)
        {
            processTree.ItemsSource = null;
            Solution.Ins.CurrentProject = null;
            EventMgr.Ins.GetEvent<ProjectChangedEvent>().Publish();
        }

        private Dictionary<string, bool> NodesStatusDic = new Dictionary<string, bool>(); //用于保存是否展开的状态 用key作为容器, 刷新前清除容器, 需要保证键值唯一
        public List<TreeNode> ProcessNodeList = new List<TreeNode>();//treeview下 所有的toolNode 
        public List<TreeNode> TreeSoureList { get; set; } = new List<TreeNode>();//treeview下 绑定的源数据

        private Cursor m_DragCursor;//拖拽时候的光标
        private string m_DragProcessName;//移动位置的时候 模块名称/
        private bool m_DragMoveFlag;//移动标志
        private double m_MousePressY;//鼠标点下时的y坐标
        private double m_MousePressX;//鼠标点下时的X坐标

        private string MultiSelectedStart { get; set; }//多选下开始的模块名称
        private string MultiSelectedEnd { get; set; }//多选下结束的模块名称
        private int MultiSelectedCount { get; set; }//多选模块总数
        public List<string> SelectedProcessNameList { get; set; } = new List<string>();// 连续选中模式下 选择的tool

        //之前选中的FlowNode
        public TreeNode SelectedNode { get; set; }

        public ProjectFlowTree SelectedProject { get; set; }


        #region 顶部工具栏方法
        private void btnCreateProcess_Click(object sender, RoutedEventArgs e)
        {
            ProjectFlowTree project = new ProjectFlowTree();

            Solution.Ins.CurrentProjectID = Solution.Ins.CreateProject(ProjectType.Process, project);
            Solution.Ins.CurrentProject = Solution.Ins.GetProjectById(Solution.Ins.CurrentProjectID);
            UpdateTree("LastNode");
        }

        private void btnDeleteProcess_Click(object sender, RoutedEventArgs e)
        {
            SelectedProject = processTree.SelectedItem as ProjectFlowTree;
            if (SelectedProject == null) return;

            MessageBoxResult res = MessageBox.Show($"确认删除流程吗?", "提示", MessageBoxButton.OKCancel, MessageBoxImage.Information);
            if (res == MessageBoxResult.OK)
            {
                var item = Solution.Ins.ProjectList.Where(o => o.ProjectInfo.ProcessName == SelectedProject.ProjectInfo.ProcessName).FirstOrDefault();
                if (item == null) return;
                Solution.Ins.ProjectList.Remove(item);
                if (Solution.Ins.ProjectList.Count > 0)
                {
                    Solution.Ins.CurrentProjectID = Solution.Ins.ProjectList[0].ProjectInfo.ProjectID;
                    Solution.Ins.CurrentProject = Solution.Ins.GetProjectById(Solution.Ins.CurrentProjectID);
                    if (Solution.Ins.CurrentProject == null)
                    {
                        processTree.ItemsSource = null;
                        IoC.Get<IProcess>().SetTreeSourceNull();
                        return;
                    }
                }
                else
                {
                    Solution.Ins.CurrentProjectID = -1;
                    Solution.Ins.CurrentProject = null;
                }
                UpdateTree("LastNode");
                IoC.Get<IProcess>().UpdateTree();
            }
        }

        private void btnCreateMethod_Click(object sender, RoutedEventArgs e)
        {

            ProjectFlowTree project = new ProjectFlowTree();

            Solution.Ins.CurrentProjectID = Solution.Ins.CreateProject(ProjectType.Method, project);
            Solution.Ins.CurrentProject = Solution.Ins.GetProjectById(Solution.Ins.CurrentProjectID);
            UpdateTree("LastNode");
        }

        /// <summary>
        /// 获取结构树的展开状态
        /// </summary>
        /// <param name="nodes"></param>
        private void GetTreeNodesStatus(ItemsControl control)
        {
            if (control != null)
            {
                foreach (object item in control.Items)
                {
                    TreeViewItem treeItem = control.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;

                    if (treeItem != null && treeItem.HasItems)
                    {
                        ProjectFlowTree toolNode = treeItem.DataContext as ProjectFlowTree;
                        NodesStatusDic[toolNode.ProjectInfo.ProcessName] = treeItem.IsExpanded;

                        GetTreeNodesStatus(treeItem as ItemsControl);
                    }
                }
            }
        }



        #endregion

        #region 流程拖拽功能
        //拖拽丢下数据
        private void processTree_Drop(object sender, DragEventArgs e)
        {
            DragDropModel model = e.Data.GetData("MoonLight.Core.Models.DragDropModel") as DragDropModel;
            if (model == null || model.SourceName != "processTree") return;
            if (SelectedProject != null)// 恢复之前的下划线
            {
                SelectedProject.DragOverHeight = 1;
            }
            if (e.AllowedEffects == DragDropEffects.Move)//表示移动位置
            {
                if (model.Name != null && SelectedProject != null)
                {
                    string processStartName = model.Name;
                    if (processStartName != SelectedProject.ProjectInfo.ProcessName)//自己不能移动到自己下面
                    {
                        if (IsMultiSelectedModel() == true)
                        {
                            ChangeProcessPos(MultiSelectedStart, MultiSelectedEnd, SelectedProject.ProjectInfo.ProcessName, true);
                        }
                        else
                        {
                            ChangeProcessPos(processStartName, processStartName, SelectedProject.ProjectInfo.ProcessName, true);
                        }
                    }
                }
            }
        }

        private void processTree_PreviewMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            //获取鼠标位置的TreeViewItem 然后选中
            Point pt = e.GetPosition(processTree);
            HitTestResult result = VisualTreeHelper.HitTest(processTree, pt);
            if (result == null) return;
            TreeViewItem selectedItem = WPFElementTool.FindVisualParent<TreeViewItem>(result.VisualHit);

            if (selectedItem != null)
            {
                selectedItem.Focus();
            }
        }

        private void processTree_PreviewMouseUp(object sender, MouseButtonEventArgs e)
        {
            m_DragMoveFlag = false;
        }


        //拖拽的时候 鼠标移动
        private void processTree_DragOver(object sender, DragEventArgs e)
        {
            //获取鼠标位置的TreeViewItem 然后选中
            Point pt = e.GetPosition(processTree);
            HitTestResult result = VisualTreeHelper.HitTest(processTree, pt);
            if (result == null) return;
            TreeViewItem selectedItem = WPFElementTool.FindVisualParent<TreeViewItem>(result.VisualHit);

            if (selectedItem != null)
            {
                selectedItem.IsSelected = true;
                ProjectFlowTree node = selectedItem.DataContext as ProjectFlowTree;

                if (SelectedProject != null)
                {
                    if (SelectedProject.ProjectInfo.ProcessName != node.ProjectInfo.ProcessName)//名称不一样说明更换了tool  恢复之前的下划线
                    {
                        SelectedProject.DragOverHeight = 1;
                    }
                }
                SelectedProject = node;
                SelectedProject.DragOverHeight = 5;//划过的时候高度变为2

            }

            //获取treeview本身的 ScrollViewer
            TreeViewAutomationPeer lvap = new TreeViewAutomationPeer(processTree);
            ScrollViewerAutomationPeer svap = lvap.GetPattern(PatternInterface.Scroll) as ScrollViewerAutomationPeer;
            ScrollViewer scroll = svap.Owner as ScrollViewer;

            pt = e.GetPosition(processTree);

            if (processTree.ActualHeight - pt.Y <= 50)
            {
                scroll.ScrollToVerticalOffset(scroll.VerticalOffset + 10);
            }
            if (Math.Abs(pt.Y) <= 50)
            {
                scroll.ScrollToVerticalOffset(scroll.VerticalOffset - 10);
            }
        }

        //拖拽的时候 离开区域
        private void processTree_DragLeave(object sender, DragEventArgs e)
        {
            if (SelectedProject != null)
            {
                SelectedProject.DragOverHeight = 1; // 恢复之前的下划线
            }
        }

        private void processTree_MouseMove(object sender, MouseEventArgs e)
        {
            if (m_DragMoveFlag == true)
            {
                Point pt = e.GetPosition(processTree);
                if (Math.Abs(pt.Y - m_MousePressY) > 10 || Math.Abs(pt.X - m_MousePressX) > 10)//在y方向差异10像素 才开始拖动
                {
                    string showText = "";
                    int width = 0;
                    if (IsMultiSelectedModel() == true)
                    {
                        showText = $"[{MultiSelectedStart}] ~ [{MultiSelectedEnd}]";
                        width = 400;
                    }
                    else
                    {
                        width = 200;
                        showText = SelectedProject.ProjectInfo.ProcessName;
                    }
                    m_DragCursor = WPFCursorTool.CreateCursor(width, 30, 13, ImageTool.ImageSourceToBitmap(SelectedProject.ProjectInfo.IconImage), 32, showText);
                    m_DragMoveFlag = false;
                    DragDropModel data = new DragDropModel() { Name = m_DragProcessName, SourceName = "processTree" };
                    DragDrop.DoDragDrop(processTree, data, DragDropEffects.Move);
                }
            }
        }

        //拖拽的时候鼠标样式
        private void processTree_GiveFeedback(object sender, GiveFeedbackEventArgs e)
        {
            e.UseDefaultCursors = false;
            Mouse.SetCursor(m_DragCursor);
            e.Handled = true;
        }

        //按键事件
        private void processTree_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyboardDevice.Modifiers == ModifierKeys.Shift)
            {
                ////只按下了shift 则开始记录是从那里开始连续选中
                //if (SelectedProject != null && !SelectedProcessNameList.Contains(SelectedProject.ProjectInfo.ProcessName))
                //{
                //    SelectedProcessNameList.Add(SelectedProject.ProjectInfo.ProcessName);
                //}
            }
            else if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.A)
            {
                foreach (ProjectFlowTree project in Solution.Ins.ProjectList)
                {
                    project.IsMultiSelected = true;
                    //if (project.lis.Count > 0)
                    //{
                    //    //如果当前模块含有子类,则选中所有子类
                    //    MultiSelectToolNode(tool);
                    //}
                }
            }
            //else if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.C)
            //{
            //    miCopy_Click(null, null);
            //}
            //else if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.V)
            //{
            //    miPaste_Click(null, null);
            //}
        }

        private void processTree_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (processTree.Items.Count == 0)
            {
                processTree.Focus();//在没有任何元素的时候 需要这几句来获得焦点
                return;
            }

            //获取鼠标位置的TreeViewItem 然后选中
            Point pt = e.GetPosition(processTree);
            HitTestResult result = VisualTreeHelper.HitTest(processTree, pt);
            if (result == null) return;

            TreeViewItem selectedItem = WPFElementTool.FindVisualParent<TreeViewItem>(result.VisualHit);
            if (selectedItem != null)
            {
                SelectedProject = selectedItem.DataContext as ProjectFlowTree;
                selectedItem.IsSelected = true;
            }

            if (Keyboard.Modifiers == ModifierKeys.Shift)//按住shift 多选
            {
                MultiSelect();
                e.Handled = true;
                return;
            }

            //靠近滚轮则不执行拖动
            if (processTree.ActualWidth - pt.X > 80)
            {

                if (SelectedProject != null /*&& SelectedProject.IsCategory == false*/)
                {
                    m_MousePressY = pt.Y;
                    m_MousePressX = pt.X;
                    m_DragProcessName = SelectedProject.ProjectInfo.ProcessName;
                    m_DragMoveFlag = true;
                }
            }


        }

        //鼠标左键弹起
        private void processTree_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (IsMultiSelectedModel() == true && Keyboard.Modifiers != ModifierKeys.Shift)//鼠标弹起 多选模式 取消显示
            {
                CancelMultiSelect();
            }
        }
        private void ChangeProcessPos(string processStartName, string processEndName, string relativeProcessName, bool isNext)
        {
            if (processStartName == relativeProcessName)
            {
                return;//名称相同则不修改
            }

            List<string> processNameList = Solution.Ins.ProjectList.Select(c => c.ProjectInfo.ProcessName).ToList();

            if (processStartName != processEndName)
            {
                List<string> tempList = Solution.Ins.ProjectList.Select(c => c.ProjectInfo.ProcessName).ToList();//必须先准备一个副本 不能在foreach里删除自己的元素,会导致跌倒器更新错位

                int startIndex = processNameList.IndexOf(processStartName);
                int endIndex = processNameList.IndexOf(processEndName);

                for (int i = startIndex; i < endIndex + 1; i++)
                {
                    processNameList.Remove(tempList[i]);  //先删除
                    int index = processNameList.IndexOf(relativeProcessName);
                    processNameList.Insert(index + 1, tempList[i]);//插入
                    relativeProcessName = tempList[i];
                }
            }
            else
            {
                if (!processStartName.StartsWith("文件夹"))
                {
                    //先删除
                    processNameList.Remove(processStartName);

                    //获取定位模块的位置
                    int index = processNameList.IndexOf(relativeProcessName);

                    if (index == -1 && isNext == true)//添加在首
                    {
                        processNameList.Insert(0, processStartName);
                    }
                    else if (index == -1 && isNext == false)//添加在末尾
                    {
                        processNameList.Add(processStartName);
                    }
                    else if (index != -1 && isNext == true)//插在后面
                    {
                        processNameList.Insert(index + 1, processStartName);
                    }
                    else if (index != -1 && isNext == false)//插在前面
                    {
                        processNameList.Insert(index, processStartName);
                    }
                }
                //else if (Regex.IsMatch(processStartName, "文件夹[0-9]*$"))
                //{
                //    List<string> brotherList;
                //    if (ProcessNodeList.FirstOrDefault(c => c.Name == processStartName).ParentToolNode != null)
                //    {
                //        //获取同级别的下一个结束
                //        brotherList = ProcessNodeList.FirstOrDefault(c => c.Name == processStartName)
                //             .ParentToolNode.Children.Select(c => c.Name).ToList();
                //    }
                //    else
                //    {
                //        brotherList = TreeSoureList.Select(c => c.Name).ToList();
                //    }

                //    int curIndex = brotherList.IndexOf(processStartName);//当前模块的位置

                //    string endToolName = "";
                //    // 在同级模块查找结束模块
                //    for (int i = curIndex + 1; i < brotherList.Count(); i++)
                //    {
                //        string endToolStartName = "";
                //        if (Regex.IsMatch(processStartName, "文件夹[0-9]*$"))
                //        {
                //            endToolStartName = "文件夹结束";
                //        }
                //        if (brotherList[i].StartsWith(endToolStartName))
                //        {
                //            endToolName = brotherList[i];
                //            break;
                //        }
                //    }

                //    curIndex = processNameList.IndexOf(processStartName);//当前模块的位置
                //    int endIndex = processNameList.IndexOf(endToolName);//结束的位置

                //    List<string> tempList = CloneObject.DeepCopy<List<string>>(processNameList);//必须先准备一个副本 不能在foreach里删除自己的元素,会导致跌倒器更新错位

                //    //获取定位模块的位置
                //    for (int i = curIndex; i < endIndex + 1; i++)
                //    {
                //        processNameList.Remove(tempList[i]);  //先删除
                //        int index = processNameList.IndexOf(relativeProcessName);
                //        processNameList.Insert(index + 1, tempList[i]);//插入
                //        relativeProcessName = tempList[i];
                //    }
                //}
            }


            //根据新的toolnameList 重新调整ToolInfoList
            ObservableCollection<Project> tempToolInfoList = new ObservableCollection<Project>();

            foreach (string toolName in processNameList)
            {
                tempToolInfoList.Add(Solution.Ins.ProjectList.FirstOrDefault(c => c.ProjectInfo.ProcessName == toolName));
            }

            Solution.Ins.ProjectList = tempToolInfoList;

            UpdateTree(processStartName);
        }
        /// <summary>
        /// 添加一个模块
        /// </summary>
        /// <param name="curToolName">要追加的模块目标位置模块名称</param>
        /// <param name="info">模块信息</param>
        /// <param name="isNext">是否在后方追加</param>
        public void UpdateTree(string selectedNoteName = "")
        {
            //ProcessNodeList.Clear();
            NodesStatusDic.Clear();
            GetTreeNodesStatus(processTree);//保存展开节点信息

            List<Project> projectDic = Solution.Ins.ProjectList.ToList();//模块信息

            //将父节点放入栈容器 
            Stack<ProjectFlowTree> s_ParentItemStack = new Stack<ProjectFlowTree>();
            //TreeSoureList.Clear();
            for (int i = 0; i < projectDic.Count; i++)
            {
                ProjectFlowTree project = (ProjectFlowTree)projectDic[i];
                if (project == null) return;
                //TreeNode nodeItem = new TreeNode(project);
                //ProcessNodeList.Add(nodeItem);

                if (i == 0) project.IsFirstNode = true;

                if (project.ProjectInfo.ProjectType == ProjectType.Process)
                {
                    if (s_ParentItemStack.Count > 0)
                    {
                        s_ParentItemStack.Pop();
                    }
                }

                //~~~~~~~~~~~~~~~
                if (s_ParentItemStack.Count > 0)
                {
                    project.ProjectInfo.Hierarchy = s_ParentItemStack.Count;//层级
                    s_ParentItemStack.Peek();
                }
                else
                {
                    project.ProjectInfo.Hierarchy = 0;

                    //TreeSoureList.Add(nodeItem);    //根目录
                }
                //判断当前节点是否是父节点开始
                if (project.ProjectInfo.ProjectType == ProjectType.Process)//Regex.IsMatch(project.ProjectInfo.ProjectName, "文件夹[0-9]*$"))
                {
                    s_ParentItemStack.Push(project);
                }

                //最后一个node如果层级大于0 则需要补划最后一条横线
                if (i == projectDic.Count - 1 && project.ProjectInfo.Hierarchy > 0)
                {
                    project.LastNodeMargin = $"{project.ProjectInfo.Hierarchy * -14},0,0,0";
                }
            }
            SelectNode(selectedNoteName);
        }

        //是否是在多选模式下
        private bool IsMultiSelectedModel()
        {
            foreach (ProjectFlowTree toolNode in Solution.Ins.ProjectList)
            {
                if (toolNode.IsMultiSelected == true)
                {
                    return toolNode.IsMultiSelected;
                }
            }

            return false;
        }
        //多选
        private void MultiSelect()
        {
            if (SelectedProject == null) return;

            SelectedProcessNameList.Add(SelectedProject.ProjectInfo.ProcessName);

            //获取多选的tool的index
            Dictionary<int, string> dic = new Dictionary<int, string>();
            foreach (string toolName in SelectedProcessNameList)
            {
                dic[Solution.Ins.ProjectList.FindIndex(c => c.ProjectInfo.ProcessName == toolName)] = toolName;
            }

            //从小到大全部选中
            foreach (ProjectFlowTree toolNode in Solution.Ins.ProjectList)
            {
                int index = Solution.Ins.ProjectList.FindIndex(c => c.ProjectInfo.ProcessName == toolNode.ProjectInfo.ProcessName);
                if (index >= dic.Keys.Min() && index <= dic.Keys.Max())
                {
                    if (toolNode.ProjectInfo.ProcessName.Contains("否则"))
                    {
                        string startName = "";//查找否则 否则如果的 起始模块名称
                        string endName = "";
                        // NativeFun.GetStartEndToolNameByElse(this. ProjectInfo.ProjectID, toolNode.ToolInfo.ToolName, out startName, out endName);
                        SelectedProcessNameList.Add(startName);
                        SelectedProcessNameList.Add(endName);

                    }
                    else
                    {
                        string endToolName = "";   //获得其结束模块
                        //  NativeFun.GetEndToolNameByStartName(toolNode.ToolInfo.ToolName, out endToolName);
                        //这里将其修改为判断
                        if (Regex.IsMatch(toolNode.ProjectInfo.ProcessName, "文件夹[0-9]*$"))
                        {
                            endToolName = toolNode.ProjectInfo.ProcessName.Replace("文件夹", "文件夹结束");
                        }//还可以自己添加其他的

                        if (endToolName != "")
                        {
                            SelectedProcessNameList.Add(endToolName);
                        }

                        string startToolName = "";//获得开始模块
                        //   NativeFun.GetStartToolNameByEndName(toolNode.ToolInfo.ToolName, out startToolName);
                        if (Regex.IsMatch(toolNode.ProjectInfo.ProcessName, "文件夹结束[0-9]*$"))
                        {
                            startToolName = toolNode.ProjectInfo.ProcessName.Replace("文件夹结束", "文件夹");
                        }//还可以自己添加其他的

                        if (startToolName != "")
                        {
                            SelectedProcessNameList.Add(startToolName);
                        }
                    }

                }
            }

            //重新计算选择的范围
            foreach (string toolName in SelectedProcessNameList)
            {
                dic[Solution.Ins.ProjectList.FindIndex(c => c.ProjectInfo.ProcessName == toolName)] = toolName;
            }

            MultiSelectedStart = dic[dic.Keys.Min()];
            MultiSelectedEnd = dic[dic.Keys.Max()];
            MultiSelectedCount = dic.Keys.Max() - dic.Keys.Min() + 1;
            //将结束模块也加入
            foreach (ProjectFlowTree toolNode in Solution.Ins.ProjectList)
            {
                int index = Solution.Ins.ProjectList.FindIndex(c => c.ProjectInfo.ProcessName == toolNode.ProjectInfo.ProcessName);
                if (index >= dic.Keys.Min() && index <= dic.Keys.Max())
                {
                    toolNode.IsMultiSelected = true;

                    //if (toolNode.Children.Count > 0)
                    //{
                    //    //如果当前模块含有子类,则选中所有子类
                    //    MultiSelectToolNode(toolNode);
                    //}
                }
            }
        }

        //取消多选样式
        public void CancelMultiSelect()
        {
            //点击的时候取消 多重选择效果
            foreach (ProjectFlowTree item in Solution.Ins.ProjectList)
            {
                item.IsMultiSelected = false;
            }
            SelectedProcessNameList.Clear();
            MultiSelectedCount = 0;
        }

        /// <summary>
        /// 遍历 获取当前ToolNode下所有的子类,并设为multiselected=true
        /// </summary>
        /// <param name="nodes"></param>
        private void MultiSelectToolNode(TreeNode toolNode)
        {
            if (toolNode != null)
            {
                //foreach (TreeNode item in toolNode.Children)
                //{
                //    item.IsMultiSelected = true;

                //    if (item.Children.Count > 0)
                //    {
                //        MultiSelectToolNode(item);
                //    }
                //}
            }
        }
        /// <summary>
        /// 选中指定名字的node
        /// </summary>
        /// <param name="nodes"></param>
        private void SelectNode(string name)
        {
            switch (name)
            {
                case "LastNode":
                    foreach (ProjectFlowTree item in Solution.Ins.ProjectList)
                    {
                        item.IsSelected = false;
                    }
                    if (Solution.Ins.ProjectList != null && Solution.Ins.ProjectList.Count > 0)
                    {
                        (Solution.Ins.ProjectList.Last() as ProjectFlowTree).IsSelected = true;
                    }
                    break;
                default:
                    //foreach (TreeNode item in TreeSoureList)
                    //{
                    //    if (item.ToolInfo == null) break;
                    //    if (item.ToolInfo.Name == name)
                    //    {
                    //        item.IsSelected = true;
                    //    }
                    //    else
                    //    {
                    //        item.IsSelected = false;
                    //    }
                    //}
                    break;
            }
            processTree.ItemsSource = Solution.Ins.ProjectList;
        }
        #endregion

        #region 流程鼠标右键方法
        private void miCreateFolder_Click(object sender, RoutedEventArgs e)
        {
            ProjectFlowTree project = new ProjectFlowTree();
            Solution.Ins.CurrentProjectID = Solution.Ins.CreateProject(ProjectType.Folder, project);
            Solution.Ins.CurrentProject = Solution.Ins.GetProjectById(Solution.Ins.CurrentProjectID);
            UpdateTree("LastNode");
        }

        //private void miCopy_Click(object sender, RoutedEventArgs e)
        //{
        //    if (Solution.Ins.CurrentProject == null) return;

        //    MemoryStream stream = SerializeHelp.BinSerializeAndSaveStream(Solution.Ins.CurrentProject);
        //    Clipboard.SetData(DataFormats.Serializable, stream);
        //    stream?.Close();
        //}

        //private void miPaste_Click(object sender, RoutedEventArgs e)
        //{
        //    MemoryStream stream = (MemoryStream)Clipboard.GetData(DataFormats.Serializable);
        //    ProjectFlowTree project = SerializeHelp.BinDeserialize<ProjectFlowTree>(stream);
        //    stream?.Close();
        //    if(project == null) return;
        //    //只支持流程
        //    Solution.Ins.CreateProject(project.ProjectInfo.ProjectType, project);
        //    UpdateTree("LastNode");
        //}
        private void miCut_Click(object sender, RoutedEventArgs e)
        {

        }

        private void processTree_PreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
            }
        }

        private void miDeleteProcess_Click(object sender, RoutedEventArgs e)
        {
            btnDeleteProcess_Click(null, null);
        }

        private void miEditRemarks_Click(object sender, RoutedEventArgs e)
        {
            SelectedProject = processTree.SelectedItem as ProjectFlowTree;
            if (SelectedProject == null) return;

            EditRemarksView editRemarks = EditRemarksView.Ins;

            string remarks = editRemarks.MessageBoxShow(SelectedProject.ProjectInfo.Remarks);
            if (editRemarks.DialogResult == true)
            {
                int projectID = SelectedProject.ProjectInfo.ProjectID;
                foreach (var item in Solution.Ins.ProjectList)
                {
                    if (item.ProjectInfo.ProjectID == projectID)
                    {
                        item.ProjectInfo.Remarks = remarks;
                        break;
                    }
                }
                processTree.ItemsSource = Solution.Ins.ProjectList;
            }
        }

        private void miRename_Click(object sender, RoutedEventArgs e)
        {
            SelectedProject = processTree.SelectedItem as ProjectFlowTree;
            if (SelectedProject == null) return;

            EditRemarksView editView = EditRemarksView.Ins;

            string name = editView.MessageBoxShow(SelectedProject.ProjectInfo.ProcessName);
            if (editView.DialogResult == true)
            {
                foreach (var item in Solution.Ins.ProjectList)
                {
                    if (item.ProjectInfo.ProcessName == name)
                    {
                        MessageBoxView.Ins.MessageBoxShow("名称重复！", MsgType.Warn);
                        return;
                    }
                }
                SelectedProject.ProjectInfo.ProcessName = name;
                processTree.ItemsSource = Solution.Ins.ProjectList;
            }
        }

        private void UpdateMenuItemsCheckState(MenuItem parentMenu, MenuItem clickedItem)
        {

        }
        #endregion



        private void Button_Click(object sender, RoutedEventArgs e)
        {

        }




        private void processTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            ProjectFlowTree node = processTree.SelectedItem as ProjectFlowTree;
            if (node == null) return;
            Solution.Ins.CurrentProjectID = node.ProjectInfo.ProjectID;
            Solution.Ins.CurrentProject = Solution.Ins.GetProjectById(Solution.Ins.CurrentProjectID);
            //var processView = IoC.Get<IProcess>();
            //if (processView == null) return;
            //processView.UpdateTree();
        }


    }
}
