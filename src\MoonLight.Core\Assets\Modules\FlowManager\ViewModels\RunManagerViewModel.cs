﻿using EventMgrLib;
using MoonLight.Core.Common.Helper;
using MoonLight.Core.Services;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework;
using System;
using System.ComponentModel.Composition;
using MoonLight.Core.Enums;
using MoonLight.Modules.FlowManager.Views;
using System.Windows.Threading;
using System.Windows;

namespace MoonLight.Modules.FlowManager.ViewModels
{
    [Export]
    public class RunManagerViewModel : Tool
    {
        private bool _IsRunning = false;
        public bool IsRunning
        {
            get { return _IsRunning; }
            set { Set(ref _IsRunning, value); IoC.Get<PropertyPanelViewModel>().IsEnabled = !value; }
        }


        private ProjectFlowTree _project;
        public ProjectFlowTree Project => (ProjectFlowTree)Solution.Ins.CurrentProject;

        public RunManagerViewModel(ProjectFlowTree project)
        {
            _project = project;

        }

        public void OnProjectChanged()
        {
            //if (_view != null)
            //    _view.toolTree.ItemsSource = Project.ToolList;
            Application.Current.Dispatcher.BeginInvoke(DispatcherPriority.Background, () =>
            {
                NotifyOfPropertyChange("Project");
            });
        }

        public override PaneLocation PreferredLocation => PaneLocation.Left;
        public RunManagerViewModel()
        {
            EventMgr.Ins.GetEvent<ProjectChangedEvent>().Subscribe(OnProjectChanged, false);
            DisplayName = "运行栏";
            //EventMgr.Ins.GetEvent<ProjectChangedEvent>().Subscribe(ProjectChangedHandle);
        }


        RunManagerView _view;
        protected override void OnViewLoaded(object view)
        {
            _view = (RunManagerView)view;
        }



        // 运行一次
        [NonSerialized]
        private CommandBase _RunOnceCommand;
        public CommandBase RunOnceCommand
        {
            get
            {
                if (_RunOnceCommand == null)
                {
                    _RunOnceCommand = new CommandBase((obj) =>
                    {
                        if (Solution.Ins.CurrentProject == null || Solution.Ins.CurrentProject.ToolList.Count == 0) return;

                        if (Solution.Ins.CurrentProject.BreakpointFlag)
                        {
                            Solution.Ins.CurrentProject.ContinueRunFlag = true;
                            Solution.Ins.CurrentProject.Breakpoint.Set();
                            return;
                        }
                        Solution.Ins.CurrentProject.RunMode = RunMode.RunOnce;
                        Solution.Ins.CurrentProject.Start();
                    });
                }
                return _RunOnceCommand;
            }
        }

        // 循环运行
        [NonSerialized]
        private CommandBase _RunCycleCommand;
        public CommandBase RunCycleCommand
        {
            get
            {
                if (_RunCycleCommand == null)
                {
                    _RunCycleCommand = new CommandBase((obj) =>
                    {
                        if (Solution.Ins.CurrentProject == null || Solution.Ins.CurrentProject.ToolList.Count == 0) return;
                        if (Solution.Ins.CurrentProject.ThreadStatus)
                        {
                            Solution.Ins.CurrentProject.BreakpointFlag = true;
                            Solution.Ins.CurrentProject.ContinueRunFlag = false;
                        }
                        if (Solution.Ins.CurrentProject.BreakpointFlag)
                        {
                            Solution.Ins.CurrentProject.Breakpoint.Set();
                            return;
                        }
                        Solution.Ins.CurrentProject.RunMode = RunMode.RunCycle;
                        Solution.Ins.CurrentProject.Start();
                    });
                }
                return _RunCycleCommand;
            }
        }

        // 暂停运行
        [NonSerialized]
        private CommandBase _StopCommand;
        public CommandBase StopCommand
        {
            get
            {
                if (_StopCommand == null)
                {
                    _StopCommand = new CommandBase((obj) =>
                    {
                        Solution.Ins.CurrentProject.Stop();
                    });
                }
                return _StopCommand;
            }
        }
    }
}
