using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using HalconDotNet;
using MoonLight.Core.Common.Helper;
using Microsoft.Win32;

namespace MoonLight.App.Demo
{
    /// <summary>
    /// Halcon序列化测试窗口
    /// </summary>
    public partial class HalconSerializationTestWindow : Window
    {
        private HTuple _testHTuple;
        private HRegion _testHRegion;
        private HImage _testHImage;

        public HalconSerializationTestWindow()
        {
            InitializeComponent();
            UpdateStatus("测试窗口已初始化");
        }

        private void UpdateStatus(string message)
        {
            StatusTextBlock.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
        }

        #region HTuple测试

        private void CreateHTuple_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建包含不同类型数据的HTuple
                var intValues = new int[] { 1, 2, 3, 4, 5 };
                var doubleValues = new double[] { 1.1, 2.2, 3.3, 4.4, 5.5 };
                var stringValues = new string[] { "Hello", "World", "Halcon", "Test" };

                // 测试整数HTuple
                _testHTuple = new HTuple(intValues);

                HTupleInfoTextBox.Text = $"HTuple类型: {_testHTuple.Type}\n" +
                                       $"长度: {_testHTuple.Length}\n" +
                                       $"值: [{string.Join(", ", intValues)}]";

                UpdateStatus("HTuple创建成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建HTuple失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"创建HTuple失败: {ex.Message}");
            }
        }

        private void SerializeHTuple_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_testHTuple == null)
                {
                    MessageBox.Show("请先创建HTuple", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var options = new JsonSerializerHelper.HalconSerializationOptions
                {
                    FormatJson = true,
                    IncludeTypeInfo = true
                };

                var json = JsonSerializerHelper.SerializeHalconObject(_testHTuple, options);
                HTupleJsonTextBox.Text = json;

                UpdateStatus("HTuple序列化成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"序列化HTuple失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"序列化HTuple失败: {ex.Message}");
            }
        }

        private void DeserializeHTuple_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var json = HTupleJsonTextBox.Text;
                if (string.IsNullOrEmpty(json))
                {
                    MessageBox.Show("请先序列化HTuple", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var deserializedHTuple = JsonSerializerHelper.DeserializeHalconObject<HTuple>(json);

                if (deserializedHTuple != null)
                {
                    var values = new List<string>();
                    for (int i = 0; i < deserializedHTuple.Length; i++)
                    {
                        values.Add(deserializedHTuple[i].ToString());
                    }

                    HTupleInfoTextBox.Text += $"\n\n反序列化结果:\n" +
                                            $"类型: {deserializedHTuple.Type}\n" +
                                            $"长度: {deserializedHTuple.Length}\n" +
                                            $"值: [{string.Join(", ", values)}]";

                    UpdateStatus("HTuple反序列化成功");
                }
                else
                {
                    UpdateStatus("HTuple反序列化失败：结果为null");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"反序列化HTuple失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"反序列化HTuple失败: {ex.Message}");
            }
        }

        #endregion

        #region HRegion测试

        private void CreateHRegion_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建一个矩形区域
                _testHRegion = new HRegion();
                _testHRegion.GenRectangle1(100, 100, 300, 400);

                // 获取区域信息
                HTuple area, row, column;
                _testHRegion.AreaCenter(out area, out row, out column);

                HTuple row1, col1, row2, col2;
                _testHRegion.SmallestRectangle1(out row1, out col1, out row2, out col2);

                HRegionInfoTextBox.Text = $"区域面积: {area.D:F2}\n" +
                                        $"中心点: ({row.D:F2}, {column.D:F2})\n" +
                                        $"边界框: ({row1.D}, {col1.D}) - ({row2.D}, {col2.D})";

                UpdateStatus("HRegion创建成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建HRegion失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"创建HRegion失败: {ex.Message}");
            }
        }

        private void SerializeHRegion_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_testHRegion == null)
                {
                    MessageBox.Show("请先创建HRegion", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var options = new JsonSerializerHelper.HalconSerializationOptions
                {
                    FormatJson = true,
                    IncludeTypeInfo = true
                };

                var json = JsonSerializerHelper.SerializeHalconObject(_testHRegion, options);
                HRegionJsonTextBox.Text = json;

                UpdateStatus("HRegion序列化成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"序列化HRegion失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"序列化HRegion失败: {ex.Message}");
            }
        }

        private void DeserializeHRegion_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var json = HRegionJsonTextBox.Text;
                if (string.IsNullOrEmpty(json))
                {
                    MessageBox.Show("请先序列化HRegion", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var deserializedHRegion = JsonSerializerHelper.DeserializeHalconObject<HRegion>(json);

                if (deserializedHRegion != null)
                {
                    HTuple area, row, column;
                    deserializedHRegion.AreaCenter(out area, out row, out column);

                    HTuple row1, col1, row2, col2;
                    deserializedHRegion.SmallestRectangle1(out row1, out col1, out row2, out col2);

                    HRegionInfoTextBox.Text += $"\n\n反序列化结果:\n" +
                                             $"区域面积: {area.D:F2}\n" +
                                             $"中心点: ({row.D:F2}, {column.D:F2})\n" +
                                             $"边界框: ({row1.D}, {col1.D}) - ({row2.D}, {col2.D})";

                    UpdateStatus("HRegion反序列化成功");
                }
                else
                {
                    UpdateStatus("HRegion反序列化失败：结果为null");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"反序列化HRegion失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"反序列化HRegion失败: {ex.Message}");
            }
        }

        #endregion

        #region HImage测试

        private void CreateHImage_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建一个简单的测试图像
                _testHImage = new HImage();
                _testHImage.GenImageConst("byte", 256, 256);

                // 获取图像信息
                HTuple width, height, channels;
                _testHImage.GetImageSize(out width, out height);
                _testHImage.CountChannels(out channels);

                HImageInfoTextBox.Text = $"图像尺寸: {width.I} x {height.I}\n" +
                                       $"通道数: {channels.I}\n" +
                                       $"类型: 8位灰度图像";

                UpdateStatus("HImage创建成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建HImage失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"创建HImage失败: {ex.Message}");
            }
        }

        private void SerializeHImage_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_testHImage == null)
                {
                    MessageBox.Show("请先创建HImage", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var options = new JsonSerializerHelper.HalconSerializationOptions
                {
                    FormatJson = true,
                    IncludeTypeInfo = true,
                    ImageFormat = "png"
                };

                var json = JsonSerializerHelper.SerializeHalconObject(_testHImage, options);
                HImageJsonTextBox.Text = json;

                UpdateStatus("HImage序列化成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"序列化HImage失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"序列化HImage失败: {ex.Message}");
            }
        }

        private void DeserializeHImage_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var json = HImageJsonTextBox.Text;
                if (string.IsNullOrEmpty(json))
                {
                    MessageBox.Show("请先序列化HImage", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var deserializedHImage = JsonSerializerHelper.DeserializeHalconObject<HImage>(json);

                if (deserializedHImage != null)
                {
                    HTuple width, height, channels;
                    deserializedHImage.GetImageSize(out width, out height);
                    deserializedHImage.CountChannels(out channels);

                    HImageInfoTextBox.Text += $"\n\n反序列化结果:\n" +
                                            $"图像尺寸: {width.I} x {height.I}\n" +
                                            $"通道数: {channels.I}";

                    UpdateStatus("HImage反序列化成功");
                }
                else
                {
                    UpdateStatus("HImage反序列化失败：结果为null");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"反序列化HImage失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"反序列化HImage失败: {ex.Message}");
            }
        }

        #endregion

        #region 批量测试和文件操作

        private void BatchTest_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var results = new List<string>();
                results.Add("=== 批量测试开始 ===\n");

                // 测试多种HTuple类型
                var intHTuple = new HTuple(new int[] { 1, 2, 3, 4, 5 });
                var doubleHTuple = new HTuple(new double[] { 1.1, 2.2, 3.3 });
                var stringHTuple = new HTuple(new string[] { "Test", "Batch", "Serialization" });

                var halconObjects = new List<object> { intHTuple, doubleHTuple, stringHTuple };

                // 创建测试区域
                var region1 = new HRegion();
                region1.GenRectangle1(50, 50, 150, 200);
                var region2 = new HRegion();
                region2.GenCircle(100, 100, 50);
                halconObjects.AddRange(new object[] { region1, region2 });

                // 批量序列化
                var batchJson = JsonSerializerHelper.SerializeHalconObjects(halconObjects);
                results.Add($"批量序列化成功，JSON长度: {batchJson.Length} 字符\n");

                // 测试各种配置选项
                var options1 = new JsonSerializerHelper.HalconSerializationOptions
                {
                    FormatJson = false,
                    IncludeTypeInfo = false
                };

                var options2 = new JsonSerializerHelper.HalconSerializationOptions
                {
                    FormatJson = true,
                    IncludeTypeInfo = true,
                    EnableCompression = false
                };

                var json1 = JsonSerializerHelper.SerializeHalconObject(intHTuple, options1);
                var json2 = JsonSerializerHelper.SerializeHalconObject(intHTuple, options2);

                results.Add($"紧凑格式JSON长度: {json1.Length}\n");
                results.Add($"格式化JSON长度: {json2.Length}\n");

                // 测试反序列化
                var deserializedIntHTuple = JsonSerializerHelper.DeserializeHalconObject<HTuple>(json1);
                if (deserializedIntHTuple != null && deserializedIntHTuple.Length == intHTuple.Length)
                {
                    results.Add("HTuple反序列化验证成功\n");
                }
                else
                {
                    results.Add("HTuple反序列化验证失败\n");
                }

                results.Add("=== 批量测试完成 ===");
                BatchTestResultTextBox.Text = string.Join("", results);

                UpdateStatus("批量测试完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"批量测试失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"批量测试失败: {ex.Message}");
            }
        }

        private void PerformanceTest_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var results = new List<string>();
                results.Add("=== 性能测试开始 ===\n");

                var stopwatch = new Stopwatch();

                // 测试HTuple序列化性能
                var largeHTuple = new HTuple(Enumerable.Range(1, 10000).ToArray());

                stopwatch.Start();
                for (int i = 0; i < 100; i++)
                {
                    var json = JsonSerializerHelper.SerializeHalconObject(largeHTuple);
                }
                stopwatch.Stop();

                results.Add($"HTuple序列化 (10000元素 x 100次): {stopwatch.ElapsedMilliseconds} ms\n");
                results.Add($"平均每次: {stopwatch.ElapsedMilliseconds / 100.0:F2} ms\n\n");

                // 测试HRegion序列化性能
                var complexRegion = new HRegion();
                complexRegion.GenRectangle1(0, 0, 1000, 1000);

                stopwatch.Restart();
                for (int i = 0; i < 10; i++)
                {
                    var json = JsonSerializerHelper.SerializeHalconObject(complexRegion);
                }
                stopwatch.Stop();

                results.Add($"HRegion序列化 (1000x1000 x 10次): {stopwatch.ElapsedMilliseconds} ms\n");
                results.Add($"平均每次: {stopwatch.ElapsedMilliseconds / 10.0:F2} ms\n\n");

                // 测试小图像序列化性能
                var smallImage = new HImage();
                smallImage.GenImageConst("byte", 100, 100);

                stopwatch.Restart();
                for (int i = 0; i < 10; i++)
                {
                    var json = JsonSerializerHelper.SerializeHalconObject(smallImage);
                }
                stopwatch.Stop();

                results.Add($"HImage序列化 (100x100 x 10次): {stopwatch.ElapsedMilliseconds} ms\n");
                results.Add($"平均每次: {stopwatch.ElapsedMilliseconds / 10.0:F2} ms\n");

                results.Add("=== 性能测试完成 ===");
                BatchTestResultTextBox.Text = string.Join("", results);

                UpdateStatus("性能测试完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"性能测试失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"性能测试失败: {ex.Message}");
            }
        }

        private void SaveToFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "json"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    // 创建测试数据
                    var testData = new
                    {
                        HTuple = new HTuple(new int[] { 1, 2, 3, 4, 5 }),
                        Region = CreateTestRegion(),
                        Timestamp = DateTime.Now
                    };

                    JsonSerializerHelper.SerializeToFile(testData, saveDialog.FileName);

                    BatchTestResultTextBox.Text = $"数据已保存到文件: {saveDialog.FileName}\n" +
                                                $"保存时间: {DateTime.Now}";

                    UpdateStatus($"数据已保存到: {Path.GetFileName(saveDialog.FileName)}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"保存文件失败: {ex.Message}");
            }
        }

        private void LoadFromFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*"
                };

                if (openDialog.ShowDialog() == true)
                {
                    var json = File.ReadAllText(openDialog.FileName);

                    BatchTestResultTextBox.Text = $"从文件加载的JSON内容:\n" +
                                                $"文件: {openDialog.FileName}\n" +
                                                $"大小: {new FileInfo(openDialog.FileName).Length} 字节\n\n" +
                                                $"{json}";

                    UpdateStatus($"已从文件加载: {Path.GetFileName(openDialog.FileName)}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus($"加载文件失败: {ex.Message}");
            }
        }

        private HRegion CreateTestRegion()
        {
            var region = new HRegion();
            region.GenRectangle1(100, 100, 300, 400);
            return region;
        }

        #endregion
    }
}
