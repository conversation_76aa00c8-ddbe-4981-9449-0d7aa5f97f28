﻿using MoonLight.Core.Common.Helper;
using MoonLight.Core.Services;
using MoonLight.Modules.Output.Models;
using MoonLight.UI.Framework;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.IO;
using System.Reflection;
using System.Timers;
using System.Windows;

namespace MoonLight.App
{


    [Export(typeof(IModule))]
    public class Application : ModuleBase
    {
        private readonly IOutput _output;

        public override IEnumerable<Type> DefaultTools
        {
            get { yield return typeof(IOutput); }
        }

        [ImportingConstructor]
        public Application(IOutput output)
        {
            _output = output;
        }


        public override void Initialize()
        {
            Shell.ShowFloatingWindowsInTaskbar = true;
            Shell.ToolBars.Visible = true;

            MainWindow.WindowState = WindowState.Maximized;
            MainWindow.UpdateTitle(Solution.Ins.Name, null);

            timer.Elapsed += Timer_Elapsed;
            timer.Interval = 2000;
            timer.Start();
            //Shell.MainMenu.Add(new )

            
            Shell.StatusBar.AddItem("切换界面", new GridLength(100), new RelayCommand((a) =>
            {
                MainWindow.IsShowShellView = !MainWindow.IsShowShellView;
            }));
            //Shell.StatusBar.AddItem("StatusBar Test!", new GridLength(1, GridUnitType.Star));
            //Shell.StatusBar.AddItem("", new GridLength(100));
            //Shell.StatusBar.AddItem(index, new GridLength(100));
            Shell.ActiveDocumentChanged += (sender, e) => RefreshOutput();
            RefreshOutput();
            LoadEmptySolution();
        }

        //加载空方案
        private void LoadEmptySolution()
        {
            //SolutionManager.NewSolution();
        }

        private void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            index = (index + 1);
        }

        Timer timer = new Timer();
        string index = "";

        private void RefreshOutput()
        {
            _output.AppendLine("你好");
        }
    }

}
