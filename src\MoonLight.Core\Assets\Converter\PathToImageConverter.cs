﻿using MoonLight.Core.Services;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Media.Imaging;

namespace MoonLight.Core.Assets.Converter
{
    [ValueConversion(typeof(string), typeof(BitmapImage))]
    public class PathToImageConverter : IValueConverter
    {
        public static PathToImageConverter Instance { get; } = new PathToImageConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return FileIconService.Ins.GetImage((string)value);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
