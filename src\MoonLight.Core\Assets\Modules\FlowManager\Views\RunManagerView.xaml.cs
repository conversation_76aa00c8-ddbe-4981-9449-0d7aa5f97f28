﻿using MoonLight.Core.Common.Helper;
using MoonLight.Core.Enums;
using MoonLight.Core.Events;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Core.Services;
using MoonLight.Core.Views;
using MoonLight.Modules.FlowManager.ViewModels;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.Composition;
using System.IO;
using System.Windows.Automation.Peers;
using System.Windows;
using System;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Linq;
using MoonLight.Core.Extension;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace MoonLight.Modules.FlowManager.Views
{
    /// <summary>
    /// RunManagerView.xaml 的交互逻辑
    /// </summary>
    [Export]
    public partial class RunManagerView : UserControl, IProcess
    {

        private RunManagerViewModel vm;
        public ToolUnit SelectedTool { get; set; }
        public RunManagerView()
        {
            InitializeComponent();
            vm = new RunManagerViewModel();
            this.DataContext = vm;
            //show(HHandle)
        }

        private Dictionary<string, bool> NodesStatusDic = new Dictionary<string, bool>(); //用于保存是否展开的状态 用key作为容器, 刷新前清除容器, 需要保证键值唯一

        public List<TreeNode> ToolNodeList = new List<TreeNode>(); //treeview下 所有的toolNode 
        public List<TreeNode> TreeSoureList { get; set; } = new List<TreeNode>(); //treeview下 绑定的源数据

        private Cursor m_DragCursor;//拖拽时候的光标
        private string m_DragToolName;//移动位置的时候 模块名称/
        private bool m_DragMoveFlag;//移动标志
        private double m_MousePressY;//鼠标点下时的y坐标
        private double m_MousePressX;//鼠标点下时的X坐标

        private string MultiSelectedStart { get; set; }//多选下开始的模块名称
        private string MultiSelectedEnd { get; set; }//多选下结束的模块名称
        private int MultiSelectedCount { get; set; }//多选模块总数

        //之前选中的ToolNode
        public TreeNode SelectedNode { get; set; }
        public List<string> SelectedToolNameList { get; set; } = new List<string>();// 连续选中模式下 选择的tool



        public void GetToolChildList(ToolUnit tool, out List<ToolUnit> tools)
        {
            tools = new List<ToolUnit>();
            if (!PluginService.PluginDic_Tool.Keys.Contains(tool.PluginName))
            {
                return;
            }
            //TreeNode node = TreeSoureList.Find(item => item.Name == tool.Name);
            //if (node == null)
            //{
            //    return;
            //}
            //foreach(var item in node.Children)
            //{
            //    //tools.Add(item.ToolInfo);
            //}
        }

        #region 模块拖拽功能





        /// 添加一个模块
        /// <param name="curToolName">要追加的模块目标位置模块名称</param>
        /// <param name="info">模块信息</param>
        /// <param name="isNext">是否在后方追加</param>
        private void AddTool(string relativeToolName, string pluginName, bool isNext, bool isDoubleClick = false)
        {
            if (!PluginService.PluginDic_Tool.Keys.Contains(pluginName)) return;

            ToolUnit tool = (ToolUnit)Activator.CreateInstance(PluginService.PluginDic_Tool[pluginName].ToolType);
            //tool.SolutiontID = Solution.Ins.SolutionID;
            tool.ProjectID = vm.Project.ProjectInfo.ProjectID;
            tool.PluginName = pluginName;
            tool.IconImage = ImageTool.GetImageByName(pluginName);

            List<string> nameList = vm.Project.ToolList.Select(c => c.Name).ToList();
            string toolName = "";

            int cnt = GetToolNameCount(vm.Project.ToolList.ToList(), pluginName);
            toolName = pluginName + ((cnt != 0) ? cnt.ToString() : "");
            tool.Name = toolName;
            tool.AddOutputParams();
            string addToolName = tool.Name;

            //找到选中节点
            int index = 0;
            var item = GetTool(vm.Project.ToolList.ToList(), relativeToolName);
            if (item != null) //存在
            {
                if (item.AllowHaveChild && bIsChild)
                {
                    tool.Parent = item;
                    item.ToolList.Add(tool);
                }
                else if (item.Parent != null)
                {
                    index = item.Parent.ToolList.FindIndex(c => c.Name == relativeToolName);
                    tool.Parent = item.Parent;
                    item.Parent.ToolList.Insert(index + 1, tool);
                }
                else
                {
                    index = vm.Project.ToolList.FindIndex(c => c.Name == relativeToolName);
                    vm.Project.ToolList.Insert(index + 1, tool);
                }
            }
            else
            {
                vm.Project.ToolList.Add(tool);
            }

            Dispatcher.BeginInvoke(DispatcherPriority.Background, () => tool.GetUserControl());
                //Application.Current?.Dispatcher.BeginInvoke(() =>  tool.GetUserControl() );

            vm.Project.ToolList_CollectionChanged(null, null);
            int indexTool = 1;

            SortTools(vm.Project.ToolList.ToList(), ref indexTool);

        }

        private void AddTool(string relativeToolName, ToolUnit tool, bool isNext, bool isDoubleClick = false)
        {
            if (!PluginService.PluginDic_Tool.Keys.Contains(tool.PluginName)) return;

            List<string> nameList = vm.Project.ToolList.Select(c => c.Name).ToList();
            string toolName = "";


            int cnt = GetToolNameCount(vm.Project.ToolList.ToList(), tool.PluginName);
            toolName = tool.PluginName + ((cnt != 0) ? cnt.ToString() : "");
            tool.Name = toolName;
            //tool.SolutiontID = Solution.Ins.SolutionID;
            tool.ProjectID = vm.Project.ProjectInfo.ProjectID;
            tool.Prj = vm.Project;

            //子节点更改
            foreach (var child in tool.ToolList)
            {
                //child.SolutiontID = Solution.Ins.SolutionID;
                child.ProjectID = vm.Project.ProjectInfo.ProjectID;
                child.Prj = vm.Project;
            }

            //找到选中节点
            int index = 0;
            var item = GetTool(vm.Project.ToolList.ToList(), relativeToolName);
            if (item != null) //存在
            {
                if (item.AllowHaveChild && bIsChild)
                {
                    tool.Parent = item;
                    item.ToolList.Add(tool);
                }
                else if (item.Parent != null)
                {
                    index = item.Parent.ToolList.FindIndex(c => c.Name == relativeToolName);
                    tool.Parent = item.Parent;
                    item.Parent.ToolList.Insert(index + 1, tool);
                }
                else
                {
                    index = vm.Project.ToolList.FindIndex(c => c.Name == relativeToolName);
                    vm.Project.ToolList.Insert(index + 1, tool);
                    tool.Parent = null;
                }
            }
            else
            {
                vm.Project.ToolList.Add(tool);
                tool.Parent = null;
            }


            vm.Project.ToolList_CollectionChanged(null, null);

            int indexTool = 1;
            SortTools(vm.Project.ToolList.ToList(), ref indexTool);

        }

        private ToolUnit GetTool(List<ToolUnit> list, string name)
        {
            foreach (var item in list)
            {
                if (item.Name == name)
                {
                    return item;
                }
                var tool = GetTool(item.ToolList.ToList(), name);
                if (tool != null) { return tool; }
            }
            return null;
        }

        private void SortTools(List<ToolUnit> list, ref int startIndex)
        {
            ToolUnit lastTool = null;
            foreach (var item in list)
            {
                item.Encode = startIndex;
                startIndex++;
                if (item.AllowHaveChild)
                {
                    if (lastTool != null)
                    {
                        //lastTool.TreeNode.DragOverHeight = 30;
                        item.DragOverHeight = 10;
                    }
                    SortTools(item.ToolList.ToList(), ref startIndex);
                }
                else
                {
                    item.DragOverHeight = 10;
                }

                lastTool = item;
            }
        }

        private int GetToolNameCount(List<ToolUnit> list, string name)
        {
            int cnt = 0;
            foreach (var item in list)
            {
                if (item.Name.Contains(name))
                {
                    cnt++;
                }
                cnt += GetToolNameCount(item.ToolList.ToList(), name);
            }
            return cnt;
        }

        private bool IsExistInList(List<ToolUnit> list, string name)
        {
            if (list == null || list.Count <= 0)
                return false;
            int index = list.FindIndex(item => item.Name.Equals(name));
            return index >= 0;
        }

        private void ChangeToolPos(string toolStartName, string toolEndName, string relativeToolName, bool isNext)
        {
            if (toolStartName == relativeToolName)
            {
                return;//名称相同则不修改
            }

            if (vm.Project == null) return;

            List<ToolUnit> tools = new List<ToolUnit>();
            vm.Project.GetTools(vm.Project.ToolList.ToList(), ref tools);

            if (tools.Count <= 0 || tools[0] == null) return;

            if (toolStartName != toolEndName)  //多选状态
            {
                int startIndex = tools.FindIndex(c => c.Name == toolStartName);
                int endIndex = tools.FindIndex(c => c.Name == toolEndName);
                ToolUnit relativeTool = tools.Find(c => c.Name == relativeToolName);
                List<ToolUnit> toolUnits = tools.Skip(startIndex).Take(endIndex - startIndex + 1).ToList();
                if (relativeTool != null) //存在
                {
                    if (relativeTool.AllowHaveChild && bIsChild)
                    {
                        // 尾插
                        foreach (var tool in toolUnits)
                        {
                            if (tool.Parent == null)
                            {
                                vm.Project.ToolList.Remove(tool);
                                tool.Parent = relativeTool;
                                relativeTool.ToolList.Add(tool);
                            }
                            else
                            {
                                if (!IsExistInList(toolUnits, tool.Parent.Name)) // 如果父节点不在多选选中的节点中，此情况需要更换父节点
                                {
                                    tool.Parent.ToolList.Remove(tool);
                                    tool.Parent = relativeTool;
                                    relativeTool.ToolList.Add(tool);
                                }
                            }
                        }
                    }
                    else if (relativeTool.Parent != null)
                    {
                        //插入指定节点后
                        foreach (var tool in toolUnits)
                        {
                            if (tool.Parent == null)
                            {
                                vm.Project.ToolList.Remove(tool);
                                int index = relativeTool.Parent.ToolList.FindIndex(c => c.Name == relativeToolName);
                                tool.Parent = relativeTool.Parent;
                                relativeTool.Parent.ToolList.Insert(index + 1, tool);
                                relativeToolName = tool.Name;
                            }
                            else
                            {
                                if (!IsExistInList(toolUnits, tool.Parent.Name)) // 如果父节点不在多选选中的节点中，此情况需要更换父节点
                                {
                                    tool.Parent.ToolList.Remove(tool);
                                    int index = relativeTool.Parent.ToolList.FindIndex(c => c.Name == relativeToolName);
                                    tool.Parent = relativeTool.Parent;
                                    relativeTool.Parent.ToolList.Insert(index + 1, tool);
                                    relativeToolName = tool.Name;
                                }
                            }
                        }
                    }
                    else
                    {
                        //插入指定节点后
                        foreach (var tool in toolUnits)
                        {
                            if (tool.Parent == null)
                            {
                                vm.Project.ToolList.Remove(tool);
                                int index = vm.Project.ToolList.FindIndex(c => c.Name == relativeToolName);
                                vm.Project.ToolList.Insert(index + 1, tool);
                                relativeToolName = tool.Name;
                            }
                            else
                            {
                                if (!IsExistInList(toolUnits, tool.Parent.Name)) // 如果父节点不在多选选中的节点中，此情况需要更换父节点
                                {
                                    tool.Parent.ToolList.Remove(tool);
                                    int index = vm.Project.ToolList.FindIndex(c => c.Name == relativeToolName);
                                    tool.Parent = null;
                                    vm.Project.ToolList.Insert(index + 1, tool);
                                    relativeToolName = tool.Name;
                                }
                            }
                        }
                    }
                }
            }
            int indexTool = 1;
            SortTools(vm.Project.ToolList.ToList(), ref indexTool);

            //else // 非多选状态
            //{
            //    if (!toolStartName.StartsWith("条件分支") && !toolStartName.StartsWith("执行片段")
            //            && !toolStartName.StartsWith("文件夹") && !toolStartName.StartsWith("坐标补正")
            //            && !toolStartName.StartsWith("点云补正") && !toolStartName.StartsWith("循环工具") && !toolStartName.StartsWith("并行块"))
            //    {
            //        //先删除
            //        toolnameList.Remove(toolStartName);

            //        //获取定位模块的位置
            //        int index = toolnameList.IndexOf(relativeToolName);

            //        if (index == -1 && isNext == true)//添加在首
            //        {
            //            toolnameList.Insert(0, toolStartName);
            //        }
            //        else if (index == -1 && isNext == false)//添加在末尾
            //        {
            //            toolnameList.Add(toolStartName);
            //        }
            //        else if (index != -1 && isNext == true)//插在后面
            //        {
            //            toolnameList.Insert(index + 1, toolStartName);
            //        }
            //        else if (index != -1 && isNext == false)//插在前面
            //        {
            //            toolnameList.Insert(index, toolStartName);
            //        }

            //    }
            //}


            ////根据新的toolnameList 重新调整ToolInfoList
            //ObservableCollection<ToolUnit> tempToolInfoList = new ObservableCollection<ToolUnit>();

            //foreach (string toolName in toolnameList)
            //{
            //    tempToolInfoList.Add(vm.Project.ToolList.FirstOrDefault(c => c.Name == toolName));
            //}

            //vm.Project.ToolList = tempToolInfoList;

            //UpdateTree(toolStartName);
        }

        private void ChangeToolPos(string toolName, string relativeToolName)
        {
            //找到选中节点
            int index = 0;
            var relativeTool = GetTool(vm.Project.ToolList.ToList(), relativeToolName);
            var tool = GetTool(vm.Project.ToolList.ToList(), toolName);

            if (relativeTool != null) //存在
            {
                if (relativeTool.AllowHaveChild && bIsChild)
                {
                    if (tool.Parent != null)
                    {
                        tool.Parent.ToolList.Remove(tool);
                    }
                    else
                    {
                        vm.Project.ToolList.Remove(tool);
                    }


                    tool.Parent = relativeTool;
                    relativeTool.ToolList.Add(tool);
                }
                else if (relativeTool.Parent != null)
                {
                    if (relativeTool.Parent == tool)
                    {
                        return;
                    }
                    if (tool.Parent != null)
                    {
                        tool.Parent.ToolList.Remove(tool);
                    }
                    else
                    {
                        vm.Project.ToolList.Remove(tool);
                    }

                    index = relativeTool.Parent.ToolList.FindIndex(c => c.Name == relativeToolName);
                    tool.Parent = relativeTool.Parent;
                    relativeTool.Parent.ToolList.Insert(index + 1, tool);
                }
                else
                {
                    if (tool.Parent != null)
                    {
                        tool.Parent.ToolList.Remove(tool);
                    }
                    else
                    {
                        vm.Project.ToolList.Remove(tool);
                    }

                    index = vm.Project.ToolList.FindIndex(c => c.Name == relativeToolName);
                    tool.Parent = null;
                    vm.Project.ToolList.Insert(index + 1, tool);
                }
            }
            else
            {
                vm.Project.ToolList.Add(tool);
            }

            int indexTool = 1;
            SortTools(vm.Project.ToolList.ToList(), ref indexTool);
        }



        //拖拽丢下数据
        private void toolTree_Drop(object sender, DragEventArgs e)
        {
            DragDropModel model = e.Data.GetData("MoonLight.Core.Models.DragDropModel") as DragDropModel;
            if (model == null || (model.SourceName != "tool" && model.SourceName != "toolTree")) return;
            if (vm.Project == null)
            {
                MessageBox.Show("当前流程为空，请先创建流程！", "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            if (SelectedTool != null)// 恢复之前的下划线
            {
                SelectedTool.DragOverHeight = 10;
            }

            if (e.AllowedEffects == DragDropEffects.Copy)//表示从工具栏拖动 需要创建新的模块
            {
                //TimeTool.Start("创建模块计时");
                string pluginName = model.Name;

                if (SelectedTool != null && vm.Project.ToolList.Count != 0)
                {
                    AddTool(SelectedTool.Name, pluginName, true);
                }
                else if (vm.Project.ToolList.Count == 0)
                {
                    AddTool("", pluginName, true);//第一创建
                }
                else //没有选择 则默认选择最后一个
                {
                    AddTool(vm.Project.ToolList.Last().Name, pluginName, true);
                }

            }
            else if (e.AllowedEffects == DragDropEffects.Move)//表示移动位置
            {

                if (model.Name != null && SelectedTool != null)
                {
                    string toolName = model.Name;

                    if (toolName != SelectedTool.Name)//自己不能移动到自己下面
                    {
                        if (IsMultiSelectedModel(vm.Project.ToolList.ToList()) == true)
                        {
                            ChangeToolPos(MultiSelectedStart, MultiSelectedEnd, SelectedTool.Name, true);
                        }
                        else
                        {
                            ChangeToolPos(toolName, SelectedTool.Name);
                            //ChangeToolPos(toolName, toolName, SelectedNode.Name, true);
                        }
                    }
                }
            }
        }

        //拖拽的时候 鼠标移动
        private void toolTree_DragOver(object sender, DragEventArgs e)
        {
            //获取鼠标位置的TreeViewItem 然后选中
            Point pt = e.GetPosition(toolTree);
            HitTestResult result = VisualTreeHelper.HitTest(toolTree, pt);
            if (result == null) return;
            TreeViewItem SelectedItem = WPFElementTool.FindVisualParent<TreeViewItem>(result.VisualHit);

            if (SelectedItem != null)
            {
                SelectedItem.IsSelected = true;
                ToolUnit tool = SelectedItem.DataContext as ToolUnit;
                if (SelectedTool != null)
                {
                    if (SelectedTool.Name != tool.Name)//名称不一样说明更换了tool  恢复之前的下划线
                    {
                        //SelectedNode.DragOverHeight = 10;
                    }
                }
                SelectedTool = tool;
                //SelectedNode.DragOverHeight = 15;//划过的时候高度变为2
            }

            else
            {

            }

            //获取treeview本身的 ScrollViewer
            TreeViewAutomationPeer lvap = new TreeViewAutomationPeer(toolTree);
            ScrollViewerAutomationPeer svap = lvap.GetPattern(PatternInterface.Scroll) as ScrollViewerAutomationPeer;
            ScrollViewer scroll = svap.Owner as ScrollViewer;

            pt = e.GetPosition(toolTree);

            if (toolTree.ActualHeight - pt.Y <= 50)
            {
                scroll.ScrollToVerticalOffset(scroll.VerticalOffset + 10);
            }
            if (Math.Abs(pt.Y) <= 50)
            {
                scroll.ScrollToVerticalOffset(scroll.VerticalOffset - 10);
            }
        }

        //拖拽的时候 离开区域
        private void toolTree_DragLeave(object sender, DragEventArgs e)
        {
            if (SelectedNode != null)
            {
                //SelectedNode.DragOverHeight = 10; // 恢复之前的下划线

            }


        }

        private void toolTree_MouseMove(object sender, MouseEventArgs e)
        {
            if (m_DragMoveFlag == true)
            {
                if (vm.Project == null) return;
                Point pt = e.GetPosition(toolTree);
                if (Math.Abs(pt.Y - m_MousePressY) > 10 || Math.Abs(pt.X - m_MousePressX) > 10)//在y方向差异10像素 才开始拖动
                {
                    if (vm.Project.GetThreadStatus())
                    {
                        m_DragCursor = WPFCursorTool.CreateCursor(200, 30, 13, ImageTool.ImageSourceToBitmap(null), 32, "");
                        m_DragMoveFlag = false;
                    }
                    else
                    {
                        string showText = "";
                        int width = 0;
                        if (IsMultiSelectedModel(vm.Project.ToolList.ToList()) == true)
                        {
                            showText = $"[{MultiSelectedStart}] ~ [{MultiSelectedEnd}]";
                            width = 400;
                        }
                        else
                        {
                            width = 200;
                            showText = SelectedTool.Name;
                        }
                        m_DragCursor = WPFCursorTool.CreateCursor(width, 30, 13, ImageTool.ImageSourceToBitmap(SelectedTool.IconImage), 32, showText);
                        m_DragMoveFlag = false;
                        DragDropModel data = new DragDropModel() { Name = m_DragToolName, SourceName = "toolTree" };
                        DragDrop.DoDragDrop(toolTree, data, DragDropEffects.Move);
                    }

                }
            }
        }

        //拖拽的时候鼠标样式
        private void toolTree_GiveFeedback(object sender, GiveFeedbackEventArgs e)
        {
            e.UseDefaultCursors = false;
            Mouse.SetCursor(m_DragCursor);
            e.Handled = true;
        }

        //按键事件
        private void toolTree_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyboardDevice.Modifiers == ModifierKeys.Shift)
            {
                //只按下了shift 则开始记录是从那里开始连续选中
                //if (SelectedNode != null && !SelectedToolNameList.Contains(SelectedNode.ToolInfo.Name))
                //{
                //    //SelectedToolNameList.Add(SelectedNode.ToolInfo.Name);
                //}
            }
            else if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.A)
            {
                SelectedToolNameList.Clear();
                foreach (ToolUnit tool in vm.Project.ToolList)
                {
                    tool.IsMultiSelected = true;
                    SelectedToolNameList.Add(tool.Name);

                    if (tool.ToolList.Count > 0)
                    {
                        //如果当前模块含有子类,则选中所有子类
                        MultiSelectToolNode(tool);
                    }
                }

                var dic = new Dictionary<int, string>();
                foreach (string toolName in SelectedToolNameList)
                {
                    ToolUnit currentTool = GetTool(vm.Project.ToolList.ToList(), toolName);
                    if (currentTool != null && !dic.ContainsKey(currentTool.Encode))
                    {
                        dic[currentTool.Encode] = toolName;
                    }
                }

                if (dic.Count > 0)
                {
                    MultiSelectedStart = dic[dic.Keys.Min()];
                    MultiSelectedEnd = dic[dic.Keys.Max()];
                    MultiSelectedCount = dic.Keys.Max() - dic.Keys.Min() + 1;
                }
            }
            else if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.C)
            {
                miCopy_Click(null, null);
            }
            else if (e.KeyboardDevice.Modifiers == ModifierKeys.Control && e.Key == Key.V)
            {
                miPaste_Click(null, null);
            }
        }

        private void toolTree_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            toolTree.Focus();

            //获取鼠标位置的TreeViewItem 然后选中
            Point pt = e.GetPosition(toolTree);
            HitTestResult result = VisualTreeHelper.HitTest(toolTree, pt);
            if (result == null) return;

            TreeViewItem SelectedItem = WPFElementTool.FindVisualParent<TreeViewItem>(result.VisualHit);

            if (SelectedItem != null)
            {
                SelectedTool = (SelectedItem.DataContext as ToolUnit);
                SelectedItem.IsSelected = true;
            }

            //按住shift 多选
            if (SelectedItem != null && Keyboard.Modifiers == ModifierKeys.Shift)
            {
                MultiSelect(SelectedItem.DataContext as ToolUnit);
                e.Handled = true;
                return;
            }

            //靠近滚轮则不执行拖动
            if (toolTree.ActualWidth - pt.X > 80)
            {

                if (SelectedTool != null /*&& SelectedTool.TreeNode?.IsCategory == false*/)
                {
                    m_MousePressY = pt.Y;
                    m_MousePressX = pt.X;
                    m_DragToolName = SelectedTool.Name;
                    m_DragMoveFlag = true;
                }
            }
        }

        //鼠标左键弹起
        private void ToolTree_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (vm.Project == null) return;
            if (IsMultiSelectedModel(vm.Project.ToolList.ToList()) == true && Keyboard.Modifiers != ModifierKeys.Shift)//鼠标弹起 多选模式 取消显示
            {
                CancelMultiSelect();
            }
        }

        //多选
        private void MultiSelect(ToolUnit tool)
        {
            if (tool == null) return;

            //获取多选的tool的index
            tool.IsMultiSelected = true;
            SelectedToolNameList.Add(tool.Name);

            //获取多选的tool的index
            var dic = new Dictionary<int, string>();
            foreach (string toolName in SelectedToolNameList)
            {
                ToolUnit currentTool = GetTool(vm.Project.ToolList.ToList(), toolName);
                if (currentTool != null && !dic.ContainsKey(currentTool.Encode))
                {
                    dic[currentTool.Encode] = toolName;
                }
            }

            if (dic.Count > 0)
            {
                //将结束模块也加入
                List<ToolUnit> tools = new List<ToolUnit>();
                vm.Project.GetTools(vm.Project.ToolList.ToList(), ref tools);
                foreach (ToolUnit toolUnit in tools)
                {
                    if (toolUnit.Encode >= dic.Keys.Min() && toolUnit.Encode <= dic.Keys.Max())
                    {
                        toolUnit.IsMultiSelected = true;
                        SelectedToolNameList.Add(toolUnit.Name);

                        if (toolUnit.ToolList.Count > 0)
                        {
                            //如果当前模块含有子类,则选中所有子类
                            MultiSelectToolNode(toolUnit);
                        }
                    }
                }
            }

            // 重新获取多选
            dic.Clear();
            foreach (string toolName in SelectedToolNameList)
            {
                ToolUnit currentTool = GetTool(vm.Project.ToolList.ToList(), toolName);
                if (currentTool != null && !dic.ContainsKey(currentTool.Encode))
                {
                    dic[currentTool.Encode] = toolName;
                }
            }

            if (dic.Count > 0)
            {
                MultiSelectedStart = dic[dic.Keys.Min()];
                MultiSelectedEnd = dic[dic.Keys.Max()];
                MultiSelectedCount = dic.Keys.Max() - dic.Keys.Min() + 1;
            }
        }

        //是否是在多选模式下
        private bool IsMultiSelectedModel(List<ToolUnit> tools)
        {
            if (tools == null) return false;

            foreach (ToolUnit tool in tools)
            {
                // 如果当前工具的 IsMultiSelected 为 true，立即返回 true  
                if (tool.IsMultiSelected)
                {
                    return true;
                }
                // 如果工具列表不为空，继续递归检查子工具  
                else if (tool.ToolList.Count > 0)
                {
                    if (IsMultiSelectedModel(tool.ToolList.ToList()))
                    {
                        return true;
                    }
                }
            }
            return false;
        }


        /// <summary>
        /// 遍历 获取当前ToolUnit下所有的子类,并设为multiselected=true
        /// </summary>
        private void MultiSelectToolNode(ToolUnit tool)
        {
            if (tool != null)
            {
                foreach (ToolUnit item in tool.ToolList)
                {
                    item.IsMultiSelected = true;
                    SelectedToolNameList.Add(item.Name);

                    if (item.ToolList.Count > 0)
                    {
                        MultiSelectToolNode(item);
                    }
                }
            }
        }

        //取消多选样式
        public void CancelMultiSelect()
        {
            List<ToolUnit> tools = new List<ToolUnit>();
            vm.Project.GetTools(vm.Project.ToolList.ToList(), ref tools);
            //点击的时候取消 多重选择效果
            foreach (ToolUnit item in tools)
            {
                item.IsMultiSelected = false;
            }

            SelectedToolNameList.Clear();
            MultiSelectedCount = 0;
        }

        private void toolTree_PreviewMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {

            //获取鼠标位置的TreeViewItem 然后选中
            Point pt = e.GetPosition(toolTree);
            HitTestResult result = VisualTreeHelper.HitTest(toolTree, pt);
            if (result == null) return;
            TreeViewItem SelectedItem = WPFElementTool.FindVisualParent<TreeViewItem>(result.VisualHit);

            if (SelectedItem != null)
            {
                SelectedItem.Focus();
            }
        }

        private void toolTree_PreviewMouseUp(object sender, MouseButtonEventArgs e)
        {
            m_DragMoveFlag = false;
            EventMgrLib.EventMgr.Ins.GetEvent<ToolOutChangedEvent>().Publish();
        }

        public void ClearStatus()
        {

            List<ToolUnit> list = new List<ToolUnit>();
            vm.Project.GetTools(vm.Project.ToolList.ToList(), ref list);
            foreach (var node in list)
            {
                //node.TreeNode.CostTime = "0";
                //node.TreeNode.StatusImage = null;
                //node.TreeNode.StatusColor = Brushes.Transparent;
                //node.TreeNode.IsRunning = false;
                //node.ConsumingTime = 0;
                node.StatusImage = null;
                node.StatusResultColor = Brushes.Transparent;
                node.IsRunning = false;
            }
        }



        public void UpdateTree(string selectedNoteName = "")
        {
            //ToolNodeList.Clear();
            //NodesStatusDic.Clear();
            //GetTreeNodesStatus(toolTree); // 保存展开节点信息
            //if (vm.Project == null)
            //{
            //    toolTree.ItemsSource = null;
            //    return;
            //}
            //ObservableCollection<ToolUnit> toolDic = vm.Project.ToolList;//模块信息

            ////将父节点放入栈容器 
            //Stack<TreeNode> s_ParentItemStack = new Stack<TreeNode>();
            //TreeSoureList.Clear();
            //for (int i = 0; i < toolDic.Count; i++)
            //{
            //    ToolUnit info = toolDic[i];
            //    if (info == null) return;
            //    info.ToolList.Clear();
            //    info.Encode = i + 1;
            //    TreeNode nodeItem = new TreeNode(info);
            //    nodeItem.IsExpanded = NodesStatusDic.ContainsKey(info.Name) ? NodesStatusDic[info.Name] : true;//还原展开状态
            //    ToolNodeList.Add(nodeItem);

            //    if (i == 0) nodeItem.IsFirstNode = true;

            //    if (info.Name.StartsWith("结束") ||
            //        info.Name.StartsWith("否则") ||// 是结束则 取消栈里对应的if
            //        info.Name.StartsWith("坐标补正结束") ||
            //         info.Name.StartsWith("文件夹结束") ||
            //        info.Name.StartsWith("点云补正结束") ||
            //        info.Name.StartsWith("并行块结束") ||
            //        info.Name.StartsWith("循环结束"))
            //    {
            //        if (s_ParentItemStack.Count > 0)
            //        {
            //            s_ParentItemStack.Pop();
            //        }
            //    }

            //    //~~~~~~~~~~~~~~~
            //    if (s_ParentItemStack.Count > 0)
            //    {
            //        nodeItem.Hierarchy = s_ParentItemStack.Count;//层级
            //        TreeNode parentItem = s_ParentItemStack.Peek();

            //        nodeItem.ParentToolNode = parentItem;
            //        parentItem.Children.Add(nodeItem);
            //        parentItem.ToolInfo.ToolList.Add(nodeItem.ToolInfo);
            //    }
            //    else
            //    {
            //        nodeItem.Hierarchy = 0;

            //        nodeItem.ParentToolNode = null;
            //        TreeSoureList.Add(nodeItem);    //根目录
            //    }
            //    //~~~~~~~~~~~~~~~
            //    //判断当前节点是否是父节点开始
            //    if (info.Name.StartsWith("如果") ||
            //        info.Name.StartsWith("否则") ||
            //        Regex.IsMatch(info.Name, "坐标补正开始[0-9]*$") ||
            //        Regex.IsMatch(info.Name, "并行块开始[0-9]*$") ||
            //        Regex.IsMatch(info.Name, "点云补正开始[0-9]*$") ||
            //        Regex.IsMatch(info.Name, "文件夹[0-9]*$") ||
            //        Regex.IsMatch(info.Name, "执行片段[0-9]*$") ||
            //        Regex.IsMatch(info.Name, "循环开始[0-9]*$")
            //        )
            //    {
            //        s_ParentItemStack.Push(nodeItem);
            //    }

            //    //最后一个node如果层级大于0 则需要补划最后一条横线
            //    if (i == toolDic.Count - 1 && nodeItem.Hierarchy > 0)
            //    {
            //        nodeItem.LastNodeMargin = $"{nodeItem.Hierarchy * -14},0,0,0";
            //    }
            //}
            //SelectNode(selectedNoteName);
        }

        /// <summary>
        /// 获取结构树的展开状态
        /// </summary>
        /// <param name="nodes"></param>
        private void GetTreeNodesStatus(ItemsControl control)
        {
            if (control != null)
            {
                foreach (object item in control.Items)
                {
                    TreeViewItem treeItem = control.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;

                    if (treeItem != null && treeItem.HasItems)
                    {
                        TreeNode toolNode = treeItem.DataContext as TreeNode;
                        NodesStatusDic[toolNode.Name] = treeItem.IsExpanded;
                        GetTreeNodesStatus(treeItem as ItemsControl);
                    }
                }
            }
        }
        private TreeNode findNote;
        /// <summary>
        /// 获取结构树的展开状态
        /// </summary>
        /// <param name="nodes"></param>
        private void GetTreeNode(ItemsControl control, ToolUnit tool)
        {
            if (control != null)
            {
                foreach (object item in control.Items)
                {
                    TreeViewItem treeItem = control.ItemContainerGenerator.ContainerFromItem(item) as TreeViewItem;
                    TreeNode toolNode = treeItem.DataContext as TreeNode;
                    if (toolNode.Name == tool.Name && toolNode.ProjectID == tool.ProjectID)
                    {
                        findNote = toolNode;
                        return;
                    }
                    if (treeItem != null && treeItem.HasItems)
                    {
                        toolNode = treeItem.DataContext as TreeNode;
                        if (toolNode.Name == tool.Name && toolNode.ProjectID == tool.ProjectID)
                        {
                            findNote = toolNode;
                            return;
                        }
                        GetTreeNode(treeItem as ItemsControl, tool);
                    }
                }
            }
        }

        public ToolUnit GetSelectedNode()
        {
            return toolTree.SelectedItem as ToolUnit;
        }

        #endregion

        #region 打开模块窗体方法
        private void toolTree_PreviewMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
#if else
            if (Solution.Ins.GetStates())
            {
                MessageBoxView.Ins.MessageBoxShow("请先停止项目！", MsgType.Warn);
                return;
            }
            if (e.ChangedButton == MouseButton.Left)
            {
                if (SelectedTool == null) return;
                string toolName = SelectedTool.Name;
                //int toolIndex = vm.Project.GetToolIndexByName(toolName);
                //if (toolName.StartsWith("结束"))
                //{
                //    string startToolName = toolName.Replace("结束", "如果");
                //    var tempTool = vm.Project.ToolList.Where(o => o.Name == startToolName).FirstOrDefault();
                //    int startIndex = vm.Project.ToolList.IndexOf(tempTool);
                //    //将父节点放入栈容器 
                //    Stack<ToolUnit> s_ParentItemStack = new Stack<ToolUnit>();
                //    TreeSoureList.Clear();
                //    for (int i = startIndex + 1; i < toolIndex; i++)
                //    {
                //        if (vm.Project.ToolList[i].Name.StartsWith("如果"))
                //        {
                //            s_ParentItemStack.Push(vm.Project.ToolList[i]);
                //        }
                //        if (vm.Project.ToolList[i].Name.StartsWith("结束"))
                //        {
                //            if (s_ParentItemStack.Count > 0)
                //            {
                //                s_ParentItemStack.Pop();
                //            }
                //        }
                //        //~~~~~~~~~~~~~~~
                //        if (s_ParentItemStack.Count == 0 && vm.Project.ToolList[i].Name.StartsWith("否则"))
                //        {
                //            return;
                //        }
                //    }
                //    ToolUnit preToolBase = vm.Project.ToolList[toolIndex - 1];
                //    AddTool(preToolBase.Name, "条件分支", true, true);
                //    return;
                //}
                //else if (toolName.StartsWith("否则如果"))
                //{
                //}
                //else if (toolName.StartsWith("否则"))
                //{
                //    ToolUnit preToolBase = vm.Project.ToolList[toolIndex - 1];
                //    AddTool(preToolBase.Name, "条件分支", true, true);
                //    return;
                //}
                /*else*/ if (toolName.StartsWith("停止循环"))
                {
                    Logger.AddLog($"当前[{toolName}]没有对应的UI界面！", MsgType.Warn, isDispGrowl: true);
                    return;
                }
                else if (toolName.StartsWith("文件夹"))
                {
                    Logger.AddLog($"当前[{toolName}]没有对应的UI界面！", MsgType.Warn, isDispGrowl: true);
                    return;
                }
                //else if (toolName.StartsWith("并行块"))
                //{
                //    Logger.AddLog($"当前[{toolName}]没有对应的UI界面！", MsgType.Warn, isDispGrowl: true);
                //    return;
                //}

                ToolUnit toolObj = vm.Project.GetToolByName(vm.Project.ToolList.ToList(), toolName);
                if (toolObj == null) return;
                //获取对应的ToolViewBase
                //ToolViewBase toolFormBase = (ToolViewBase)PluginService.PluginDic_Tool[toolObj.ToolParam.PluginName].ToolType.Assembly.CreateInstance(PluginService.PluginDic_Tool[toolObj.ToolParam.PluginName].ToolViewType.FullName);

                if (toolObj.ToolView == null)
                {
                    ToolViewBase toolFormBase = (ToolViewBase)Activator.CreateInstance(PluginService.PluginDic_Tool[toolObj.PluginName].ToolViewType);
                    toolObj.ToolView = toolFormBase;
                    toolFormBase.ToolUnit = toolObj;
                }
                toolObj.ToolView.ShowDialog(); 
                }
#endif
        }
        #endregion

        #region 鼠标右键模块方法
        private void miDisable_Click(object sender, RoutedEventArgs e)
        {
            SelectedTool = toolTree.SelectedItem as ToolUnit;
            if (SelectedTool == null) return;

            SelectedTool.IsUse = false;
        }

        private void miCopy_Click(object sender, RoutedEventArgs e)
        {
            if (IsMultiSelectedModel(vm.Project.ToolList.ToList()))
            {
                List<ToolUnit> tools = new List<ToolUnit>();
                vm.Project.GetTools(vm.Project.ToolList.ToList(), ref tools);

                int startIndex = tools.FindIndex(c => c.Name == MultiSelectedStart);
                int endIndex = tools.FindIndex(c => c.Name == MultiSelectedEnd);
                List<ToolUnit> toolUnits = tools.Skip(startIndex).Take(endIndex - startIndex + 1).ToList();

                MemoryStream stream = SerializeHelp.BinSerializeAndSaveStream(toolUnits);
                Clipboard.SetData(DataFormats.Serializable, stream);
                stream?.Close();
            }
            else
            {
                SelectedTool = toolTree.SelectedItem as ToolUnit;
                if (SelectedTool == null) return;

                MemoryStream stream = SerializeHelp.BinSerializeAndSaveStream(SelectedTool);
                Clipboard.SetData(DataFormats.Serializable, stream);
                stream?.Close();
            }

        }

        /// <summary>
        /// 更换节点的名称
        /// </summary>
        private void UpdateToolName(List<ToolUnit> tools, ref Dictionary<string, int> keys)
        {
            for (int i = 0; i < tools.Count; i++)
            {
                int cnt = GetToolNameCount(vm.Project.ToolList.ToList(), tools[i].PluginName);
                if (!keys.ContainsKey(tools[i].PluginName))
                {
                    keys.Add(tools[i].PluginName, 0);
                }
                else
                {
                    keys[tools[i].PluginName]++;
                }
                cnt = cnt + keys[tools[i].PluginName];
                tools[i].Name = tools[i].PluginName + ((cnt != 0) ? cnt.ToString() : "");
                if (tools[i].ToolList.Count > 0)
                {
                    UpdateToolName(tools[i].ToolList.ToList(), ref keys);
                }
            }
        }

        /// <summary>
        /// 插入多选节点到指定链表中
        /// </summary>
        /// <param name="relativeToolName">把多选节点插入其后的节点的名称</param>
        /// <param name="tools">多选节点</param>
        /// <param name="projectToolList">指定链表</param>
        /// <param name="parent">指定链表对应的父节点</param>
        private void InsertMultiToolsInToolList(string relativeToolName, List<ToolUnit> tools, ObservableCollection<ToolUnit> projectToolList, ToolUnit parent = null)
        {
            foreach (var tool in tools)
            {
                //tool.SolutiontID = Solution.Ins.SolutionID;
                tool.ProjectID = vm.Project.ProjectInfo.ProjectID;
                tool.Prj = vm.Project;

                if (tool.Parent == null)
                {
                    int index = projectToolList.FindIndex(c => c.Name == relativeToolName);
                    projectToolList.Insert(index + 1, tool);
                    tool.Parent = parent;
                    relativeToolName = tool.Name;
                }
                else
                {
                    if (!IsExistInList(tools, tool.Parent.Name)) // 如果父节点不在多选选中的节点中，此情况需要更换父节点
                    {
                        int index = projectToolList.FindIndex(c => c.Name == relativeToolName);
                        tool.Parent = parent;
                        projectToolList.Insert(index + 1, tool);
                        relativeToolName = tool.Name;
                    }
                }
            }
        }

        private void miPaste_Click(object sender, RoutedEventArgs e)
        {
            using (MemoryStream stream = (MemoryStream)Clipboard.GetData(DataFormats.Serializable))
            {
                if (SerializeHelp.BinDeserialize(stream) is ToolUnit toolUnit)
                {
                    if (toolUnit == null) return;

                    SelectedTool = toolTree.SelectedItem as ToolUnit;
                    var projectToolList = vm.Project.ToolList;

                    if (SelectedTool != null && projectToolList.Count > 0)
                    {
                        var keys = new Dictionary<string, int> { { toolUnit.PluginName, 0 } };
                        UpdateToolName(toolUnit.ToolList.ToList(), ref keys);
                        AddTool(SelectedTool.Name, toolUnit, true);
                    }
                    else if (projectToolList.Count == 0)
                    {
                        AddTool("", toolUnit, true);
                    }
                    else
                    {
                        AddTool(projectToolList.Last().Name, toolUnit, true);
                    }
                }
                else if (SerializeHelp.BinDeserialize(stream) is List<ToolUnit> tools)
                {
                    if (tools == null || tools.Count == 0) return;
                    SelectedTool = toolTree.SelectedItem as ToolUnit;

                    // 取消多选
                    if (IsMultiSelectedModel(vm.Project.ToolList.ToList()))
                    {
                        CancelMultiSelect();
                    }

                    var keys = new Dictionary<string, int>();
                    foreach (var tool in tools)
                    {
                        int cnt = GetToolNameCount(vm.Project.ToolList.ToList(), tool.PluginName);
                        if (!keys.ContainsKey(tool.PluginName))
                        {
                            keys.Add(tool.PluginName, 0);
                        }
                        else
                        {
                            keys[tool.PluginName]++;
                        }
                        cnt = cnt + keys[tool.PluginName];
                        tool.Name = tool.PluginName + ((cnt != 0) ? cnt.ToString() : "");
                    }

                    if (SelectedTool != null) //存在
                    {
                        if (SelectedTool.Parent != null)
                        {
                            //插入指定节点后
                            InsertMultiToolsInToolList(SelectedTool.Name, tools, SelectedTool.Parent.ToolList, SelectedTool.Parent);
                        }
                        else
                        {
                            InsertMultiToolsInToolList(SelectedTool.Name, tools, vm.Project.ToolList);
                        }
                    }
                    else if (vm.Project.ToolList.Count == 0)
                    {
                        InsertMultiToolsInToolList("", tools, vm.Project.ToolList);
                    }
                    else
                    {
                        InsertMultiToolsInToolList(vm.Project.ToolList.Last().Name, tools, vm.Project.ToolList);
                    }

                    // 序号排序   是否需要？？？？
                    int indexTool = 1;
                    SortTools(vm.Project.ToolList.ToList(), ref indexTool);
                }
            }
        }

        private void miCut_Click(object sender, RoutedEventArgs e)
        {
            //SelectedTool = toolTree.SelectedItem as ToolUnit;
            //if (SelectedTool == null) return;

            //MemoryStream stream = SerializeHelp.BinSerializeAndSaveStream(SelectedTool);
            //Clipboard.SetData(DataFormats.Serializable, stream);
            //stream?.Close();
        }

        private void miRename_Click(object sender, RoutedEventArgs e)
        {
            if (IsMultiSelectedModel(vm.Project.ToolList.ToList()))
            {
                MessageBoxView.Ins.MessageBoxShow("多选模式下不可重命名！", MsgType.Warn);
                return;
            }
            SelectedTool = toolTree.SelectedItem as ToolUnit;
            if (SelectedTool == null)
            {
                return;
            }

            string toolName = SelectedTool.Name;
            string name = EditRemarksView.Ins.MessageBoxShow(toolName);
            if (EditRemarksView.Ins.DialogResult == true)
            {
                ToolUnit tool1 = GetTool(vm.Project.ToolList.ToList(), name);
                if (tool1 != null)
                {
                    MessageBoxView.Ins.MessageBoxShow("名称重复！", MsgType.Warn);
                    return;
                }
                SelectedTool.Name = name;

                vm.Project.ToolList_CollectionChanged(null, null);
                //RecursionChild(TreeSoureList, toolName, eToolTreeOperateType.ModifyRemarks, name);
                //toolTree.ItemsSource = TreeSoureList.ToList();
            }
        }

        private void miExcuteSelectedTool_Click(object sender, RoutedEventArgs e)
        {
            SelectedTool = toolTree.SelectedItem as ToolUnit;
            if (SelectedTool == null) return;

            vm.Project.ExcuteOneToolByName(SelectedTool.Name);
        }

        private void miExcuteMultiTool_Click(object sender, RoutedEventArgs e)
        {
            SelectedTool = toolTree.SelectedItem as ToolUnit;
            if (SelectedTool == null) return;

            vm.Project.ExcuteMultiToolByName(SelectedTool.Name);
        }

        private void miEditRemarks_Click(object sender, RoutedEventArgs e)
        {
            if (IsMultiSelectedModel(vm.Project.ToolList.ToList()))
            {
                MessageBoxView.Ins.MessageBoxShow("多选模式下不可设置注释！", MsgType.Warn);
                return;
            }

            SelectedTool = toolTree.SelectedItem as ToolUnit;
            if (SelectedTool == null)
            {
                return;
            }
            EditRemarksView editRemarks = EditRemarksView.Ins;
            string toolRemarks = SelectedTool.Remarks;
            string remarks = editRemarks.MessageBoxShow(toolRemarks);
            if (editRemarks.DialogResult == true)
            {
                SelectedTool.Remarks = remarks;
                //RecursionChild(TreeSoureList, toolName, eToolTreeOperateType.ModifyRemarks, remarks);
                //toolTree.ItemsSource = TreeSoureList.ToList();
            }
        }

        private void miDeleteTool_Click(object sender, RoutedEventArgs e)
        {

            if (IsMultiSelectedModel(vm.Project.ToolList.ToList()) == true)
            {
                foreach (var name in SelectedToolNameList)
                {
                    ToolUnit tool = vm.Project.GetToolByName(vm.Project.ToolList.ToList(), name);
                    if (tool == null || !tool.IsMultiSelected) continue;

                    // 如果有子节点，遍历删除子节点
                    if (tool.AllowHaveChild)
                    {
                        for (int index = 0; index < tool.ToolList.Count; index++)
                        {
                            tool.ToolList.RemoveAt(index);
                        }
                        // 如果有父节点，从父节点删除该节点
                        if (tool.Parent != null)
                        {
                            tool.Parent.ToolList.Remove(tool);
                        }
                        else
                        {
                            vm.Project.ToolList.Remove(tool);
                        }
                    }
                    else
                    {
                        // 如果有父节点，从父节点删除该节点
                        if (tool.Parent != null)
                        {
                            tool.Parent.ToolList.Remove(tool);
                        }
                        else
                        {
                            vm.Project.ToolList.Remove(tool);
                        }
                    }

                } // foreach

                SelectedToolNameList.Clear();
                MultiSelectedCount = 0;
            }
            else
            {
                SelectedTool = toolTree.SelectedItem as ToolUnit;
                if (SelectedTool == null) return;

                // 如果有子节点，遍历删除子节点
                if (SelectedTool.AllowHaveChild)
                {
                    for (int i = 0; i < SelectedTool.ToolList.Count; i++)
                    {
                        SelectedTool.ToolList.RemoveAt(i);
                    }
                    // 如果有父节点，从父节点删除该节点
                    if (SelectedTool.Parent != null)
                    {
                        SelectedTool.Parent.ToolList.Remove(SelectedTool);
                    }
                    else
                    {
                        vm.Project.ToolList.Remove(SelectedTool);
                    }
                }
                else
                {
                    // 如果有父节点，从父节点删除该节点
                    if (SelectedTool.Parent != null)
                    {
                        SelectedTool.Parent.ToolList.Remove(SelectedTool);
                    }
                    else
                    {
                        vm.Project.ToolList.Remove(SelectedTool);
                    }
                }

            }
            int indexTool = 1;
            SortTools(vm.Project.ToolList.ToList(), ref indexTool);
            //UpdateTree();
            //if (IsMultiSelectedModel() == true)
            //{
            //    List<int> removeIndex = new List<int>();
            //    for (int i = 0; i < toolTree.Items.Count; i++)
            //    {
            //        TreeViewItem treeItem = toolTree.ItemContainerGenerator.ContainerFromItem(toolTree.Items[i]) as TreeViewItem;
            //        if (treeItem != null)
            //        {
            //            TreeNode toolNode = treeItem.DataContext as TreeNode;
            //            if (toolNode == null || !toolNode.IsMultiSelected) continue;
            //            var toolBase = vm.Project.ToolList.Where(o => o.Name == toolNode.DisplayName).FirstOrDefault();
            //            if (toolBase.Name.StartsWith("如果"))
            //            {
            //                string endToolName = toolBase.Name.Replace("如果", "结束");
            //                int index = vm.Project.ToolList.IndexOf(toolBase);
            //                while (vm.Project.ToolList[index].Name != endToolName)
            //                {
            //                    removeIndex.Add(index);
            //                    index++;
            //                    i++;
            //                }
            //                removeIndex.Add(i);
            //            }
            //            else if (toolBase.Name.StartsWith("结束"))
            //            {
            //                continue;
            //            }
            //            else if (toolBase.Name.StartsWith("循环开始"))
            //            {
            //                string endToolName = toolBase.Name.Replace("循环开始", "循环结束");
            //                int index = vm.Project.ToolList.IndexOf(toolBase);
            //                while (vm.Project.ToolList[index].Name != endToolName)
            //                {
            //                    removeIndex.Add(index);
            //                    index++;
            //                    i++;
            //                }
            //                removeIndex.Add(i);
            //            }
            //            else if (toolBase.Name.StartsWith("循环结束"))
            //            {
            //                continue;
            //            }
            //            else if (toolBase.Name.StartsWith("并行块开始"))
            //            {
            //                string endToolName = toolBase.Name.Replace("并行块开始", "并行块结束");
            //                int index = vm.Project.ToolList.IndexOf(toolBase);
            //                while (vm.Project.ToolList[index].Name != endToolName)
            //                {
            //                    removeIndex.Add(index);
            //                    index++;
            //                    i++;
            //                }
            //                removeIndex.Add(i);
            //            }
            //            else if (toolBase.Name.StartsWith("并行块结束"))
            //            {
            //                continue;
            //            }
            //            else if (toolBase.Name.StartsWith("坐标补正开始"))
            //            {
            //                string endToolName = toolBase.Name.Replace("坐标补正开始", "坐标补正结束");
            //                int index = vm.Project.ToolList.IndexOf(toolBase);
            //                while (vm.Project.ToolList[index].Name != endToolName)
            //                {
            //                    removeIndex.Add(index);
            //                    index++;
            //                    i++;
            //                }
            //                removeIndex.Add(i);
            //            }
            //            else if (toolBase.Name.StartsWith("坐标补正结束"))
            //            {
            //                continue;
            //            }

            //            else
            //            {
            //                removeIndex.Add(i);
            //            }
            //        }
            //    }
            //    List<ToolUnit> removeItems = new List<ToolUnit>();
            //    foreach (var index in removeIndex)
            //    {
            //        removeItems.Add(vm.Project.ToolList[index]);
            //    }
            //    foreach (var item in removeItems)
            //    {
            //        vm.Project.ToolList.Remove(item);
            //    }
            //}
            //else
            //{
            //    if (toolTree.SelectedItem == null) return;
            //    var selectedTool = toolTree.SelectedItem as ToolUnit;
            //    if (selectedTool == null) return;
            //    var item = vm.Project.ToolList.Where(o => o.Name == selectedTool.Name).FirstOrDefault();
            //    if (item.Name.StartsWith("如果"))
            //    {
            //        string endToolName = item.Name.Replace("如果", "结束");
            //        int index = vm.Project.ToolList.IndexOf(item);
            //        while (vm.Project.ToolList[index].Name != endToolName)
            //        {
            //            vm.Project.ToolList.RemoveAt(index);
            //        }
            //        vm.Project.ToolList.RemoveAt(index);
            //    }
            //    else if (item.Name.StartsWith("结束"))
            //    {
            //        return;
            //    }
            //    else if (item.Name.StartsWith("循环开始"))
            //    {
            //        string endToolName = item.Name.Replace("循环开始", "循环结束");
            //        int index = vm.Project.ToolList.IndexOf(item);
            //        while (vm.Project.ToolList[index].Name != endToolName)
            //        {
            //            vm.Project.ToolList.RemoveAt(index);
            //        }
            //        vm.Project.ToolList.RemoveAt(index);
            //    }
            //    else if (item.Name.StartsWith("循环结束"))
            //    {
            //        return;
            //    }
            //    else if (item.Name.StartsWith("并行块开始"))
            //    {
            //        string endToolName = item.Name.Replace("并行块开始", "并行块结束");
            //        int index = vm.Project.ToolList.IndexOf(item);
            //        while (vm.Project.ToolList[index].Name != endToolName)
            //        {
            //            vm.Project.ToolList.RemoveAt(index);
            //        }
            //        vm.Project.ToolList.RemoveAt(index);
            //    }
            //    else if (item.Name.StartsWith("并行块结束"))
            //    {
            //        return;
            //    }
            //    else if (item.Name.StartsWith("坐标补正开始"))
            //    {
            //        string endToolName = item.Name.Replace("坐标补正开始", "坐标补正结束");
            //        int index = vm.Project.ToolList.IndexOf(item);
            //        while (vm.Project.ToolList[index].Name != endToolName)
            //        {
            //            vm.Project.ToolList.RemoveAt(index);
            //        }
            //        vm.Project.ToolList.RemoveAt(index);
            //    }
            //    else if (item.Name.StartsWith("坐标补正结束"))
            //    {
            //        return;
            //    }

            //    else
            //    {
            //        vm.Project.ToolList.Remove(item);
            //    }

            //}
            //UpdateTree();
        }

        private void miEnableBreakPoint_Click(object sender, RoutedEventArgs e)
        {
            SelectedTool = toolTree.SelectedItem as ToolUnit;
            if (SelectedTool == null) return;

            SelectedTool.IsEnableBreakPoint = !SelectedTool.IsEnableBreakPoint;
            //RecursionChild(TreeSoureList, toolName, eToolTreeOperateType.ModifyBreakPoint, null);
            //toolTree.ItemsSource = TreeSoureList.ToList();
        }
        public enum eToolTreeOperateType
        {
            // 修改备注
            ModifyRemarks,
            // 使能断点
            ModifyBreakPoint,
        }
        /// <summary>
        /// 递归
        /// </summary>
        /// <param name="children"></param>
        /// <param name="toolName"></param>
        /// <param name="operateType"></param>
        /// <param name="remarks"></param>
        //private void RecursionChild(List<TreeNode> children, string toolName, eToolTreeOperateType operateType, string remarks)
        //{
        //    if (children.Count > 0)
        //    {
        //        foreach (var item in children)
        //        {
        //            if (item.DisplayName == toolName)
        //            {
        //                switch (operateType)
        //                {
        //                    case eToolTreeOperateType.ModifyRemarks:
        //                        item.Remarks = remarks;
        //                        return;
        //                    case eToolTreeOperateType.ModifyBreakPoint:
        //                        item.IsEnableBreakPoint = !item.IsEnableBreakPoint;
        //                        return;
        //                    default:
        //                        break;
        //                }
        //            }
        //            //RecursionChild(item.Children, toolName, operateType, remarks);
        //        }
        //    }
        //}

        public void SetTreeSourceNull()
        {
            toolTree.ItemsSource = null;
        }

        public void SetIsContinueRuning(bool isContinueRuning)
        {
            //if (isContinueRuning)
            //{
            //    btnRunOnce.Content = "\ue8f0";
            //    btnRunCycle.Content = "\ue6ef";
            //    btnRunOnce.ToolTip = "继续运行(F5)";
            //    btnRunCycle.ToolTip = "逐过程,一步一步运行(F6)";
            //    ck_IsRunning.IsChecked = true;
            //}
            //else
            //{
            //    btnRunOnce.Content = "\ue67b";
            //    btnRunCycle.Content = "\ue612";
            //    btnRunOnce.ToolTip = "当前项目单次执行";
            //    btnRunCycle.ToolTip = "当前项目连续执行";
            //    ck_IsRunning.IsChecked = false;
            //}
        }


        #endregion

        private void tv_SelectedItemChanged(object sender, EventArgs e)
        {

        }

        private void DockPanel_MouseDown(object sender, MouseButtonEventArgs e)
        {

        }

        //private void toolTree_PreviewSelectionChanged(object sender, PreviewSelectionChangedEventArgs e)
        //{
        //    //(e.Item as ToolUnit).TreeNode.IsSelected = false;
        //}


        private void toolTree_DragLeave_1(object sender, DragEventArgs e)
        {

        }


        bool bIsChild = false;
        private void Image_MouseEnter(object sender, MouseEventArgs e)
        {
            //bIsChild = true;
        }

        private void Image_MouseLeave(object sender, MouseEventArgs e)
        {
            bIsChild = false;
        }

        private void Image_PreviewDragOver(object sender, DragEventArgs e)
        {
            bIsChild = true;

        }

        private void Tool_PreviewDragOver(object sender, DragEventArgs e)
        {
            bIsChild = false;

        }

        private void toolTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (toolTree.SelectedItem == null)
            {
                IoC.Get<PropertyPanelViewModel>().SelectedTool = null;
                return;
            }
            var selected = toolTree.SelectedItem as ToolUnit;
            List<ToolUnit> list = new List<ToolUnit>();
            vm.Project.GetTools(vm.Project.ToolList.ToList(), ref list);
            foreach (var item in list)
            {
                item.NeedRender = false;
            }
            selected.NeedRender = true;
            IoC.Get<PropertyPanelViewModel>().SelectedTool = toolTree.SelectedItem as ToolUnit;
        }


        #region 未使用的
        public void UpdateStatus(ToolUnit tool)
        {
            if (TreeSoureList == null || TreeSoureList.Count == 0) return; // bug
            findNote = null;
            GetTreeNode(toolTree, tool);
            if (findNote == null) return;
            findNote.CostTime = tool.ConsumingTime.ToString();
            switch (tool.StatusColor)
            {
                case RunStatus.OK:
                    findNote.StatusImage = "\xe62e";
                    findNote.StatusColor = Brushes.Lime;
                    findNote.IsRunning = false;
                    break;
                case RunStatus.NG:
                    findNote.StatusImage = "\xe633";
                    findNote.StatusColor = Brushes.Red;
                    findNote.IsRunning = false;
                    break;
                case RunStatus.None:
                    findNote.StatusImage = null;
                    findNote.StatusColor = Brushes.Transparent;
                    findNote.IsRunning = false;
                    break;
                case RunStatus.Running:
                    findNote.StatusImage = null;
                    findNote.StatusColor = Brushes.Transparent;
                    findNote.IsRunning = true;
                    break;
                default:
                    break;
            }
        }

        private void GetStartEndToolNameByElse(int projectID, string toolName, out string startName, out string endName)
        {
            startName = "";
            endName = "";
            var project = Solution.Ins.GetProjectById(projectID);
            int index = project.ToolList.FindIndex(c => c.Name == toolName);
            Stack<ToolUnit> s_ItemStack = new Stack<ToolUnit>();
            for (int i = index; i < project.ToolList.Count; i++)
            {
                if (project.ToolList[i].Name.StartsWith("结束") && s_ItemStack.Count == 0)
                {
                    endName = project.ToolList[i].Name;
                    break;
                }
                else if (project.ToolList[i].Name.StartsWith("结束"))
                {
                    if (s_ItemStack.Count > 0)
                    {
                        s_ItemStack.Pop();
                    }
                }
                else if (project.ToolList[i].Name.StartsWith("如果"))
                {
                    s_ItemStack.Push(project.ToolList[i]);
                }
            }
            s_ItemStack = new Stack<ToolUnit>();
            for (int i = index; i < project.ToolList.Count; i--)
            {
                if (project.ToolList[i].Name.StartsWith("如果") && s_ItemStack.Count == 0)
                {
                    startName = project.ToolList[i].Name;
                    break;
                }
                else if (project.ToolList[i].Name.StartsWith("如果"))
                {
                    if (s_ItemStack.Count > 0)
                    {
                        s_ItemStack.Pop();
                    }
                }
                else if (project.ToolList[i].Name.StartsWith("结束"))
                {
                    s_ItemStack.Push(project.ToolList[i]);

                }
            }
        }




        /// <summary>
        /// 选中指定名字的node
        /// </summary>
        /// <param name="nodes"></param>
        public void SelectNode(string name)
        {
            //foreach (TreeNode item in ToolNodeList)
            //{
            //    if (item.ToolInfo.Name == name)
            //    {
            //        item.IsSelected = true;
            //        SelectedNode = item;
            //    }
            //    else
            //    {
            //        item.IsSelected = false;
            //    }
            //}
            //toolTree.ItemsSource = TreeSoureList.ToList();
        }



        #endregion


    }
}
