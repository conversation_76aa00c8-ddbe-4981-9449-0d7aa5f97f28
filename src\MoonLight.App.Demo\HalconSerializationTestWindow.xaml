<Window x:Class="MoonLight.App.Demo.HalconSerializationTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Halcon序列化测试" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="Halcon对象序列化/反序列化测试" 
                   FontSize="16" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="10"/>
        
        <!-- 主要内容区域 -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- HTuple测试 -->
            <TabItem Header="HTuple测试">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button Name="CreateHTupleBtn" Content="创建HTuple" Width="100" Margin="5" Click="CreateHTuple_Click"/>
                        <Button Name="SerializeHTupleBtn" Content="序列化HTuple" Width="100" Margin="5" Click="SerializeHTuple_Click"/>
                        <Button Name="DeserializeHTupleBtn" Content="反序列化HTuple" Width="100" Margin="5" Click="DeserializeHTuple_Click"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" Text="HTuple信息:" FontWeight="Bold" Margin="5"/>
                    <TextBox Grid.Row="2" Name="HTupleInfoTextBox" Height="60" Margin="5" IsReadOnly="True" TextWrapping="Wrap"/>
                    
                    <TextBlock Grid.Row="3" Text="序列化结果:" FontWeight="Bold" Margin="5,10,5,5"/>
                    <ScrollViewer Grid.Row="3" Margin="5,30,5,5">
                        <TextBox Name="HTupleJsonTextBox" TextWrapping="Wrap" AcceptsReturn="True" IsReadOnly="True"/>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- HRegion测试 -->
            <TabItem Header="HRegion测试">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button Name="CreateHRegionBtn" Content="创建HRegion" Width="100" Margin="5" Click="CreateHRegion_Click"/>
                        <Button Name="SerializeHRegionBtn" Content="序列化HRegion" Width="100" Margin="5" Click="SerializeHRegion_Click"/>
                        <Button Name="DeserializeHRegionBtn" Content="反序列化HRegion" Width="100" Margin="5" Click="DeserializeHRegion_Click"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" Text="HRegion信息:" FontWeight="Bold" Margin="5"/>
                    <TextBox Grid.Row="2" Name="HRegionInfoTextBox" Height="60" Margin="5" IsReadOnly="True" TextWrapping="Wrap"/>
                    
                    <TextBlock Grid.Row="3" Text="序列化结果:" FontWeight="Bold" Margin="5,10,5,5"/>
                    <ScrollViewer Grid.Row="3" Margin="5,30,5,5">
                        <TextBox Name="HRegionJsonTextBox" TextWrapping="Wrap" AcceptsReturn="True" IsReadOnly="True"/>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- HImage测试 -->
            <TabItem Header="HImage测试">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button Name="CreateHImageBtn" Content="创建HImage" Width="100" Margin="5" Click="CreateHImage_Click"/>
                        <Button Name="SerializeHImageBtn" Content="序列化HImage" Width="100" Margin="5" Click="SerializeHImage_Click"/>
                        <Button Name="DeserializeHImageBtn" Content="反序列化HImage" Width="100" Margin="5" Click="DeserializeHImage_Click"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" Text="HImage信息:" FontWeight="Bold" Margin="5"/>
                    <TextBox Grid.Row="2" Name="HImageInfoTextBox" Height="60" Margin="5" IsReadOnly="True" TextWrapping="Wrap"/>
                    
                    <TextBlock Grid.Row="3" Text="序列化结果:" FontWeight="Bold" Margin="5,10,5,5"/>
                    <ScrollViewer Grid.Row="3" Margin="5,30,5,5">
                        <TextBox Name="HImageJsonTextBox" TextWrapping="Wrap" AcceptsReturn="True" IsReadOnly="True"/>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- 批量测试 -->
            <TabItem Header="批量测试">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button Name="BatchTestBtn" Content="批量测试" Width="100" Margin="5" Click="BatchTest_Click"/>
                        <Button Name="PerformanceTestBtn" Content="性能测试" Width="100" Margin="5" Click="PerformanceTest_Click"/>
                        <Button Name="SaveToFileBtn" Content="保存到文件" Width="100" Margin="5" Click="SaveToFile_Click"/>
                        <Button Name="LoadFromFileBtn" Content="从文件加载" Width="100" Margin="5" Click="LoadFromFile_Click"/>
                    </StackPanel>
                    
                    <TextBlock Grid.Row="1" Text="测试结果:" FontWeight="Bold" Margin="5"/>
                    <ScrollViewer Grid.Row="2" Margin="5">
                        <TextBox Name="BatchTestResultTextBox" TextWrapping="Wrap" AcceptsReturn="True" IsReadOnly="True"/>
                    </ScrollViewer>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="StatusTextBlock" Text="就绪"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
