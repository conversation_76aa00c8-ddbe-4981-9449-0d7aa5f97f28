﻿using MoonLight.Modules.FlowManager.ViewModels;
using MoonLight.UI.Framework.Commands;
using MoonLight.UI.Framework.Services;
using MoonLight.UI.Framework.Threading;
using System.ComponentModel.Composition;
using System.Threading.Tasks;

namespace MoonLight.Modules.FlowManager.Commands
{
    [CommandHandler]
    public class ViewPropertyPanelCommandHandler : CommandHandlerBase<ViewPropertyPanelCommandDefinition>
    {
        private readonly IShell _shell;

        [ImportingConstructor]
        public ViewPropertyPanelCommandHandler(IShell shell)
        {
            _shell = shell;
        }

        public override Task Run(Command command)
        {
            _shell.ShowTool(IoC.Get<PropertyPanelViewModel>());
            return TaskUtility.Completed;
        }
    }
}
