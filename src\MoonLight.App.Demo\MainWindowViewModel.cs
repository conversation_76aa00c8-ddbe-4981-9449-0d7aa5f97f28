﻿using Microsoft.Win32;
using MoonLight.Core.Assets.Modules.RenderControl.Views;
using MoonLight.Core.Common.Helper;
using MoonLight.Core.Common.Log;
using MoonLight.Core.Enums;
using MoonLight.Core.Interfaces;
using MoonLight.Core.Models;
using MoonLight.Core.Services;
using MoonLight.UI.Framework;
using MoonLight.UI.Framework.Services;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Forms.Integration;

namespace MoonLight.App.Demo
{
    [Export]
    [PartCreationPolicy(CreationPolicy.Shared)]
    internal class MainWindowViewModel : WindowBase
    {
        IEngineService _engineService;

        [ImportingConstructor]
        public MainWindowViewModel(IEngineService engineService)
        {
            _engineService = engineService;
            Solution.SolutionChangedEvent += _engineService_SolutionEventChanged;
        }

        private void _engineService_SolutionEventChanged(object sender, EventArgs e)
        {
            NotifyOfPropertyChange(nameof(ListProjectName));
            NotifyOfPropertyChange(CurPath);

        }

        public string CurPath
        {
            get
            {
                return _engineService.CurrentSolution.CurrentSolName;
            }
        }

        public List<string> ListProjectName
        {
            get
            {
                var names = _engineService.GetAllProjectsName();
                if (string.IsNullOrEmpty(SelectedProjectName) && names.Count > 0)
                {
                    SelectedProjectName = names[0];
                }
                return names;
            }
        }



        //列举需要显示的参数窗口
        public List<string> ListToolName
        {
            get
            {
                return new List<string>()
                {
                    "模板匹配",
                    "模板匹配1",
                    "拟合直线",
                };
            }
        }

        private string _selectedToolName = "";
        public string SelectedToolName
        {
            get { return _selectedToolName; }
            set
            {
                Set(ref _selectedToolName, value);
                PropertyControl = PluginService.PluginDic_ToolPropertyView[_selectedToolName];// _engineService.GetProjectByIndex(0)?.GetToolByName(_selectedToolName)?.GetUserControl();
                _engineService.SetRenderTool(SelectedProjectName, _selectedToolName);
                if (PropertyControl == null) return;
                //_engineService.CurrentSolution.SelectedProject = _engineService.GetProjectByName(SelectedProjectName);

                //var selectedTool = _engineService.GetProjectByName(SelectedProjectName).GetToolByName(_selectedToolName);

                //PropertyControl.DataContext = selectedTool;
                //selectedTool.Loaded(IoC.Get<IRenderViewManager>().GetView(selectedTool.Prj.DispViewID), PropertyControl);
            }

        }

        private string _selectedProjectName = "";
        public string SelectedProjectName
        {
            get { return _selectedProjectName; }
            set
            {
                Set(ref _selectedProjectName, value);
            }

        }

        private UserControl _PropertyControl;
        public UserControl PropertyControl
        {
            get { return _PropertyControl; }
            set { Set(ref _PropertyControl, value); }
        }


        public IRenderView RenderView { get; set; }



        private string _SelectedSolPath;
        public string SelectedSolPath
        {
            get { return _SelectedSolPath; }
            set { Set(ref _SelectedSolPath, value); }
        }

        private bool _IsEnableSelectedSol = false;
        public bool IsEnableSelectedSol
        {
            get { return _IsEnableSelectedSol; }
            set { Set(ref _IsEnableSelectedSol, value); }
        }

        

        private double _CrossX;
        public double CrossX
        {
            get { return _CrossX; }
            set { Set(ref _CrossX, value); }
        }

        private double _CrossY;
        public double CrossY
        {
            get { return _CrossY; }
            set { Set(ref _CrossY, value); }
        }

        private bool _status;
        public bool Status
        {
            get { return _status; }
            set { Set(ref _status, value); }
        }

        private IRenderView _VisionControl = new RenderViewWpf();
        public IRenderView VisionControl
        {
            get { return _VisionControl; }
            set { Set(ref _VisionControl, value); }
        }
 


        [NonSerialized]
        private CommandBase _LoadedCommand;
        public CommandBase LoadedCommand
        {
            get
            {
                if (_LoadedCommand == null)
                {
                    _LoadedCommand = new CommandBase((obj) =>
                    {
                        if (RenderView == null)
                        {
                            //VisionControl = IoC.Get<IRenderViewManager>().GetView(0);
                            //VisionControl = RenderView;
                            //VisionControl.Child = (System.Windows.Forms.Control)RenderView ;
                        }
                    });
                }
                return _LoadedCommand;
            }
        }



 


        [NonSerialized]
        private CommandBase _LoadPathCommand;
        public CommandBase LoadPathCommand
        {
            get
            {
                if (_LoadPathCommand == null)
                {
                    _LoadPathCommand = new CommandBase((obj) =>
                    {

                        OpenFileDialog openFileDialog = new OpenFileDialog();
                        openFileDialog.Filter = "解决方案 (*.vm)|*.vm";
                        openFileDialog.DefaultExt = "vm";
                        if (openFileDialog.ShowDialog() == true)
                        {
                            try
                            {
                                IoC.Get<IEngineService>().LoadSolution(openFileDialog.FileName);
                                SelectedSolPath = openFileDialog.FileName;
                                IsEnableSelectedSol = true;
                            }
                            catch (Exception ex)
                            {
                                Logger.AddLog($"解决方案加载失败！{ex.Message}", MsgType.Error);
                            }
                        }
                    });
                }
                return _LoadPathCommand;
            }
        }

        [NonSerialized]
        private CommandBase _SaveSolCommand;
        public CommandBase SaveSolCommand
        {
            get
            {
                if (_SaveSolCommand == null)
                {
                    _SaveSolCommand = new CommandBase( (obj) =>
                    {
                        try
                        {
                            bool ret= IoC.Get<IEngineService>().SaveSolution(true);
                       
                            if (ret)
                            {
                                Logger.AddLog("解决方案保存成功！", MsgType.Success, isDispGrowl: true);
                            }
                            else
                            {
                                Logger.AddLog("解决方案保存失败！", MsgType.Error);
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"解决方案保存失败！{ex.Message}", MsgType.Error);
                        }
                    });
                }
                return _SaveSolCommand;
            }
        }

      
        [NonSerialized]
        private CommandBase _RunOnceCommand;
        public CommandBase RunOnceCommand
        {
            get
            {
                if (_RunOnceCommand == null)
                {
                    _RunOnceCommand = new CommandBase((obj) =>
                    {
                        try
                        {
                            _engineService.SetRenderTool(SelectedProjectName, "渲染显示");
                            _engineService.RunProject(SelectedProjectName, RunMode.RunOnce, callback);
                        }
                        catch (Exception ex)
                        {
                            Logger.AddLog($"执行一次解决方案失败,{ex.Message}");
                        }
                    });
                }
                return _RunOnceCommand;
            }
        }

        private void callback(object sender, EventArgs e)
        {
            string project_name = sender as string;
            Status = (bool)_engineService.GetProjectOutput(project_name, "模板匹配.状态");
            CrossX = (double)_engineService.GetProjectOutput(project_name, "模板匹配.X");
            CrossY = (double)_engineService.GetProjectOutput(project_name, "模板匹配.Y");
        }
    }
}
